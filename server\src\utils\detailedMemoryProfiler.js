/**
 * Detailed Memory Profiler for tracking object types and memory leaks
 * Specifically designed to identify what's causing memory issues in search operations
 */

const v8 = require('v8');
const fs = require('fs');
const path = require('path');

class DetailedMemoryProfiler {
    constructor() {
        this.profiles = [];
        this.isActive = false;
        this.startTime = null;
        this.operationName = null;
        this.heapSnapshots = [];
        this.objectCounts = [];
    }

    /**
     * Start profiling an operation
     */
    startProfiling(operationName) {
        this.operationName = operationName;
        this.isActive = true;
        this.startTime = Date.now();
        this.profiles = [];
        this.heapSnapshots = [];
        this.objectCounts = [];

        console.log(`🔬 [PROFILER] Starting detailed profiling for: ${operationName}`);
        
        this.takeSnapshot('start');
        this.recordObjectCounts('start');
        
        return this;
    }

    /**
     * Take a memory snapshot at a specific point
     */
    takeSnapshot(label) {
        if (!this.isActive) return;

        const memUsage = process.memoryUsage();
        const heapStats = v8.getHeapStatistics();
        
        const snapshot = {
            label,
            timestamp: Date.now(),
            memoryUsage: {
                rss: memUsage.rss,
                heapTotal: memUsage.heapTotal,
                heapUsed: memUsage.heapUsed,
                external: memUsage.external,
                arrayBuffers: memUsage.arrayBuffers
            },
            heapStats: {
                totalHeapSize: heapStats.total_heap_size,
                totalHeapSizeExecutable: heapStats.total_heap_size_executable,
                totalPhysicalSize: heapStats.total_physical_size,
                totalAvailableSize: heapStats.total_available_size,
                usedHeapSize: heapStats.used_heap_size,
                heapSizeLimit: heapStats.heap_size_limit,
                mallocedMemory: heapStats.malloced_memory,
                peakMallocedMemory: heapStats.peak_malloced_memory,
                doesZapGarbage: heapStats.does_zap_garbage,
                numberOfNativeContexts: heapStats.number_of_native_contexts,
                numberOfDetachedContexts: heapStats.number_of_detached_contexts
            }
        };

        this.heapSnapshots.push(snapshot);
        
        console.log(`📸 [PROFILER] Snapshot "${label}": ${(memUsage.heapUsed / 1024 / 1024).toFixed(1)}MB heap used`);
        
        return snapshot;
    }

    /**
     * Record object counts using heap space statistics
     */
    recordObjectCounts(label) {
        if (!this.isActive) return;

        try {
            const heapSpaces = v8.getHeapSpaceStatistics();
            const objectCount = {
                label,
                timestamp: Date.now(),
                heapSpaces: heapSpaces.map(space => ({
                    spaceName: space.space_name,
                    spaceSize: space.space_size,
                    spaceUsedSize: space.space_used_size,
                    spaceAvailableSize: space.space_available_size,
                    physicalSpaceSize: space.physical_space_size
                }))
            };

            this.objectCounts.push(objectCount);
            
            console.log(`🧮 [PROFILER] Object count "${label}" recorded`);
            
            return objectCount;
        } catch (error) {
            console.error(`❌ [PROFILER] Error recording object counts:`, error);
        }
    }

    /**
     * Force garbage collection and measure impact
     */
    forceGCAndMeasure(label) {
        if (!this.isActive) return;

        const beforeSnapshot = this.takeSnapshot(`${label}_before_gc`);
        
        if (global.gc) {
            console.log(`🗑️ [PROFILER] Forcing GC at "${label}"`);
            global.gc();
            
            const afterSnapshot = this.takeSnapshot(`${label}_after_gc`);
            
            const freed = beforeSnapshot.memoryUsage.heapUsed - afterSnapshot.memoryUsage.heapUsed;
            console.log(`🗑️ [PROFILER] GC freed ${(freed / 1024 / 1024).toFixed(1)}MB at "${label}"`);
            
            return { before: beforeSnapshot, after: afterSnapshot, freed };
        } else {
            console.warn(`⚠️ [PROFILER] GC not available (start with --expose-gc)`);
            return null;
        }
    }

    /**
     * Create a heap dump file
     */
    createHeapDump(label) {
        if (!this.isActive) return;

        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `heapdump-${this.operationName}-${label}-${timestamp}.heapsnapshot`;
            const filepath = path.join(process.cwd(), 'logs', filename);
            
            const logsDir = path.dirname(filepath);
            if (!fs.existsSync(logsDir)) {
                fs.mkdirSync(logsDir, { recursive: true });
            }

            console.log(`💾 [PROFILER] Creating heap dump: ${filename}`);
            
            const heapSnapshot = v8.getHeapSnapshot();
            const writeStream = fs.createWriteStream(filepath);
            
            heapSnapshot.pipe(writeStream);
            
            writeStream.on('finish', () => {
                console.log(`✅ [PROFILER] Heap dump saved: ${filepath}`);
            });

            writeStream.on('error', (error) => {
                console.error(`❌ [PROFILER] Error saving heap dump:`, error);
            });

            return filepath;
        } catch (error) {
            console.error(`❌ [PROFILER] Error creating heap dump:`, error);
        }
    }

    /**
     * Analyze memory differences between snapshots
     */
    analyzeMemoryDifference(startLabel, endLabel) {
        const startSnapshot = this.heapSnapshots.find(s => s.label === startLabel);
        const endSnapshot = this.heapSnapshots.find(s => s.label === endLabel);

        if (!startSnapshot || !endSnapshot) {
            console.error(`❌ [PROFILER] Cannot find snapshots for analysis: ${startLabel} -> ${endLabel}`);
            return null;
        }

        const analysis = {
            operation: `${startLabel} -> ${endLabel}`,
            duration: endSnapshot.timestamp - startSnapshot.timestamp,
            memoryDifference: {
                rss: endSnapshot.memoryUsage.rss - startSnapshot.memoryUsage.rss,
                heapTotal: endSnapshot.memoryUsage.heapTotal - startSnapshot.memoryUsage.heapTotal,
                heapUsed: endSnapshot.memoryUsage.heapUsed - startSnapshot.memoryUsage.heapUsed,
                external: endSnapshot.memoryUsage.external - startSnapshot.memoryUsage.external,
                arrayBuffers: endSnapshot.memoryUsage.arrayBuffers - startSnapshot.memoryUsage.arrayBuffers
            },
            heapStatsDifference: {
                usedHeapSize: endSnapshot.heapStats.usedHeapSize - startSnapshot.heapStats.usedHeapSize,
                totalHeapSize: endSnapshot.heapStats.totalHeapSize - startSnapshot.heapStats.totalHeapSize,
                mallocedMemory: endSnapshot.heapStats.mallocedMemory - startSnapshot.heapStats.mallocedMemory,
                numberOfNativeContexts: endSnapshot.heapStats.numberOfNativeContexts - startSnapshot.heapStats.numberOfNativeContexts,
                numberOfDetachedContexts: endSnapshot.heapStats.numberOfDetachedContexts - startSnapshot.heapStats.numberOfDetachedContexts
            }
        };

        console.log(`📊 [PROFILER] Memory Analysis: ${analysis.operation}`);
        console.log(`   Duration: ${analysis.duration}ms`);
        console.log(`   Heap Used: ${(analysis.memoryDifference.heapUsed / 1024 / 1024).toFixed(1)}MB`);
        console.log(`   RSS: ${(analysis.memoryDifference.rss / 1024 / 1024).toFixed(1)}MB`);
        console.log(`   External: ${(analysis.memoryDifference.external / 1024 / 1024).toFixed(1)}MB`);
        console.log(`   Native Contexts: ${analysis.heapStatsDifference.numberOfNativeContexts}`);
        console.log(`   Detached Contexts: ${analysis.heapStatsDifference.numberOfDetachedContexts}`);

        return analysis;
    }

    /**
     * Stop profiling and generate comprehensive report
     */
    stopProfiling() {
        if (!this.isActive) return;

        this.takeSnapshot('end');
        this.recordObjectCounts('end');

        const totalDuration = Date.now() - this.startTime;
        
        console.log(`🏁 [PROFILER] Stopping profiling for: ${this.operationName}`);
        console.log(`   Total Duration: ${totalDuration}ms`);
        console.log(`   Snapshots Taken: ${this.heapSnapshots.length}`);

        const overallAnalysis = this.analyzeMemoryDifference('start', 'end');

        const report = this.generateReport();

        this.isActive = false;
        return { overallAnalysis, report };
    }

    /**
     * Generate comprehensive profiling report
     */
    generateReport() {
        const report = {
            operationName: this.operationName,
            totalDuration: Date.now() - this.startTime,
            snapshots: this.heapSnapshots,
            objectCounts: this.objectCounts,
            memoryLeaks: this.detectPotentialLeaks(),
            recommendations: this.generateRecommendations()
        };

        this.saveReportToFile(report);

        return report;
    }

    /**
     * Detect potential memory leaks
     */
    detectPotentialLeaks() {
        if (this.heapSnapshots.length < 2) return [];

        const leaks = [];
        const startSnapshot = this.heapSnapshots[0];
        const endSnapshot = this.heapSnapshots[this.heapSnapshots.length - 1];

        const heapIncrease = endSnapshot.memoryUsage.heapUsed - startSnapshot.memoryUsage.heapUsed;
        const heapIncreasePercent = (heapIncrease / startSnapshot.memoryUsage.heapUsed) * 100;

        if (heapIncrease > 50 * 1024 * 1024) { // More than 50MB increase
            leaks.push({
                type: 'large_heap_increase',
                severity: 'high',
                description: `Heap increased by ${(heapIncrease / 1024 / 1024).toFixed(1)}MB (${heapIncreasePercent.toFixed(1)}%)`,
                recommendation: 'Investigate object retention and ensure proper cleanup'
            });
        }

        const contextIncrease = endSnapshot.heapStats.numberOfDetachedContexts - startSnapshot.heapStats.numberOfDetachedContexts;
        if (contextIncrease > 0) {
            leaks.push({
                type: 'detached_contexts',
                severity: 'medium',
                description: `${contextIncrease} detached contexts created`,
                recommendation: 'Check for unclosed database connections or event listeners'
            });
        }

        return leaks;
    }

    /**
     * Generate optimization recommendations
     */
    generateRecommendations() {
        const recommendations = [];

        if (this.heapSnapshots.length >= 2) {
            const startSnapshot = this.heapSnapshots[0];
            const endSnapshot = this.heapSnapshots[this.heapSnapshots.length - 1];
            
            const heapIncrease = endSnapshot.memoryUsage.heapUsed - startSnapshot.memoryUsage.heapUsed;
            
            if (heapIncrease > 100 * 1024 * 1024) { // More than 100MB
                recommendations.push('Consider implementing object pooling for large operations');
                recommendations.push('Review database query result processing for memory efficiency');
                recommendations.push('Implement streaming for large datasets');
            }

            if (heapIncrease > 20 * 1024 * 1024) { // More than 20MB
                recommendations.push('Force garbage collection at strategic points');
                recommendations.push('Clear temporary variables and references explicitly');
            }
        }

        return recommendations;
    }

    /**
     * Save report to file
     */
    saveReportToFile(report) {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `memory-profile-${this.operationName}-${timestamp}.json`;
            const filepath = path.join(process.cwd(), 'logs', filename);
            
            const logsDir = path.dirname(filepath);
            if (!fs.existsSync(logsDir)) {
                fs.mkdirSync(logsDir, { recursive: true });
            }

            fs.writeFileSync(filepath, JSON.stringify(report, null, 2));
            console.log(`📄 [PROFILER] Report saved: ${filepath}`);
        } catch (error) {
            console.error(`❌ [PROFILER] Error saving report:`, error);
        }
    }
}

module.exports = new DetailedMemoryProfiler();
