const { Model, DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const generateOrderId = require('../utils/generateOrderId');

class Order extends Model {
    static associate(models) {
        // Associate with User
        Order.belongsTo(models.User, {
            foreignKey: 'userId',
            as: 'user',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });

        // Associate with EsimPlan
        Order.belongsTo(models.EsimPlan, {
            foreignKey: 'esimPlanId',
            as: 'plan',
            onDelete: 'RESTRICT',
            onUpdate: 'CASCADE'
        });

        // Associate with EsimStock
        Order.belongsTo(models.EsimStock, {
            foreignKey: 'esimStockId',
            as: 'stock',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        });
        // Associate with EsimPlanStockHistory
        Order.hasOne(models.EsimPlanStockHistory, {
            foreignKey: 'orderId',
            as: 'stockHistory'
        });
    }
}

Order.init({
    id: {
        type: DataTypes.STRING(20),
        primaryKey: true,
        allowNull: false,
        validate: {
            notNull: {
                msg: 'Order ID is required'
            },
            notEmpty: {
                msg: 'Order ID cannot be empty'
            }
        }
    },
    externalOrderId: {
        type: DataTypes.STRING(255),
        allowNull: true
    },
    externalTransactionId: {
        type: DataTypes.STRING(255),
        allowNull: true
    },
    userId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        references: {
            model: 'users',
            key: 'id'
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    },
    esimPlanId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        references: {
            model: 'esimplans',
            key: 'id'
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE'
    },
    esimStockId: {
        type: DataTypes.STRING(36),
        allowNull: true,
        references: {
            model: 'esimstocks',
            key: 'id'
        },
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE'
    },
    quantity: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 1
    },
    orderTotal: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false
    },
    startDate: {
        type: DataTypes.DATE,
        allowNull: true
    },
    status: {
        type: DataTypes.ENUM('pending', 'completed', 'failed', 'cancelled'),
        defaultValue: 'pending',
        allowNull: false
    },
    providerResponse: {
        type: DataTypes.JSON,
        allowNull: true
    },
    providerMetadata: {
        type: DataTypes.JSON,
        allowNull: true
    },
    walletAuthTransactionId: {
        type: DataTypes.STRING(255),
        allowNull: true
    },
    usageData: {
        type: DataTypes.JSON,
        allowNull: true
    },
    dataUsage: {
        type: DataTypes.BIGINT,
        allowNull: true
    },
    dataAllowance: {
        type: DataTypes.BIGINT,
        allowNull: true
    },
    usageStatus: {
        type: DataTypes.ENUM('Active', 'Data Depleted', 'Expired', 'Not Available', 'Unknown'),
        allowNull: true
    },
    expiryDate: {
        type: DataTypes.DATE,
        allowNull: true
    },
    usageMessage: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    lastUsageCheck: {
        type: DataTypes.DATE,
        allowNull: true
    },
    providerOrderStatus: {
        type: DataTypes.STRING(255),
        allowNull: true
    },
    providerErrorCode: {
        type: DataTypes.STRING(50),
        allowNull: true
    },
    providerErrorMessage: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    lastProviderCheck: {
        type: DataTypes.DATE,
        allowNull: true
    },
    validTime: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Valid time for BillionConnect eSIMs'
    },
    parentOrderId: {
        type: DataTypes.STRING(20),
        allowNull: true,
        references: {
            model: 'orders',
            key: 'id'
        },
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
        comment: 'Parent order ID for top-up orders'
    }
}, {
    sequelize,
    modelName: 'Order',
    tableName: 'orders',
    timestamps: true,
    hooks: {
        beforeValidate: async (order) => {
            try {
                if (!order.id) {
                    order.id = await generateOrderId();
                }
            } catch (error) {
                console.error('Error generating order ID:', error);
                throw new Error('Failed to generate order ID');
            }
        }
    }
});

module.exports = Order;
