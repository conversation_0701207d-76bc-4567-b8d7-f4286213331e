import React, { useState, useEffect } from 'react';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
    Search, 
    Filter, 
    ChevronLeft, 
    ChevronRight, 
    RefreshCw, 
    Bell, 
    AlertTriangle, 
    CheckCircle, 
    Clock, 
    XCircle,
    RotateCcw,
    EyeOff,
    Trash2,
    Activity
} from 'lucide-react';
import { useToast } from "@/components/ui/use-toast";
import api from '@/lib/axios';
import { format } from 'date-fns';

const Notifications = () => {
    const [notifications, setNotifications] = useState([]);
    const [stats, setStats] = useState({});
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [providerFilter, setProviderFilter] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [totalCount, setTotalCount] = useState(0);
    const [pageSize, setPageSize] = useState(20);
    const { toast } = useToast();

    const fetchNotifications = async () => {
        try {
            setLoading(true);
            const params = new URLSearchParams({
                page: currentPage,
                limit: pageSize,
                ...(statusFilter && statusFilter !== 'all' && { status: statusFilter }),
                ...(providerFilter && providerFilter !== 'all' && { provider: providerFilter }),
                ...(searchTerm && { orderId: searchTerm })
            });

            const response = await api.get(`/api/admin/notifications?${params}`);
            setNotifications(response.data.data.notifications);
            setTotalPages(response.data.data.pagination.pages);
            setTotalCount(response.data.data.pagination.total);
        } catch (error) {
            toast({
                title: "Error",
                description: "Failed to fetch notifications",
                variant: "destructive",
            });
        } finally {
            setLoading(false);
        }
    };

    const fetchStats = async () => {
        try {
            const response = await api.get('/api/admin/notifications/stats');
            setStats(response.data.data.stats);
        } catch (error) {
            console.error('Failed to fetch notification stats:', error);
        }
    };

    const retryNotification = async (id) => {
        try {
            await api.post(`/api/admin/notifications/${id}/retry`);
            toast({
                title: "Success",
                description: "Notification retry initiated",
            });
            fetchNotifications();
        } catch (error) {
            toast({
                title: "Error",
                description: "Failed to retry notification",
                variant: "destructive",
            });
        }
    };

    const retryBatch = async () => {
        // Show confirmation dialog
        const confirmed = window.confirm(
            `Are you sure you want to retry all ${stats?.failed || 0} failed notifications? This will attempt to reprocess all eligible failed notifications.`
        );

        if (!confirmed) return;

        try {
            await api.post('/api/admin/notifications/retry-batch');
            toast({
                title: "Success",
                description: "Batch retry initiated for all eligible failed notifications. Check server logs for details.",
            });
            fetchNotifications();
            fetchStats();
        } catch (error) {
            toast({
                title: "Error",
                description: "Failed to trigger batch retry",
                variant: "destructive",
            });
        }
    };

    const ignoreNotification = async (id) => {
        try {
            await api.post(`/api/admin/notifications/${id}/ignore`, {
                reason: 'Manually ignored by admin'
            });
            toast({
                title: "Success",
                description: "Notification marked as ignored",
            });
            fetchNotifications();
        } catch (error) {
            toast({
                title: "Error",
                description: "Failed to ignore notification",
                variant: "destructive",
            });
        }
    };

    const processPending = async () => {
        try {
            await api.post('/api/admin/notifications/process-pending');
            toast({
                title: "Success",
                description: "Pending notification processing initiated",
            });
            setTimeout(() => {
                fetchNotifications();
                fetchStats();
            }, 2000);
        } catch (error) {
            toast({
                title: "Error",
                description: "Failed to process pending notifications",
                variant: "destructive",
            });
        }
    };

    useEffect(() => {
        fetchNotifications();
        fetchStats();
    }, [currentPage, statusFilter, providerFilter, searchTerm, pageSize]);

    // Reset to page 1 when filters or page size change
    useEffect(() => {
        if (currentPage !== 1) {
            setCurrentPage(1);
        }
    }, [statusFilter, providerFilter, searchTerm, pageSize]);

    const getStatusColor = (status) => {
        switch (status) {
            case 'completed':
                return 'bg-green-100 text-green-800 border-green-200';
            case 'pending':
                return 'bg-yellow-100 text-yellow-800 border-yellow-200';
            case 'processing':
                return 'bg-blue-100 text-blue-800 border-blue-200';
            case 'failed':
                return 'bg-red-100 text-red-800 border-red-200';
            case 'ignored':
                return 'bg-gray-100 text-gray-800 border-gray-200';
            default:
                return 'bg-gray-100 text-gray-800 border-gray-200';
        }
    };

    const getStatusIcon = (status) => {
        switch (status) {
            case 'completed':
                return <CheckCircle className="w-4 h-4" />;
            case 'pending':
                return <Clock className="w-4 h-4" />;
            case 'processing':
                return <Activity className="w-4 h-4 animate-spin" />;
            case 'failed':
                return <XCircle className="w-4 h-4" />;
            case 'ignored':
                return <EyeOff className="w-4 h-4" />;
            default:
                return <Bell className="w-4 h-4" />;
        }
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <Card>
                <CardHeader>
                    <div className="flex justify-between items-center py-8 px-6 rounded-t-lg bg-gradient-to-r from-purple-800 to-indigo-600">
                        <div className="flex items-center gap-2">
                            <Bell className="w-8 h-8 text-white" />
                            <div>
                                <CardTitle className="text-white">Webhook Notifications</CardTitle>
                                <CardDescription className="text-white">
                                    Monitor and manage BC webhook notification processing
                                </CardDescription>
                            </div>
                        </div>
                        <div className="flex items-center gap-2">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={processPending}
                                className="bg-purple-500 text-white focus:ring-purple-600 border-purple-700"
                            >
                                <RotateCcw className="h-4 w-4 mr-2" />
                                Process Pending
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={retryBatch}
                                className="bg-purple-500 text-white focus:ring-purple-600 border-purple-700"
                                disabled={loading || !stats?.failed || stats.failed === 0}
                                title={stats?.failed > 0 ? `Retry ${stats.failed} failed notifications` : 'No failed notifications to retry'}
                            >
                                <RotateCcw className="h-4 w-4 mr-2" />
                                Retry Failed ({stats?.failed || 0})
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                    fetchNotifications();
                                    fetchStats();
                                }}
                                className="bg-purple-500 text-white focus:ring-purple-600 border-purple-700"
                                disabled={loading}
                            >
                                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                                Refresh
                            </Button>
                        </div>
                    </div>
                </CardHeader>
            </Card>

            {/* Stats Cards */}
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                            <Clock className="w-5 h-5 text-yellow-600" />
                            <div>
                                <p className="text-sm text-gray-600">Pending</p>
                                <p className="text-2xl font-bold text-yellow-600">{stats.pending || 0}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                            <Activity className="w-5 h-5 text-blue-600" />
                            <div>
                                <p className="text-sm text-gray-600">Processing</p>
                                <p className="text-2xl font-bold text-blue-600">{stats.processing || 0}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                            <CheckCircle className="w-5 h-5 text-green-600" />
                            <div>
                                <p className="text-sm text-gray-600">Completed</p>
                                <p className="text-2xl font-bold text-green-600">{stats.completed || 0}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                            <XCircle className="w-5 h-5 text-red-600" />
                            <div>
                                <p className="text-sm text-gray-600">Failed</p>
                                <p className="text-2xl font-bold text-red-600">{stats.failed || 0}</p>
                                {stats.failed > 0 && (
                                    <p className="text-xs text-orange-600 mt-1">
                                        Click &quot;Retry Failed&quot; to retry all
                                    </p>
                                )}
                            </div>
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                            <EyeOff className="w-5 h-5 text-gray-600" />
                            <div>
                                <p className="text-sm text-gray-600">Ignored</p>
                                <p className="text-2xl font-bold text-gray-600">{stats.ignored || 0}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Filters */}
            <Card>
                <CardContent className="p-4">
                    <div className="flex flex-wrap gap-4">
                        <div className="flex-1 min-w-[200px]">
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                                <Input
                                    placeholder="Search by Order ID..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                        </div>
                        <Select value={statusFilter} onValueChange={setStatusFilter}>
                            <SelectTrigger className="w-[150px]">
                                <SelectValue placeholder="Status" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Status</SelectItem>
                                <SelectItem value="pending">Pending</SelectItem>
                                <SelectItem value="processing">Processing</SelectItem>
                                <SelectItem value="completed">Completed</SelectItem>
                                <SelectItem value="failed">Failed</SelectItem>
                                <SelectItem value="ignored">Ignored</SelectItem>
                            </SelectContent>
                        </Select>
                        <Select value={providerFilter} onValueChange={setProviderFilter}>
                            <SelectTrigger className="w-[150px]">
                                <SelectValue placeholder="Provider" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Providers</SelectItem>
                                <SelectItem value="billionconnect">BillionConnect</SelectItem>
                                <SelectItem value="mobimatter">MobiMatter</SelectItem>
                            </SelectContent>
                        </Select>
                        <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(parseInt(value))}>
                            <SelectTrigger className="w-[120px]">
                                <SelectValue placeholder="Per page" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="10">10 per page</SelectItem>
                                <SelectItem value="20">20 per page</SelectItem>
                                <SelectItem value="50">50 per page</SelectItem>
                                <SelectItem value="100">100 per page</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </CardContent>
            </Card>

            {/* Notifications Table */}
            <Card>
                <CardContent className="p-0">
                    <div className="overflow-x-auto">
                        <Table>
                            <TableHeader>
                                <TableRow className="bg-gradient-to-r from-slate-100 to-gray-100">
                                    <TableHead>ID</TableHead>
                                    <TableHead>Provider</TableHead>
                                    <TableHead>Type</TableHead>
                                    <TableHead>Order ID</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead>Attempts</TableHead>
                                    <TableHead>Received</TableHead>
                                    <TableHead>Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {loading ? (
                                    <TableRow>
                                        <TableCell colSpan={8} className="text-center py-8">
                                            <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
                                            Loading notifications...
                                        </TableCell>
                                    </TableRow>
                                ) : notifications.length === 0 ? (
                                    <TableRow>
                                        <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                                            No notifications found
                                        </TableCell>
                                    </TableRow>
                                ) : (
                                    notifications.map((notification) => (
                                        <TableRow key={notification.id}>
                                            <TableCell className="font-mono text-xs">
                                                {notification.id.substring(0, 8)}...
                                            </TableCell>
                                            <TableCell>
                                                <Badge variant="outline" className="capitalize">
                                                    {notification.provider}
                                                </Badge>
                                            </TableCell>
                                            <TableCell>
                                                <Badge variant="secondary">
                                                    {notification.notificationType}
                                                </Badge>
                                            </TableCell>
                                            <TableCell className="font-mono text-sm">
                                                {notification.orderId || notification.channelOrderId || '-'}
                                            </TableCell>
                                            <TableCell>
                                                <Badge className={getStatusColor(notification.status)}>
                                                    <div className="flex items-center gap-1">
                                                        {getStatusIcon(notification.status)}
                                                        {notification.status}
                                                    </div>
                                                </Badge>
                                            </TableCell>
                                            <TableCell>
                                                <span className={notification.processingAttempts > 1 ? 'text-orange-600 font-medium' : ''}>
                                                    {notification.processingAttempts}
                                                </span>
                                            </TableCell>
                                            <TableCell>
                                                {format(new Date(notification.receivedAt), 'MMM dd, HH:mm')}
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex gap-1">
                                                    {notification.status === 'failed' && (
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => retryNotification(notification.id)}
                                                            className="h-8 px-2"
                                                        >
                                                            <RotateCcw className="h-3 w-3" />
                                                        </Button>
                                                    )}
                                                    {(notification.status === 'pending' || notification.status === 'failed') && (
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => ignoreNotification(notification.id)}
                                                            className="h-8 px-2"
                                                        >
                                                            <EyeOff className="h-3 w-3" />
                                                        </Button>
                                                    )}
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))
                                )}
                            </TableBody>
                        </Table>
                    </div>
                </CardContent>
            </Card>

            {/* Enhanced Pagination */}
            {totalPages > 1 && (
                <Card>
                    <CardContent className="py-4">
                        <div className="flex justify-between items-center">
                            <div className="text-sm text-gray-600">
                                Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalCount)} of {totalCount} notifications
                            </div>
                            <div className="flex items-center gap-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentPage(1)}
                                    disabled={currentPage === 1}
                                    className="px-3"
                                >
                                    First
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                                    disabled={currentPage === 1}
                                >
                                    <ChevronLeft className="h-4 w-4" />
                                    Previous
                                </Button>

                                {/* Page Numbers */}
                                <div className="flex items-center gap-1">
                                    {(() => {
                                        const pages = [];
                                        const showPages = 5; // Show 5 page numbers
                                        let startPage = Math.max(1, currentPage - Math.floor(showPages / 2));
                                        let endPage = Math.min(totalPages, startPage + showPages - 1);

                                        // Adjust start if we're near the end
                                        if (endPage - startPage + 1 < showPages) {
                                            startPage = Math.max(1, endPage - showPages + 1);
                                        }

                                        for (let i = startPage; i <= endPage; i++) {
                                            pages.push(
                                                <Button
                                                    key={i}
                                                    variant={i === currentPage ? "default" : "outline"}
                                                    size="sm"
                                                    onClick={() => setCurrentPage(i)}
                                                    className="w-8 h-8 p-0"
                                                >
                                                    {i}
                                                </Button>
                                            );
                                        }
                                        return pages;
                                    })()}
                                </div>

                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                                    disabled={currentPage === totalPages}
                                >
                                    Next
                                    <ChevronRight className="h-4 w-4" />
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentPage(totalPages)}
                                    disabled={currentPage === totalPages}
                                    className="px-3"
                                >
                                    Last
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            )}
        </div>
    );
};

export default Notifications;
