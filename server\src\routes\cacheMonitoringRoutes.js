const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../middleware/auth');
const optimizedCache = require('../utils/optimizedCacheManager');
const hybridCache = require('../services/hybridCache.service');
const memoryMonitor = require('../utils/memoryMonitor');

// Cache statistics endpoint
router.get('/stats', isAuthenticated, async (req, res) => {
    try {
        const stats = await optimizedCache.getStats();
        const healthCheck = await optimizedCache.healthCheck();
        
        res.json({
            success: true,
            data: {
                cache: stats,
                health: healthCheck,
                timestamp: Date.now()
            }
        });
    } catch (error) {
        console.error('Error fetching cache stats:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch cache statistics'
        });
    }
});

// Cache health check endpoint
router.get('/health', async (req, res) => {
    try {
        const health = await optimizedCache.healthCheck();
        const statusCode = health.healthy ? 200 : 503;
        
        res.status(statusCode).json({
            success: health.healthy,
            data: health
        });
    } catch (error) {
        res.status(503).json({
            success: false,
            error: error.message
        });
    }
});

// Manual cache warming endpoint (admin only)
router.post('/warm', isAuthenticated, async (req, res) => {
    try {
        // Basic auth check - in production, add proper admin role check
        if (!req.user || req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                error: 'Admin access required'
            });
        }

        const warmingConfig = [
            {
                namespace: 'countries',
                loader: async () => {
                    const { Country } = require('../models');
                    return await Country.findAll({
                        attributes: ['id', 'name', 'code', 'region'],
                        where: { isActive: true }
                    });
                }
            },
            {
                namespace: 'providers',
                loader: async () => {
                    const { Provider } = require('../models');
                    return await Provider.findAll({
                        attributes: ['id', 'name', 'isActive'],
                        where: { isActive: true }
                    });
                }
            }
        ];

        const warmedCount = await optimizedCache.warmCache(warmingConfig);
        
        res.json({
            success: true,
            data: {
                warmedEntries: warmedCount,
                timestamp: Date.now()
            }
        });
    } catch (error) {
        console.error('Error warming cache:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to warm cache'
        });
    }
});

// Cache invalidation endpoint (admin only)
router.delete('/invalidate/:namespace', isAuthenticated, async (req, res) => {
    try {
        // Basic auth check - in production, add proper admin role check
        if (!req.user || req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                error: 'Admin access required'
            });
        }

        const { namespace } = req.params;
        const { identifier } = req.query;
        
        const result = await optimizedCache.invalidate(namespace, identifier);
        
        res.json({
            success: result,
            data: {
                namespace,
                identifier: identifier || 'all',
                timestamp: Date.now()
            }
        });
    } catch (error) {
        console.error('Error invalidating cache:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to invalidate cache'
        });
    }
});

// Memory status endpoint
router.get('/memory', isAuthenticated, async (req, res) => {
    try {
        const memoryUsage = memoryMonitor.getMemoryUsage();
        const isWarning = memoryMonitor.isMemoryWarning();
        const isCritical = memoryMonitor.isMemoryCritical();
        
        res.json({
            success: true,
            data: {
                usage: memoryUsage,
                status: {
                    normal: !isWarning && !isCritical,
                    warning: isWarning && !isCritical,
                    critical: isCritical
                },
                recommendations: generateMemoryRecommendations(memoryUsage, isWarning, isCritical),
                timestamp: Date.now()
            }
        });
    } catch (error) {
        console.error('Error fetching memory status:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch memory status'
        });
    }
});

// Cache performance metrics endpoint
router.get('/metrics', isAuthenticated, async (req, res) => {
    try {
        const stats = await optimizedCache.getStats();
        
        // Calculate performance metrics
        const metrics = {
            hitRate: parseFloat(stats.hitRate),
            redisAvailability: stats.redis.connected ? 100 : 0,
            totalRequests: stats.redis.hits + stats.redis.misses + stats.fallback.hits + stats.fallback.misses,
            errorRate: stats.errors > 0 ? (stats.errors / (stats.errors + stats.redis.hits + stats.fallback.hits) * 100).toFixed(2) : 0,
            memoryEfficiency: calculateMemoryEfficiency(stats.fallback),
            compressionRatio: calculateCompressionRatio(stats)
        };
        
        res.json({
            success: true,
            data: {
                metrics,
                rawStats: stats,
                timestamp: Date.now()
            }
        });
    } catch (error) {
        console.error('Error fetching cache metrics:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch cache metrics'
        });
    }
});

// Helper functions
function generateMemoryRecommendations(usage, isWarning, isCritical) {
    const recommendations = [];
    
    if (isCritical) {
        recommendations.push('CRITICAL: Consider restarting the application or clearing cache');
        recommendations.push('Reduce cache TTL values to free up memory faster');
        recommendations.push('Enable Redis to offload memory pressure');
    } else if (isWarning) {
        recommendations.push('Monitor memory usage closely');
        recommendations.push('Consider enabling data compression');
        recommendations.push('Review cache size limits');
    } else {
        recommendations.push('Memory usage is healthy');
        recommendations.push('Consider increasing cache TTL for better performance');
    }
    
    return recommendations;
}

function calculateMemoryEfficiency(fallbackStats) {
    if (!fallbackStats.keys || !fallbackStats.vsize) return 0;
    
    // Calculate average value size per key
    const avgValueSize = fallbackStats.vsize / fallbackStats.keys;
    const efficiency = Math.max(0, 100 - (avgValueSize / 1024)); // Penalize large values
    
    return Math.min(100, efficiency).toFixed(2);
}

function calculateCompressionRatio(stats) {
    // This would need to be tracked in the cache service
    // For now, return a placeholder
    return 'N/A';
}

module.exports = router;
