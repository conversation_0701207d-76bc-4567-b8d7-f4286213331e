const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const NotificationMessage = sequelize.define('NotificationMessage', {
    id: {
        type: DataTypes.CHAR(36),
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
    },
    provider: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: 'Provider name'
    },
    notificationType: {
        type: DataTypes.STRING(20),
        allowNull: false,
        comment: 'Notification type (e.g., N009, F041, etc.)'
    },
    rawPayload: {
        type: DataTypes.JSON,
        allowNull: false,
        comment: 'Complete raw webhook payload as received'
    },
    orderId: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: 'External order ID from the notification'
    },
    channelOrderId: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: 'Channel order ID from the notification'
    },
    internalOrderId: {
        type: DataTypes.CHAR(36),
        allowNull: true,
        references: {
            model: 'orders',
            key: 'id'
        },
        comment: 'Reference to internal order if found'
    },
    status: {
        type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed', 'ignored'),
        allowNull: false,
        defaultValue: 'pending',
        comment: 'Processing status of the notification'
    },
    processingAttempts: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Number of processing attempts made'
    },
    lastProcessingAttempt: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Timestamp of last processing attempt'
    },
    processingError: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: 'Error message from last failed processing attempt'
    },
    processingResult: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: 'Result data from successful processing'
    },
    receivedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: 'Timestamp when notification was received'
    },
    processedAt: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Timestamp when notification was successfully processed'
    },
    metadata: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: 'Additional metadata for processing context'
    }
}, {
    tableName: 'notification_messages',
    timestamps: true,
    indexes: [
        {
            name: 'idx_notification_provider_type',
            fields: ['provider', 'notificationType']
        },
        {
            name: 'idx_notification_status',
            fields: ['status', 'receivedAt']
        },
        {
            name: 'idx_notification_order_ids',
            fields: ['orderId', 'channelOrderId']
        },
        {
            name: 'idx_notification_processing',
            fields: ['status', 'processingAttempts', 'lastProcessingAttempt']
        },
        {
            name: 'idx_notification_cleanup',
            fields: ['status', 'createdAt']
        }
    ]
});

// Define associations
NotificationMessage.associate = function(models) {
    NotificationMessage.belongsTo(models.Order, {
        foreignKey: 'internalOrderId',
        as: 'order',
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE'
    });
};

module.exports = NotificationMessage;
