# Enhanced Memory Management System

This document describes the comprehensive memory management system implemented to prevent memory leaks and optimize memory usage in the eSIM platform.

## Overview

The enhanced memory management system consists of several interconnected components that work together to:

1. **Prevent memory leaks** during intensive operations
2. **Predict memory usage** based on historical patterns
3. **Automatically clean up** memory when thresholds are exceeded
4. **Monitor and alert** on memory issues
5. **Provide comprehensive testing** and monitoring tools

## Components

### 1. Enhanced Memory Cleanup (`memoryCleanup.js`)

**Features:**
- Object pool management for reusable objects
- Streaming cleanup for very large collections
- Weak reference tracking
- Search-specific cleanup strategies
- Proactive memory monitoring
- Comprehensive garbage collection with measurement

**Key Methods:**
- `createObjectPool(name, factory, resetFn, maxSize)` - Create reusable object pools
- `clearLargeCollections(collections, options)` - Clean up large arrays/objects with streaming support
- `cleanupSearchResults(searchData, searchType)` - Search-specific cleanup
- `forceGarbageCollection(options)` - Enhanced GC with multiple cycles
- `proactiveCleanup()` - Automatic cleanup when memory pressure is high

### 2. Search Memory Manager (`searchMemoryManager.js`)

**Purpose:** Specialized memory management for search operations that are prone to memory leaks.

**Features:**
- Operation-specific memory monitoring
- Memory threshold enforcement
- Automatic cleanup based on search type
- Memory prediction and risk assessment

**Key Methods:**
- `startSearchOperation(operationId, searchType, searchTerm)` - Begin monitoring
- `checkSearchMemory(operationId, phase)` - Check memory during operation
- `cleanupSearchData(searchData, searchType)` - Clean up search results
- `completeSearchOperation(operationId, results)` - Complete and cleanup
- `emergencyCleanupAll()` - Emergency cleanup for all active searches

### 3. Predictive Memory Manager (`predictiveMemoryManager.js`)

**Purpose:** Machine learning-like system that learns from past operations to predict and prevent memory issues.

**Features:**
- Pattern recognition for memory usage
- Risk assessment for operations
- Predictive memory usage calculation
- Operation recommendations
- Historical analysis and insights

**Key Methods:**
- `recordOperation(operation)` - Record operation for learning
- `predictMemoryUsage(operationInfo)` - Predict memory usage
- `shouldProceedWithOperation(operationInfo, currentMemory)` - Risk assessment
- `getMemoryInsights()` - Get comprehensive insights

### 4. Memory Leak Prevention Middleware (`memoryLeakPrevention.js`)

**Purpose:** Express middleware that automatically manages memory for API endpoints.

**Features:**
- Automatic search type detection
- Memory monitoring during requests
- Cleanup after responses
- Emergency memory protection
- Request-specific memory management

**Middleware Functions:**
- `memoryLeakPrevention(options)` - Main middleware for search operations
- `memoryMonitoring(options)` - General memory monitoring
- `emergencyMemoryProtection(options)` - Critical memory protection
- `memoryCleanupAfterResponse(options)` - Post-response cleanup

## Usage Examples

### 1. Basic Memory Cleanup

```javascript
const memoryCleanup = require('./utils/memoryCleanup');

// Create object pool
const pool = memoryCleanup.createObjectPool(
    'user-objects',
    () => ({ id: null, data: null }),
    (obj) => { obj.id = null; obj.data = null; },
    100
);

// Clean up large collections
const largeArray = new Array(10000).fill('data');
const largeObject = { /* large object */ };

memoryCleanup.clearLargeCollections([largeArray, largeObject], {
    streaming: true,
    batchSize: 1000
});

// Force garbage collection
memoryCleanup.forceGarbageCollection({ cycles: 2 });
```

### 2. Search Memory Management

```javascript
const searchMemoryManager = require('./utils/searchMemoryManager');

// Start search operation
const operation = searchMemoryManager.startSearchOperation(
    'search-123',
    'broad',
    'global'
);

// Check memory during operation
const memoryCheck = searchMemoryManager.checkSearchMemory('search-123', 'processing');
if (!memoryCheck.canContinue) {
    // Handle memory limit exceeded
    return res.status(503).json({ error: 'Memory limit exceeded' });
}

// Clean up search data
const searchData = { allPlans, processedPlans, countries };
searchMemoryManager.cleanupSearchData(searchData, 'broad');

// Complete operation
searchMemoryManager.completeSearchOperation('search-123', { resultCount: 1000 });
```

### 3. Predictive Memory Management

```javascript
const predictiveMemoryManager = require('./utils/predictiveMemoryManager');

// Get prediction for operation
const prediction = predictiveMemoryManager.predictMemoryUsage({
    type: 'search',
    searchType: 'broad',
    searchTerm: 'global',
    resultCount: 5000
});

// Check if operation should proceed
const currentMemory = process.memoryUsage().heapUsed / 1024 / 1024;
const shouldProceed = predictiveMemoryManager.shouldProceedWithOperation({
    type: 'search',
    searchType: 'broad',
    searchTerm: 'global'
}, currentMemory);

if (!shouldProceed.proceed) {
    return res.status(503).json({
        error: 'Operation blocked by predictive memory management',
        reason: shouldProceed.reason
    });
}
```

### 4. Using Middleware

```javascript
const { memoryLeakPrevention, emergencyMemoryProtection } = require('./middleware/memoryLeakPrevention');

// Apply to routes
router.get('/partner', 
    isAuthenticated,
    emergencyMemoryProtection(),
    memoryLeakPrevention(),
    getPartnerEsimPlans
);
```

## API Endpoints

### Memory Status
- `GET /api/memory/status` - Get comprehensive memory status
- `GET /api/memory/health` - Simple health check

### Memory Management
- `POST /api/memory/cleanup` - Trigger manual cleanup
- `POST /api/memory/emergency` - Emergency cleanup
- `PUT /api/memory/settings` - Configure settings

### Monitoring and Testing
- `GET /api/memory/prediction` - Get memory predictions
- `GET /api/memory/searches` - Get active search operations
- `GET /api/memory/history` - Get memory usage history
- `POST /api/memory/test` - Run comprehensive tests
- `GET /api/memory/test/results` - Get test results

## Configuration

### Memory Thresholds

```javascript
// Default thresholds (in MB)
const thresholds = {
    warning: 1200,      // Warning level
    critical: 1500,     // Critical level
    emergency: 1700     // Emergency level
};

// Search-specific thresholds
const searchThresholds = {
    broad: 150,         // Broad searches (global, premium, etc.)
    specific: 50,       // Specific searches
    none: 25           // No search operations
};
```

### Cleanup Strategies

```javascript
// Broad search strategy
const broadStrategy = {
    maxMemoryIncrease: 150,
    cleanupFrequency: 'immediate',
    streamingThreshold: 1000,
    gcCycles: 2,
    emergencyThreshold: 200
};

// Specific search strategy
const specificStrategy = {
    maxMemoryIncrease: 50,
    cleanupFrequency: 'delayed',
    streamingThreshold: 5000,
    gcCycles: 1,
    emergencyThreshold: 100
};
```

## Monitoring and Alerts

### Memory Leak Detection

The system automatically detects memory leak patterns:

1. **Steady Growth**: Memory consistently increasing over time
2. **High Memory Usage**: Memory usage above 80% of available
3. **Rapid Growth**: Memory increasing by more than 15% quickly
4. **Consistent High**: Memory consistently above 70%

### Alerts and Actions

When memory issues are detected:

1. **Warning Level**: Log warnings, trigger proactive cleanup
2. **Critical Level**: Force garbage collection, clear object pools
3. **Emergency Level**: Abort operations, emergency cleanup, consider restart

### Predictive Insights

The system provides insights on:

- **Risk Patterns**: Operations with high memory leak risk
- **Memory Trends**: Historical memory usage patterns
- **Operation Efficiency**: Success rates and memory usage by operation type
- **Recommendations**: Specific actions to improve memory management

## Testing

### Comprehensive Test Suite

Run the complete memory management test suite:

```bash
# Via API
POST /api/memory/test

# Get results
GET /api/memory/test/results
```

### Test Categories

1. **Object Pool Management**: Test pool creation, usage, and cleanup
2. **Large Collection Cleanup**: Test cleanup of large arrays and objects
3. **Streaming Cleanup**: Test streaming cleanup for very large collections
4. **Garbage Collection**: Test GC effectiveness and measurement
5. **Weak Reference Management**: Test weak reference tracking and cleanup
6. **Search Memory Management**: Test search-specific memory management
7. **Predictive Management**: Test prediction accuracy and recommendations

## Best Practices

### For Developers

1. **Use Object Pools**: For frequently created/destroyed objects
2. **Clean Up Large Collections**: Use streaming cleanup for large datasets
3. **Monitor Memory Usage**: Check memory during intensive operations
4. **Handle Memory Limits**: Gracefully handle memory limit exceeded errors
5. **Use Weak References**: For optional references to large objects

### For Operations

1. **Monitor Memory Endpoints**: Regularly check `/api/memory/status`
2. **Set Up Alerts**: Monitor memory usage and leak patterns
3. **Run Regular Tests**: Execute memory tests to validate cleanup
4. **Review Insights**: Check predictive insights for optimization opportunities
5. **Configure Thresholds**: Adjust thresholds based on system capacity

## Troubleshooting

### Common Issues

1. **Memory Leaks in Search Operations**
   - Check search type detection
   - Verify cleanup strategies are applied
   - Review predictive insights for patterns

2. **High Memory Usage**
   - Trigger manual cleanup: `POST /api/memory/cleanup`
   - Check active searches: `GET /api/memory/searches`
   - Review memory history: `GET /api/memory/history`

3. **Predictive Blocks**
   - Review prediction accuracy
   - Adjust risk thresholds if needed
   - Check historical operation patterns

### Emergency Procedures

1. **Critical Memory Situation**
   ```bash
   # Emergency cleanup
   POST /api/memory/emergency
   
   # Check status
   GET /api/memory/health
   ```

2. **Application Restart Required**
   - When memory cannot be freed
   - When leak patterns are critical
   - When emergency cleanup fails

This enhanced memory management system provides comprehensive protection against memory leaks while maintaining optimal performance for the eSIM platform.
