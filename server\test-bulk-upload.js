/**
 * Test script for bulk eSIM plan upload functionality
 * This script tests the bulk upload API endpoint with sample data
 */

const axios = require('axios');

// Sample test data for bulk upload
const testPlansData = [
    {
        name: "Europe 7-Day Data Plan",
        description: "7-day data plan for Europe",
        networkName: "Vodafone",
        networkType: "4G/LTE",
        region: "Europe",
        buyingPrice: 8.50,
        sellingPrice: 15.00,
        validityDays: 7,
        planType: "Fixed",
        planData: 5,
        planDataUnit: "GB",
        category: "esim_realtime",
        planCategory: "Data Only",
        is_voice: "Not Available",
        is_sms: "Not Available",
        top_up: "Available",
        hotspot: "Available",
        activationPolicy: "Activation upon purchase",
        speed: "Unrestricted",
        stockThreshold: 10,
        countries: ["United Kingdom", "France", "Germany", "Italy", "Spain"],
        instructions: "Activate upon arrival in Europe",
        planInfo: "High-speed data with EU roaming",
        additionalInfo: "Compatible with all devices"
    },
    {
        name: "USA 14-Day Unlimited",
        description: "14-day unlimited data for USA",
        networkName: "T-Mobile",
        networkType: "5G",
        region: "North America",
        buyingPrice: 12.00,
        sellingPrice: 25.00,
        validityDays: 14,
        planType: "Unlimited",
        category: "esim_realtime",
        planCategory: "Data Only",
        is_voice: "Not Available",
        is_sms: "Not Available",
        top_up: "Not Available",
        hotspot: "Available",
        activationPolicy: "Activation upon first usage",
        speed: "Unrestricted",
        stockThreshold: 15,
        countries: ["United States"],
        instructions: "Activate when you arrive in the USA",
        planInfo: "Unlimited high-speed data",
        additionalInfo: "No throttling"
    },
    {
        name: "Asia Pacific Voice & Data",
        description: "Voice and data plan for Asia Pacific",
        networkName: "SingTel",
        networkType: "4G/LTE",
        region: "Asia Pacific",
        buyingPrice: 15.00,
        sellingPrice: 30.00,
        validityDays: 10,
        planType: "Fixed",
        planData: 3,
        planDataUnit: "GB",
        category: "esim_realtime",
        planCategory: "Voice and Data",
        is_voice: "Available",
        voiceMin: 100,
        voiceMinUnit: "Min",
        is_sms: "Available",
        sms: 50,
        top_up: "Available",
        hotspot: "Available",
        activationPolicy: "Activation upon purchase",
        speed: "Unrestricted",
        stockThreshold: 20,
        countries: ["Singapore", "Malaysia", "Thailand", "Japan"],
        instructions: "Activate before travel",
        planInfo: "Voice, SMS and data included",
        additionalInfo: "Perfect for business travel"
    }
];

// Test data with validation errors (for testing error handling)
const testPlansDataWithErrors = [
    {
        // Missing required fields
        description: "Invalid plan - missing name",
        networkName: "Test Network",
        buyingPrice: 5.00,
        validityDays: 7,
        countries: ["US"]
    },
    {
        name: "Invalid Country Plan",
        networkName: "Test Network",
        buyingPrice: 5.00,
        validityDays: 7,
        countries: ["Invalid Country"], // Invalid country name
        category: "esim_realtime",
        planCategory: "Data Only",
        activationPolicy: "Activation upon purchase",
        speed: "Unrestricted"
    },
    {
        name: "Valid Plan",
        networkName: "Test Network",
        buyingPrice: 5.00,
        sellingPrice: 10.00,
        validityDays: 7,
        planType: "Fixed",
        planData: 1,
        planDataUnit: "GB",
        category: "esim_realtime",
        planCategory: "Data Only",
        is_voice: "Not Available",
        is_sms: "Not Available",
        top_up: "Not Available",
        hotspot: "Available",
        activationPolicy: "Activation upon purchase",
        speed: "Unrestricted",
        stockThreshold: 10,
        countries: ["United States"],
        rowIndex: 3
    }
];

async function testBulkUpload(testData, testName) {
    console.log(`\n🧪 Testing: ${testName}`);
    console.log(`📊 Test data: ${testData.length} plans`);
    
    try {
        const response = await axios.post('http://localhost:3000/api/esim-plans/bulk', testData, {
            headers: {
                'Content-Type': 'application/json',
                // Note: In real usage, you would need to include authentication headers
                // 'Authorization': 'Bearer YOUR_ADMIN_TOKEN'
            }
        });

        console.log(`✅ Status: ${response.status}`);
        console.log(`📝 Message: ${response.data.message}`);
        console.log(`📈 Summary:`, response.data.summary);
        
        if (response.data.errors && response.data.errors.length > 0) {
            console.log(`❌ Errors found:`);
            response.data.errors.forEach(error => {
                console.log(`   Row ${error.row}: ${error.error}`);
            });
        }
        
        if (response.data.plans && response.data.plans.length > 0) {
            console.log(`✅ Successfully created plans:`);
            response.data.plans.forEach(plan => {
                console.log(`   - ${plan.name} (ID: ${plan.id})`);
            });
        }

    } catch (error) {
        console.log(`❌ Test failed:`);
        if (error.response) {
            console.log(`   Status: ${error.response.status}`);
            console.log(`   Message: ${error.response.data.message}`);
            if (error.response.data.errors) {
                console.log(`   Errors:`, error.response.data.errors);
            }
        } else {
            console.log(`   Error: ${error.message}`);
        }
    }
}

async function runTests() {
    console.log('🚀 Starting bulk upload tests...');
    console.log('⚠️  Note: Make sure the server is running and you have admin authentication set up');
    
    // Test 1: Valid data
    await testBulkUpload(testPlansData, 'Valid Plans Upload');
    
    // Test 2: Mixed valid/invalid data
    await testBulkUpload(testPlansDataWithErrors, 'Mixed Valid/Invalid Plans');
    
    console.log('\n🎉 All tests completed!');
    console.log('\n📋 To run these tests:');
    console.log('1. Start the server: npm run dev (in server directory)');
    console.log('2. Ensure you have admin authentication');
    console.log('3. Update the authorization header in this script');
    console.log('4. Run: node test-bulk-upload.js');
}

// Export for use in other test files
module.exports = {
    testPlansData,
    testPlansDataWithErrors,
    testBulkUpload
};

// Run tests if this file is executed directly
if (require.main === module) {
    runTests().catch(console.error);
}
