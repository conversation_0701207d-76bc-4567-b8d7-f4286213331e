'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // First, check if there are any duplicate orderId records in the stock history table
      const duplicates = await queryInterface.sequelize.query(
        `SELECT orderId, COUNT(*) as count 
         FROM esimplanstockhistory 
         WHERE orderId IS NOT NULL 
         GROUP BY orderId 
         HAVING COUNT(*) > 1`,
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      if (duplicates.length > 0) {
        console.log(`Found ${duplicates.length} orders with duplicate stock history records. Cleaning up...`);
        
        // For each duplicate orderId, keep only the most recent record (by createdAt)
        for (const duplicate of duplicates) {
          const orderId = duplicate.orderId;
          
          // Get all records for this orderId, ordered by createdAt DESC
          const records = await queryInterface.sequelize.query(
            `SELECT id, createdAt FROM esimplanstockhistory 
             WHERE orderId = :orderId 
             ORDER BY createdAt DESC`,
            { 
              replacements: { orderId },
              type: Sequelize.QueryTypes.SELECT,
              transaction 
            }
          );
          
          // Keep the first (most recent) record, delete the rest
          const recordsToDelete = records.slice(1);
          
          for (const record of recordsToDelete) {
            await queryInterface.sequelize.query(
              `DELETE FROM esimplanstockhistory WHERE id = :id`,
              { 
                replacements: { id: record.id },
                type: Sequelize.QueryTypes.DELETE,
                transaction 
              }
            );
          }
          
          console.log(`Cleaned up ${recordsToDelete.length} duplicate records for order ${orderId}`);
        }
      }

      // Now add the unique constraint
      await queryInterface.addConstraint('esimplanstockhistory', {
        fields: ['orderId'],
        type: 'unique',
        name: 'unique_order_stock_history',
        transaction
      });

      console.log('Successfully added unique constraint on orderId to esimplanstockhistory table');
      
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      console.error('Error adding unique constraint:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // Remove the unique constraint
      await queryInterface.removeConstraint('esimplanstockhistory', 'unique_order_stock_history', {
        transaction
      });
      
      console.log('Successfully removed unique constraint from esimplanstockhistory table');
      
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      console.error('Error removing unique constraint:', error);
      throw error;
    }
  }
};
