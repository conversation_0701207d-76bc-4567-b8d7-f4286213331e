const { EsimPlan, Provider } = require('./src/models');
const billionconnectService = require('./src/services/billionconnect.service');
const providerFactory = require('./src/services/provider.factory');

async function checkAllPlansCapacity() {
    console.log('🔍 COMPREHENSIVE CHECK: All Plans Capacity Analysis\n');
    
    try {
        // 1. Find BillionConnect provider
        const provider = await Provider.findOne({
            where: { name: 'billionconnect' }
        });
        
        if (!provider) {
            console.log('❌ BillionConnect provider not found');
            return;
        }
        
        console.log(`✅ Found BillionConnect provider: ${provider.name}`);
        
        // 2. Get ALL fresh data from BillionConnect API
        console.log('\n📡 Fetching ALL fresh data from BillionConnect API...');
        const freshProducts = await billionconnectService.getProducts();
        console.log(`✅ Retrieved ${freshProducts.length} fresh products from API\n`);
        
        // 3. Analyze ALL products by capacity
        let capacityMinusOne = 0;
        let capacityPositive = 0;
        let capacityOther = 0;
        
        console.log('📊 ANALYZING ALL PRODUCTS BY CAPACITY:\n');
        
        freshProducts.forEach((product, index) => {
            console.log(`${index + 1}. ${product.name}`);
            console.log(`   SKU: ${product.externalSkuId}`);
            console.log(`   capacity: ${product.capacity} (type: ${typeof product.capacity})`);
            console.log(`   isUnlimited: ${product.isUnlimited}`);
            console.log(`   planData: ${product.planData}`);
            console.log(`   planDataUnit: ${product.planDataUnit}`);
            
            // Categorize by capacity
            if (product.capacity === -1) {
                capacityMinusOne++;
                console.log(`   📋 CATEGORY: UNLIMITED (capacity = -1)`);
            } else if (product.capacity > 0) {
                capacityPositive++;
                console.log(`   📋 CATEGORY: FIXED (capacity = ${product.capacity})`);
            } else {
                capacityOther++;
                console.log(`   📋 CATEGORY: OTHER (capacity = ${product.capacity})`);
            }
            
            // Test what provider factory would do with this product
            const expectedPlanType = product.capacity === -1 ? 'Unlimited' : 'Fixed';
            console.log(`   🎯 EXPECTED planType: "${expectedPlanType}"`);
            
            console.log(''); // Empty line for readability
        });
        
        console.log('📈 CAPACITY SUMMARY:');
        console.log(`   Plans with capacity = -1: ${capacityMinusOne}`);
        console.log(`   Plans with capacity > 0: ${capacityPositive}`);
        console.log(`   Plans with other capacity: ${capacityOther}`);
        console.log(`   Total: ${freshProducts.length}\n`);
        
        // 4. Test standardization for ALL products
        console.log('🔧 TESTING STANDARDIZATION FOR ALL PRODUCTS:\n');
        
        let correctUnlimited = 0;
        let incorrectUnlimited = 0;
        let correctFixed = 0;
        let incorrectFixed = 0;
        
        for (const [index, product] of freshProducts.entries()) {
            try {
                console.log(`${index + 1}. STANDARDIZING: ${product.name}`);
                console.log(`   Input capacity: ${product.capacity}`);
                
                const standardized = await providerFactory.standardizeProduct('billionconnect', product);
                
                console.log(`   Output planType: "${standardized.planType}"`);
                console.log(`   Output planData: ${standardized.planData}`);
                console.log(`   Output planDataUnit: ${standardized.planDataUnit}`);
                
                // Check if standardization is correct
                const shouldBeUnlimited = product.capacity === -1;
                const isUnlimited = standardized.planType === 'Unlimited';
                
                if (shouldBeUnlimited && isUnlimited) {
                    correctUnlimited++;
                    console.log(`   ✅ CORRECT: Unlimited plan`);
                } else if (shouldBeUnlimited && !isUnlimited) {
                    incorrectUnlimited++;
                    console.log(`   ❌ INCORRECT: Should be Unlimited but is ${standardized.planType}`);
                    console.log(`   🐛 DEBUG: capacity=${product.capacity}, capacity===-1: ${product.capacity === -1}`);
                } else if (!shouldBeUnlimited && !isUnlimited) {
                    correctFixed++;
                    console.log(`   ✅ CORRECT: Fixed plan`);
                } else if (!shouldBeUnlimited && isUnlimited) {
                    incorrectFixed++;
                    console.log(`   ❌ INCORRECT: Should be Fixed but is Unlimited`);
                }
                
            } catch (error) {
                console.log(`   ❌ ERROR standardizing: ${error.message}`);
            }
            
            console.log(''); // Empty line
        }
        
        console.log('🎯 STANDARDIZATION RESULTS:');
        console.log(`   Correct Unlimited: ${correctUnlimited}`);
        console.log(`   Incorrect Unlimited: ${incorrectUnlimited}`);
        console.log(`   Correct Fixed: ${correctFixed}`);
        console.log(`   Incorrect Fixed: ${incorrectFixed}\n`);
        
        // 5. Check what's currently in the database
        console.log('💾 CHECKING DATABASE RECORDS:\n');
        
        const dbPlans = await EsimPlan.findAll({
            where: { providerId: provider.id },
            attributes: ['name', 'externalSkuId', 'planType', 'planData', 'planDataUnit', 'providerMetadata']
        });
        
        console.log(`Found ${dbPlans.length} plans in database\n`);
        
        let dbUnlimitedCorrect = 0;
        let dbUnlimitedIncorrect = 0;
        let dbFixedCorrect = 0;
        let dbFixedIncorrect = 0;
        let dbNoMetadata = 0;
        
        dbPlans.forEach((plan, index) => {
            const metadata = plan.providerMetadata || {};
            
            console.log(`${index + 1}. DB PLAN: ${plan.name}`);
            console.log(`   SKU: ${plan.externalSkuId}`);
            console.log(`   DB planType: "${plan.planType}"`);
            console.log(`   DB planData: ${plan.planData}`);
            console.log(`   DB planDataUnit: ${plan.planDataUnit}`);
            console.log(`   Metadata capacity: ${metadata.capacity}`);
            console.log(`   Metadata isUnlimited: ${metadata.isUnlimited}`);
            
            if (metadata.capacity === undefined) {
                dbNoMetadata++;
                console.log(`   ⚠️  NO METADATA: Missing capacity information`);
            } else {
                const shouldBeUnlimited = metadata.capacity === -1;
                const isUnlimited = plan.planType === 'Unlimited';
                
                if (shouldBeUnlimited && isUnlimited) {
                    dbUnlimitedCorrect++;
                    console.log(`   ✅ DB CORRECT: Unlimited plan`);
                } else if (shouldBeUnlimited && !isUnlimited) {
                    dbUnlimitedIncorrect++;
                    console.log(`   ❌ DB INCORRECT: capacity=-1 but planType="${plan.planType}"`);
                } else if (!shouldBeUnlimited && !isUnlimited) {
                    dbFixedCorrect++;
                    console.log(`   ✅ DB CORRECT: Fixed plan`);
                } else if (!shouldBeUnlimited && isUnlimited) {
                    dbFixedIncorrect++;
                    console.log(`   ❌ DB INCORRECT: capacity=${metadata.capacity} but planType="Unlimited"`);
                }
            }
            
            console.log(''); // Empty line
        });
        
        console.log('📊 DATABASE ANALYSIS RESULTS:');
        console.log(`   DB Unlimited Correct: ${dbUnlimitedCorrect}`);
        console.log(`   DB Unlimited Incorrect: ${dbUnlimitedIncorrect}`);
        console.log(`   DB Fixed Correct: ${dbFixedCorrect}`);
        console.log(`   DB Fixed Incorrect: ${dbFixedIncorrect}`);
        console.log(`   DB No Metadata: ${dbNoMetadata}\n`);
        
        // 6. Final summary
        console.log('🎯 FINAL ANALYSIS:');
        console.log(`   API has ${capacityMinusOne} plans with capacity = -1`);
        console.log(`   Standardization creates ${correctUnlimited} unlimited plans correctly`);
        console.log(`   Database has ${dbUnlimitedCorrect} unlimited plans correctly stored`);
        console.log(`   Database has ${dbUnlimitedIncorrect} plans that should be unlimited but aren't`);
        
        if (dbUnlimitedIncorrect > 0) {
            console.log('\n❌ PROBLEM IDENTIFIED:');
            console.log(`   ${dbUnlimitedIncorrect} plans in database have capacity=-1 but are not marked as Unlimited`);
            console.log('   This suggests the sync process is not working correctly');
        } else if (incorrectUnlimited > 0) {
            console.log('\n❌ PROBLEM IDENTIFIED:');
            console.log(`   ${incorrectUnlimited} plans are not being standardized correctly`);
            console.log('   The provider factory logic may have an issue');
        } else {
            console.log('\n✅ NO PROBLEMS FOUND:');
            console.log('   All plans are being processed correctly');
        }
        
    } catch (error) {
        console.error('❌ Error in comprehensive check:', error);
        console.error('Stack trace:', error.stack);
    }
}

checkAllPlansCapacity().catch(console.error);
