/**
 * Memory Test Utilities
 * Utilities for testing memory cleanup and leak prevention functionality
 */

const memoryCleanup = require('./memoryCleanup');
const searchMemoryManager = require('./searchMemoryManager');
const predictiveMemoryManager = require('./predictiveMemoryManager');

class MemoryTestUtils {
    constructor() {
        this.testResults = [];
        this.isRunning = false;
    }

    /**
     * Run comprehensive memory cleanup tests
     */
    async runMemoryCleanupTests() {
        console.log('🧪 [MEMORY TESTS] Starting comprehensive memory cleanup tests');
        this.isRunning = true;
        const results = [];

        try {
            // Test 1: Object Pool Management
            results.push(await this.testObjectPoolManagement());

            // Test 2: Large Collection Cleanup
            results.push(await this.testLargeCollectionCleanup());

            // Test 3: Streaming Cleanup
            results.push(await this.testStreamingCleanup());

            // Test 4: Garbage Collection Effectiveness
            results.push(await this.testGarbageCollectionEffectiveness());

            // Test 5: Weak Reference Management
            results.push(await this.testWeakReferenceManagement());

            // Test 6: Search Memory Management
            results.push(await this.testSearchMemoryManagement());

            // Test 7: Predictive Memory Management
            results.push(await this.testPredictiveMemoryManagement());

            const summary = this.generateTestSummary(results);
            console.log('🧪 [MEMORY TESTS] All tests completed');
            
            this.testResults.push({
                timestamp: Date.now(),
                results,
                summary
            });

            return { success: true, results, summary };
        } catch (error) {
            console.error('🧪 [MEMORY TESTS] Test suite failed:', error);
            return { success: false, error: error.message };
        } finally {
            this.isRunning = false;
        }
    }

    /**
     * Test object pool management
     */
    async testObjectPoolManagement() {
        console.log('🧪 Testing object pool management...');
        const startTime = Date.now();
        const startMemory = process.memoryUsage().heapUsed;

        try {
            const pool = memoryCleanup.createObjectPool(
                'test-pool',
                () => ({ data: new Array(1000).fill('test') }),
                (obj) => { obj.data = null; },
                50
            );

            const objects = [];
            for (let i = 0; i < 100; i++) {
                objects.push(memoryCleanup.getFromPool('test-pool'));
            }

            for (const obj of objects) {
                memoryCleanup.returnToPool('test-pool', obj);
            }

            const stats = memoryCleanup.getPoolStats('test-pool');

            memoryCleanup.clearAllPools();

            const endMemory = process.memoryUsage().heapUsed;
            const duration = Date.now() - startTime;

            return {
                test: 'Object Pool Management',
                success: true,
                duration,
                memoryChange: (endMemory - startMemory) / 1024 / 1024,
                stats,
                details: 'Object pool created, used, and cleared successfully'
            };
        } catch (error) {
            return {
                test: 'Object Pool Management',
                success: false,
                error: error.message,
                duration: Date.now() - startTime
            };
        }
    }

    /**
     * Test large collection cleanup
     */
    async testLargeCollectionCleanup() {
        console.log('🧪 Testing large collection cleanup...');
        const startTime = Date.now();
        const startMemory = process.memoryUsage().heapUsed;

        try {
            const largeArray = new Array(10000).fill().map((_, i) => ({ id: i, data: `item_${i}` }));
            const largeObject = {};
            for (let i = 0; i < 5000; i++) {
                largeObject[`key_${i}`] = `value_${i}`;
            }

            const beforeCleanup = process.memoryUsage().heapUsed;

            const standardCleared = memoryCleanup.clearLargeCollections([largeArray, largeObject]);

            const streamingArray = new Array(20000).fill().map((_, i) => ({ id: i }));
            const streamingObject = {};
            for (let i = 0; i < 10000; i++) {
                streamingObject[`stream_${i}`] = i;
            }

            const streamingCleared = memoryCleanup.clearLargeCollections(
                [streamingArray, streamingObject], 
                { streaming: true, batchSize: 1000 }
            );

            await this.waitForStreamingCleanup();

            const afterCleanup = process.memoryUsage().heapUsed;
            const duration = Date.now() - startTime;

            return {
                test: 'Large Collection Cleanup',
                success: true,
                duration,
                memoryBefore: (beforeCleanup - startMemory) / 1024 / 1024,
                memoryAfter: (afterCleanup - startMemory) / 1024 / 1024,
                standardCleared,
                streamingCleared,
                details: 'Large collections created and cleaned up successfully'
            };
        } catch (error) {
            return {
                test: 'Large Collection Cleanup',
                success: false,
                error: error.message,
                duration: Date.now() - startTime
            };
        }
    }

    /**
     * Test streaming cleanup
     */
    async testStreamingCleanup() {
        console.log('🧪 Testing streaming cleanup...');
        const startTime = Date.now();

        try {
            const massiveArray = new Array(50000).fill().map((_, i) => ({ 
                id: i, 
                data: new Array(100).fill(`data_${i}`) 
            }));

            const beforeStreaming = process.memoryUsage().heapUsed;

            memoryCleanup.clearLargeCollections([massiveArray], { 
                streaming: true, 
                batchSize: 2000 
            });

            const streamingComplete = await this.waitForStreamingCleanup(30000); // 30 second timeout

            const afterStreaming = process.memoryUsage().heapUsed;
            const duration = Date.now() - startTime;

            return {
                test: 'Streaming Cleanup',
                success: streamingComplete,
                duration,
                memoryFreed: (beforeStreaming - afterStreaming) / 1024 / 1024,
                details: streamingComplete ? 'Streaming cleanup completed successfully' : 'Streaming cleanup timed out'
            };
        } catch (error) {
            return {
                test: 'Streaming Cleanup',
                success: false,
                error: error.message,
                duration: Date.now() - startTime
            };
        }
    }

    /**
     * Test garbage collection effectiveness
     */
    async testGarbageCollectionEffectiveness() {
        console.log('🧪 Testing garbage collection effectiveness...');
        const startTime = Date.now();

        try {
            const memoryHogs = [];
            for (let i = 0; i < 1000; i++) {
                memoryHogs.push(new Array(1000).fill(`memory_hog_${i}`));
            }

            const beforeGC = process.memoryUsage().heapUsed;

            memoryHogs.length = 0;

            const singleGCResult = memoryCleanup.forceGarbageCollection({ cycles: 1 });

            const multiGCResult = memoryCleanup.forceGarbageCollection({ cycles: 3 });

            const afterGC = process.memoryUsage().heapUsed;
            const duration = Date.now() - startTime;

            return {
                test: 'Garbage Collection Effectiveness',
                success: true,
                duration,
                memoryBefore: beforeGC / 1024 / 1024,
                memoryAfter: afterGC / 1024 / 1024,
                singleGCFreed: singleGCResult ? singleGCResult.freedMB : 0,
                multiGCFreed: multiGCResult ? multiGCResult.freedMB : 0,
                totalFreed: (beforeGC - afterGC) / 1024 / 1024,
                details: 'Garbage collection cycles tested successfully'
            };
        } catch (error) {
            return {
                test: 'Garbage Collection Effectiveness',
                success: false,
                error: error.message,
                duration: Date.now() - startTime
            };
        }
    }

    /**
     * Test weak reference management
     */
    async testWeakReferenceManagement() {
        console.log('🧪 Testing weak reference management...');
        const startTime = Date.now();

        try {
            const testObjects = [];
            for (let i = 0; i < 100; i++) {
                const obj = { id: i, data: new Array(100).fill(`test_${i}`) };
                testObjects.push(obj);
                
                memoryCleanup.trackWeakReference(`test_${i}`, obj, () => {
                    console.log(`Cleanup callback for test_${i}`);
                });
            }

            testObjects.length = 0;

            memoryCleanup.forceGarbageCollection({ cycles: 2 });

            const cleanedCount = memoryCleanup.cleanupWeakReferences();

            const duration = Date.now() - startTime;

            return {
                test: 'Weak Reference Management',
                success: true,
                duration,
                trackedObjects: 100,
                cleanedReferences: cleanedCount,
                details: 'Weak references tracked and cleaned up successfully'
            };
        } catch (error) {
            return {
                test: 'Weak Reference Management',
                success: false,
                error: error.message,
                duration: Date.now() - startTime
            };
        }
    }

    
    async testSearchMemoryManagement() {
        console.log('🧪 Testing search memory management...');
        const startTime = Date.now();

        try {
            const broadSearch = searchMemoryManager.startSearchOperation('test-broad', 'broad', 'global');
            const specificSearch = searchMemoryManager.startSearchOperation('test-specific', 'specific', 'specific-term');

            const broadCheck = searchMemoryManager.checkSearchMemory('test-broad', 'processing');
            const specificCheck = searchMemoryManager.checkSearchMemory('test-specific', 'processing');

            const searchData = {
                allPlans: new Array(5000).fill().map((_, i) => ({ id: i, name: `plan_${i}` })),
                processedPlans: new Array(1000).fill().map((_, i) => ({ id: i, processed: true })),
                countries: new Array(200).fill().map((_, i) => ({ id: i, name: `country_${i}` })),
                processedRegions: new Array(50).fill().map((_, i) => ({ id: i, name: `region_${i}` }))
            };

            const cleanupResult = searchMemoryManager.cleanupSearchData(searchData, 'broad');

            searchMemoryManager.completeSearchOperation('test-broad', { resultCount: 5000 });
            searchMemoryManager.completeSearchOperation('test-specific', { resultCount: 1000 });

            const duration = Date.now() - startTime;

            return {
                test: 'Search Memory Management',
                success: broadCheck.canContinue && specificCheck.canContinue,
                duration,
                broadSearchCheck: broadCheck,
                specificSearchCheck: specificCheck,
                cleanupResult,
                details: 'Search memory management tested successfully'
            };
        } catch (error) {
            return {
                test: 'Search Memory Management',
                success: false,
                error: error.message,
                duration: Date.now() - startTime
            };
        }
    }

   
    async testPredictiveMemoryManagement() {
        console.log('🧪 Testing predictive memory management...');
        const startTime = Date.now();

        try {
            const testOperations = [
                { type: 'search', searchType: 'broad', searchTerm: 'global', memoryIncrease: 150, duration: 5000, success: true },
                { type: 'search', searchType: 'specific', searchTerm: 'specific-term', memoryIncrease: 50, duration: 2000, success: true },
                { type: 'search', searchType: 'broad', searchTerm: 'premium', memoryIncrease: 200, duration: 8000, success: false, abortReason: 'memory_limit' }
            ];

            for (const op of testOperations) {
                predictiveMemoryManager.recordOperation(op);
            }

            const broadPrediction = predictiveMemoryManager.predictMemoryUsage({
                type: 'search',
                searchType: 'broad',
                searchTerm: 'global',
                resultCount: 5000
            });

            const specificPrediction = predictiveMemoryManager.predictMemoryUsage({
                type: 'search',
                searchType: 'specific',
                searchTerm: 'test-term',
                resultCount: 1000
            });

            const currentMemory = process.memoryUsage().heapUsed / 1024 / 1024;
            const shouldProceedBroad = predictiveMemoryManager.shouldProceedWithOperation({
                type: 'search',
                searchType: 'broad',
                searchTerm: 'global'
            }, currentMemory);

            const insights = predictiveMemoryManager.getMemoryInsights();

            const duration = Date.now() - startTime;

            return {
                test: 'Predictive Memory Management',
                success: true,
                duration,
                broadPrediction,
                specificPrediction,
                shouldProceedBroad,
                insights,
                details: 'Predictive memory management tested successfully'
            };
        } catch (error) {
            return {
                test: 'Predictive Memory Management',
                success: false,
                error: error.message,
                duration: Date.now() - startTime
            };
        }
    }

   
    async waitForStreamingCleanup(timeout = 10000) {
        return new Promise((resolve) => {
            const startTime = Date.now();
            
            const checkComplete = () => {
                if (!memoryCleanup.isStreamingCleanup) {
                    resolve(true);
                } else if (Date.now() - startTime > timeout) {
                    resolve(false);
                } else {
                    setTimeout(checkComplete, 100);
                }
            };
            
            checkComplete();
        });
    }

    generateTestSummary(results) {
        const totalTests = results.length;
        const passedTests = results.filter(r => r.success).length;
        const failedTests = totalTests - passedTests;
        const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);

        return {
            totalTests,
            passedTests,
            failedTests,
            successRate: (passedTests / totalTests) * 100,
            totalDuration,
            averageDuration: totalDuration / totalTests,
            status: failedTests === 0 ? 'PASSED' : 'FAILED'
        };
    }

    getTestHistory() {
        return this.testResults;
    }

    clearTestHistory() {
        this.testResults = [];
    }
}

module.exports = new MemoryTestUtils();
