/**
 * Final test to verify planInfo is now working correctly in sync
 */

const { EsimPlan, Provider } = require('./src/models');
const cronService = require('./src/services/cron.service.js');

async function testFinalSync() {
    try {
        console.log('Testing final planInfo sync fix...');
        
        // Find a specific plan that we know should have planInfo
        const testPlan = await EsimPlan.findOne({
            where: {
                name: { [require('sequelize').Op.like]: '%TopUp Bolivia%' }
            },
            include: [{
                model: Provider,
                as: 'provider',
                where: { name: 'Mobimatter' }
            }]
        });
        
        if (!testPlan) {
            console.log('Test plan not found');
            return;
        }
        
        console.log('Found test plan:', testPlan.name);
        console.log('Current planInfo:', testPlan.planInfo ? 'Has content' : 'Empty');
        
        // Clear the planInfo to test the fix
        await testPlan.update({ planInfo: null });
        console.log('Cleared planInfo for testing');
        
        // Run sync for just Mobimatter
        console.log('Running Mobimatter sync...');
        const result = await cronService.syncExternalPlans();
        
        // Reload the plan
        await testPlan.reload();
        
        console.log('After sync:');
        console.log('- planInfo:', testPlan.planInfo ? 'Has content' : 'Empty');
        
        if (testPlan.planInfo) {
            console.log('- Content preview:', testPlan.planInfo.substring(0, 150) + '...');
            console.log('✅ SUCCESS: planInfo is now being generated and saved correctly!');
        } else {
            console.log('❌ FAILED: planInfo is still empty after sync');
        }
        
    } catch (error) {
        console.error('Error in final sync test:', error);
    } finally {
        process.exit(0);
    }
}

testFinalSync();
