-- Use the database
USE esim_demo;

-- Drop existing tables and constraints
SET FOREIGN_KEY_CHECKS=0;

-- Drop existing tables
DROP TABLE IF EXISTS esimplanstockhistory;
DROP TABLE IF EXISTS esimstocks;
DROP TABLE IF EXISTS esimplancountries;
DROP TABLE IF EXISTS esimplans;
DROP TABLE IF EXISTS providers;
DROP TABLE IF EXISTS cart;
DROP TABLE IF EXISTS orders;
DROP TABLE IF EXISTS knowledgebase;


SET FOREIGN_KEY_CHECKS=1;

DROP FUNCTION IF EXISTS generate_product_id;

-- Create Providers table
CREATE TABLE IF NOT EXISTS providers (
    id CHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    type ENUM('API', 'Custom') NOT NULL DEFAULT 'Custom',
    country VARCHAR(100),
    merchantId VARCHAR(255),
    apiEndpoint VARCHAR(255),
    apiKey VARCHAR(255),
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    description TEXT,
    configData JSON,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB;


ALTER TABLE providers
MODIFY createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE providers 
MODIFY updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP;



-- Create EsimPlans table 
CREATE TABLE IF NOT EXISTS esimplans (
    id CHAR(36) PRIMARY KEY,
    productId CHAR(12) NOT NULL,
    externalProductId VARCHAR(255),
    externalSkuId VARCHAR(255),
    name VARCHAR(100) NOT NULL,
    instructions TEXT,
    description TEXT,
    PlanInfo TEXT,
    additionalInfo TEXT,
    providerId CHAR(36),
    networkName VARCHAR(100) NOT NULL,
    networkType VARCHAR(50) DEFAULT '4G/LTE',
    region TEXT,
    supportedRegions JSON, 
    buyingPrice DECIMAL(10, 2) NOT NULL,
    sellingPrice DECIMAL(10, 2),
    validityDays INT NOT NULL,
    planType ENUM('Fixed', 'Unlimited', 'Custom') NOT NULL DEFAULT 'Fixed',
    category ENUM('esim_realtime', 'esim_addon', 'esim_replacement') NOT NULL DEFAULT 'esim_realtime',
    planCategory ENUM('Voice and Data', 'Data Only') NOT NULL DEFAULT 'Data Only',
    is_voice ENUM('Available', 'Not Available') NOT NULL DEFAULT 'Not Available',
    voiceMin INT NULL,
    voiceMinUnit ENUM('Min', 'Hr', 'Sec') NULL,
    planData DECIMAL(10, 1) NULL,
    planDataUnit ENUM('MB', 'GB', 'TB') NULL,
    customPlanData TEXT,
    is_sms ENUM('Available', 'Not Available') NOT NULL DEFAULT 'Not Available',
    sms INT NULL,
    startDateEnabled BOOLEAN NOT NULL DEFAULT FALSE,
    stockThreshold INT NOT NULL DEFAULT 10 
    CHECK (stockThreshold BETWEEN 1 AND 1000),
    hotspot ENUM('Available', 'Not Available') NOT NULL DEFAULT 'Available',
    activationPolicy ENUM('Activation upon purchase', 'Activation upon first usage', 'Activation upon travel date') NOT NULL DEFAULT 'Activation upon purchase',  
    speed ENUM('Restricted', 'Unrestricted') NOT NULL DEFAULT 'Unrestricted',
    status ENUM('visible', 'hidden') NOT NULL DEFAULT 'visible',
    providerMetadata JSON,
    features JSON,
    isActive BOOLEAN DEFAULT TRUE,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    profile ENUM('local', 'roaming') NOT NULL DEFAULT 'local',
    top_up ENUM('Available', 'Not Available') NOT NULL DEFAULT 'Not Available',  
    UNIQUE INDEX idx_product_id (productId),
    INDEX idx_provider_id (providerId),
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_top_up (top_up),
    INDEX idx_planCategory (planCategory),
    INDEX idx_name (name),
    INDEX idx_esimplans_category_status_region (category, status, region(50), name(100)),
    FOREIGN KEY (providerId) REFERENCES providers(id) ON DELETE SET NULL ON UPDATE CASCADE,
    CHECK ((planData IS NULL AND planDataUnit IS NULL) OR (planData IS NOT NULL AND planDataUnit IS NOT NULL)),
    CHECK (planCategory = 'Data Only' AND voiceMin IS NULL OR planCategory = 'Voice and Data'),
    -- Allow zero validity days for replacement eSIMs
    CHECK (validityDays > 0 OR category = 'esim_replacement'),
    CHECK  (planType != 'Fixed' OR planData > 0 OR category IN ('esim_addon', 'esim_replacement'))
) ENGINE=InnoDB;

ALTER TABLE esimplans
MODIFY createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE esimplans 
MODIFY updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP;


-- Create trigger to generate UUID for Providers
DELIMITER //
CREATE TRIGGER before_insert_provider
BEFORE INSERT ON providers
FOR EACH ROW
BEGIN
    IF NEW.id IS NULL THEN
        SET NEW.id = UUID();
    END IF;
END//
DELIMITER ;


-- Create EsimPlanCountries junction table
CREATE TABLE IF NOT EXISTS esimplancountries (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    esimPlanId CHAR(36) NOT NULL,
    countryId VARCHAR(36) NOT NULL,
    isDefault BOOLEAN DEFAULT FALSE,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_plan_country (esimPlanId, countryId),
    FOREIGN KEY (esimPlanId) REFERENCES esimplans(id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (countryId) REFERENCES countries(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB;

ALTER TABLE esimplancountries
MODIFY createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE esimplancountries 
MODIFY updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP;


-- Create EsimStocks table
CREATE TABLE IF NOT EXISTS esimstocks (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    externalStockId VARCHAR(255),      -- Provider's stock/inventory ID
    externalIccid VARCHAR(255),        -- Provider's ICCID
    externalProfileId VARCHAR(255),    -- Provider's profile identifier
    providerStatus VARCHAR(255),       -- Provider's status for this eSIM
    activationCode VARCHAR(255),       -- Activation code from provider
    qrCodeUrl TEXT,                    -- URL to QR code image
    lastUsageSync TIMESTAMP,           -- Last time usage was synced
    esimPlanId CHAR(36) NOT NULL,
    lpaString VARCHAR(255) NOT NULL,
    iccid VARCHAR(255) NOT NULL UNIQUE,
    smdpAddress VARCHAR(255) NOT NULL,
    accessPointName VARCHAR(100) NOT NULL,
    phoneNumber VARCHAR(100),
    confCode VARCHAR(100),
    pin VARCHAR(100),
    walletAuthTransactionId VARCHAR(100),
    orderId VARCHAR(20),
    orderDate DATETIME DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    providerMetadata JSON,
    providerActivationDetails JSON,         -- Store activation-related data
    lastProviderSync TIMESTAMP,
    status ENUM('available', 'assigned', 'activated', 'expired') NOT NULL DEFAULT 'available',
    createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_external_iccid (externalIccid),
    FOREIGN KEY (esimPlanId) REFERENCES esimplans(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    INDEX idx_esimplan_id (esimPlanId),
    INDEX idx_status (status)
) ENGINE=InnoDB;


ALTER TABLE esimstocks
MODIFY createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE esimstocks 
MODIFY updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Create Orders table
CREATE TABLE IF NOT EXISTS orders (
    id VARCHAR(20) PRIMARY KEY,
    parentOrderId VARCHAR(255) NULL,
    externalOrderId VARCHAR(255),
    userId CHAR(36) NOT NULL,
    esimPlanId CHAR(36) NOT NULL,
    esimStockId VARCHAR(36),
    quantity INT NOT NULL DEFAULT 1,
    orderTotal DECIMAL(10, 2) NOT NULL,
    startDate DATE,
    validTime DATETIME,
    status ENUM('pending', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'pending',
    providerResponse JSON,
    providerMetadata JSON,
    walletAuthTransactionId VARCHAR(255),
    externalTransactionId VARCHAR(255),
    usageData JSON, 
    dataUsage BIGINT NULL,
    dataAllowance BIGINT NULL,
    usageStatus ENUM('Active', 'Data Depleted', 'Expired', 'Not Available', 'Unknown') NULL,
    expiryDate DATETIME NULL,
    usageMessage TEXT NULL,
    lastUsageCheck TIMESTAMP,
    providerOrderStatus VARCHAR(255), 
    providerErrorCode VARCHAR(50),         
    providerErrorMessage TEXT,              
    lastProviderCheck TIMESTAMP,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (esimPlanId) REFERENCES esimplans(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (esimStockId) REFERENCES esimstocks(id) ON DELETE SET NULL ON UPDATE CASCADE,
    INDEX idx_external_order (externalOrderId),
    INDEX idx_data_usage (dataUsage),
    INDEX idx_usage_status (usageStatus),
    INDEX idx_expiry_date (expiryDate)
) ENGINE=InnoDB;


ALTER TABLE esimstocks
ADD CONSTRAINT fk_esimstocks_orderId
FOREIGN KEY (orderId) REFERENCES orders(id)
ON DELETE SET NULL ON UPDATE CASCADE;



-- Create EsimPlanStockHistory table
CREATE TABLE IF NOT EXISTS esimplanstockhistory (
    id VARCHAR(36) PRIMARY KEY,
    esimPlanId CHAR(36) NOT NULL,
    esimStockId VARCHAR(36) NOT NULL,
    iccid VARCHAR(50) NOT NULL,
    smdpAddress VARCHAR(255) NOT NULL,
    lpaString TEXT NOT NULL,
    accessPointName VARCHAR(255),
    activationCode VARCHAR(255),
    phoneNumber VARCHAR(20),
    orderId VARCHAR(20),
    orderDate TIMESTAMP,
    quantity INT NOT NULL,
    providerActivationDetails JSON,         -- Store activation-related data
    lastProviderSync TIMESTAMP,
    status ENUM('assigned', 'activated', 'expired', 'cancelled') NOT NULL DEFAULT 'assigned',
    reason VARCHAR(255) NOT NULL DEFAULT 'Order placement',
    createdBy VARCHAR(36) NOT NULL,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (esimPlanId) REFERENCES esimplans(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (orderId) REFERENCES orders(id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (createdBy) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE,
    UNIQUE KEY unique_order_stock_history (orderId),
    INDEX idx_esimplan_id (esimPlanId),
    INDEX idx_order_id (orderId),
    INDEX idx_iccid (iccid),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- Create Cart table
CREATE TABLE IF NOT EXISTS cart (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    userId CHAR(36) NOT NULL,
    esimPlanId CHAR(36) NOT NULL,
    isTopUp BOOLEAN DEFAULT FALSE,
    parentOrderId VARCHAR(255) NULL,
    quantity INT DEFAULT 1,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (esimPlanId) REFERENCES esimplans(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_cart (userId, esimPlanId)
) ENGINE=InnoDB;


-- Create KnowledgeBase table
CREATE TABLE IF NOT EXISTS knowledgebase (
    id VARCHAR(36) NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    category ENUM('general', 'technical', 'billing', 'support', 'faq') NOT NULL DEFAULT 'general',
    visibility ENUM('public', 'partner', 'admin') NOT NULL DEFAULT 'public',
    status ENUM('draft', 'published', 'archived') NOT NULL DEFAULT 'draft',
    authorId CHAR(36) COLLATE utf8mb4_unicode_ci NOT NULL,
    createdAt DATETIME NOT NULL,
    updatedAt DATETIME NOT NULL,
    PRIMARY KEY (id),
    KEY idx_category (category),
    KEY idx_visibility (visibility),
    KEY idx_status (status),
    KEY idx_author (authorId),
    CONSTRAINT fk_kb_author FOREIGN KEY (authorId) REFERENCES users (id) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Add index for faster lookups
CREATE INDEX idx_orders_user ON orders(userId);
CREATE INDEX idx_orders_plan ON orders(esimPlanId);
CREATE INDEX idx_orders_stock ON orders(esimStockId);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_external_product_id ON esimplans(externalProductId);
CREATE INDEX idx_external_stock_id ON esimstocks(externalStockId);
CREATE INDEX idx_external_order_id ON orders(externalOrderId);
CREATE INDEX idx_external_transaction_id ON orders(externalTransactionId);
CREATE INDEX idx_region ON esimplans(region(255));

-- Index for better performance
CREATE INDEX idx_stockhistory_planid ON esimplanstockhistory(esimPlanId);

-- Trigger to generate a unique productId before inserting into EsimPlans
DROP TRIGGER IF EXISTS before_esimplan_insert;

DELIMITER //
CREATE TRIGGER before_esimplan_insert
BEFORE INSERT ON esimplans
FOR EACH ROW
BEGIN
    DECLARE attempts INT DEFAULT 0;
    DECLARE new_product_id CHAR(12);

    -- If no productId is provided, generate one
    IF NEW.productId IS NULL THEN
        -- Attempt up to 10 times to generate a unique productId
        WHILE attempts < 10 DO
            SET new_product_id = generate_product_id();

            -- Ensure uniqueness
            IF NOT EXISTS (SELECT 1 FROM esimplans WHERE productId = new_product_id) THEN
                SET NEW.productId = new_product_id;
                SET attempts = 10; -- Exit loop
            END IF;

            SET attempts = attempts + 1;
        END WHILE;
    END IF;

    -- Validate that selling price is greater than buying price, except for replacement eSIMs
    IF NEW.sellingPrice IS NOT NULL AND NEW.sellingPrice <= NEW.buyingPrice AND NEW.category != 'esim_replacement' THEN
        SIGNAL SQLSTATE '45000'
        SET MESSAGE_TEXT = 'Selling price must be greater than buying price';
    END IF;
END;
//
DELIMITER ;

-- Trigger to validate EsimPlans updates
DROP TRIGGER IF EXISTS before_esimplan_update;

DELIMITER //
CREATE TRIGGER before_esimplan_update
BEFORE UPDATE ON esimplans
FOR EACH ROW
BEGIN
    -- Ensure sellingPrice is greater than buyingPrice when updating, except for replacement eSIMs
    IF NEW.sellingPrice IS NOT NULL AND NEW.sellingPrice <= NEW.buyingPrice AND NEW.category != 'esim_replacement' THEN
        SIGNAL SQLSTATE '45000'
        SET MESSAGE_TEXT = 'Selling price must be greater than buying price';
    END IF;

    -- Ensure validity days is positive, except for replacement eSIMs
    IF NEW.validityDays <= 0 AND NEW.category != 'esim_replacement' THEN
        SIGNAL SQLSTATE '45000'
        SET MESSAGE_TEXT = 'Validity days must be greater than zero';
    END IF;
END;
//
DELIMITER ;

DELIMITER //

CREATE FUNCTION generate_product_id()
RETURNS CHAR(12) DETERMINISTIC
BEGIN
    DECLARE result CHAR(12);
    DECLARE letters CHAR(26) DEFAULT 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    DECLARE digits CHAR(10) DEFAULT '0123456789';
    DECLARE i INT DEFAULT 1;
    
    SET result = '';
    
    -- Generate 6 random letters
    WHILE i <= 6 DO
        SET result = CONCAT(result, SUBSTRING(letters, FLOOR(1 + RAND() * 26), 1));
        SET i = i + 1;
    END WHILE;
    
    -- Generate 6 random digits
    SET i = 1;
    WHILE i <= 6 DO
        SET result = CONCAT(result, SUBSTRING(digits, FLOOR(1 + RAND() * 10), 1));
        SET i = i + 1;
    END WHILE;
    
    RETURN result;
END//

DELIMITER ;


DELIMITER //

CREATE TRIGGER before_insert_esimplans
BEFORE INSERT ON esimplans
FOR EACH ROW
BEGIN
    DECLARE new_id CHAR(12);
    DECLARE done INT DEFAULT 0;

    WHILE done = 0 DO
        SET new_id = generate_product_id();

        IF NOT EXISTS (SELECT 1 FROM esimplans WHERE productId = new_id) THEN
            SET NEW.productId = new_id;
            SET done = 1;
        END IF;
    END WHILE;
END//

DELIMITER ;



-- Add optimized indexes for better performance
CREATE INDEX idx_esimplans_search ON esimplans(name, productId, status);
CREATE INDEX idx_esimplans_filters ON esimplans(status, category, region(50));
CREATE INDEX idx_esimplans_provider ON esimplans(providerId, status);
CREATE INDEX idx_esimplans_combined ON esimplans(status, category, region(50), name(100));

-- Add additional indexes for common query patterns
CREATE INDEX idx_esimplans_isactive ON esimplans(isActive);
CREATE INDEX idx_esimplans_status_isactive ON esimplans(status, isActive);
CREATE INDEX idx_esimplans_category_status ON esimplans(category, status, isActive);