const { EsimPlan, Country, User, EsimPlanCountries, Provider, Feature } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');
const { v4: uuidv4 } = require('uuid');
const { sendLowStockEmail } = require('../utils/emailService');
const providerFactory = require('../services/provider.factory');
const generateProductId = require('../utils/generateProductId');
const { getCachedPlan, setCachePlan, invalidatePlanCache } = require('../utils/cacheManager');
const exchangeRateService = require('../services/exchangeRate.service');
const productCacheService = require('../services/productCache.service');
const memoryMonitor = require('../utils/memoryMonitor');
const emergencyMemoryProtection = require('../utils/emergencyMemoryProtection');
const detailedMemoryProfiler = require('../utils/detailedMemoryProfiler');
const memoryCleanup = require('../utils/memoryCleanup');
const searchMemoryManager = require('../utils/searchMemoryManager');
const predictiveMemoryManager = require('../utils/predictiveMemoryManager');

// Helper function to check stock and notify admins if needed
const checkStockAndNotify = async (planId) => {
    try {
        // Get plan details with current stock count
        const plan = await EsimPlan.findByPk(planId, {
            include: [
                {
                    model: sequelize.models.EsimStock,
                    as: 'stocks',
                    where: { status: 'available' },
                    required: false
                }
            ]
        });

        if (!plan) return;

        const currentStock = plan.stocks?.length || 0;

        // Check if stock is below threshold
        if (currentStock < plan.stockThreshold) {
            const admins = await User.findAll({
                where: { role: 'admin', isActive: true },
                attributes: ['email']
            });

            const adminEmails = admins.map(admin => admin.email);

            // Send notification
            await sendLowStockEmail(adminEmails, plan, currentStock);
        }
    } catch (error) {
        console.error('Error in stock check and notification:', error);
    }
};

const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const isProviderPlansHidden = async (providerId) => {
    if (!providerId) return false;

    try {
        const visiblePlanCount = await EsimPlan.count({
            where: {
                providerId: providerId,
                status: 'visible',
                isActive: true
            }
        });

        const totalPlanCount = await EsimPlan.count({
            where: {
                providerId: providerId,
                isActive: true
            }
        });

        return totalPlanCount > 0 && visiblePlanCount === 0;
    } catch (error) {
        console.error('Error checking provider plan visibility:', error);
        return false;
    }
};

// Get all eSIM plans (including external provider plans)
exports.getAllPlans = async (req, res) => {
    try {
        const allPlans = await EsimPlan.findAll({
            where: { isActive: true },
            include: [{
                model: Provider,
                as: 'provider'
            }]
        });

        res.json({
            success: true,
            data: allPlans
        });
    } catch (error) {
        console.error('Error fetching plans:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch plans',
            error: error.message
        });
    }
};

// Get plan by ID (works for both local and external plans)
exports.getPlanById = async (req, res) => {
    try {
        const { id } = req.params;

        // First try to find local plan
        let plan = await EsimPlan.findOne({
            where: { id },
            include: [{
                model: Provider,
                as: 'provider'
            }]
        });

        // If not found locally, check if it's an external plan
        if (!plan) {
            const providers = await Provider.findAll({ where: { type: 'API', status: 'active' } });

            for (const provider of providers) {
                try {
                    const providerService = providerFactory.getProvider(provider.name);
                    const externalPlan = await providerService.getProductDetails(id);

                    if (externalPlan) {
                        plan = {
                            ...providerFactory.standardizeProduct(provider.name, externalPlan),
                            providerId: provider.id,
                            provider: provider
                        };
                        break;
                    }
                } catch (error) {
                    console.error(`Error fetching plan from provider ${provider.name}:`, error);
                }
            }
        }

        if (!plan) {
            return res.status(404).json({ message: 'Plan not found' });
        }

        res.json(plan);
    } catch (error) {
        console.error('Error fetching plan:', error);
        res.status(500).json({ message: 'Failed to fetch plan details' });
    }
};

// Search plans (works for both local and external plans)
exports.searchPlans = async (req, res) => {
    try {
        const { search, countryId, region, category, page = 1, limit = 50 } = req.query;

        let whereClause = {
                isActive: true,
            isDeleted: false
        };

        // Add category filter if specified
        if (category) {
            whereClause.category = category;
        }

      // Add country filter if specified
        if (countryId) {
            whereClause['$countries.id$'] = countryId;
        }

        // Add region filter if specified
        if (region) {
            whereClause.region = region;
        }

        const searchConditions = [];
        if (search) {
            const searchTerms = search.toLowerCase().split(' ');
            searchConditions.push(
                ...searchTerms.map(term => ({
                    [Op.or]: [
                        { name: { [Op.iLike]: `%${term}%` } },
                        { networkName: { [Op.iLike]: `%${term}%` } },
                        { planType: { [Op.iLike]: `%${term}%` } },
                        { planCategory: { [Op.iLike]: `%${term}%` } },
                        { region: { [Op.iLike]: `%${term}%` } },
                        { '$countries.name$': { [Op.iLike]: `%${term}%` } },
                        { '$features.name$': { [Op.iLike]: `%${term}%` } }
                    ]
                }))
            );
        }

        if (searchConditions.length > 0) {
            whereClause = {
                [Op.and]: [
                    whereClause,
                    ...searchConditions
                ]
            };
        }

        // Fetch plans with pagination
        const { count, rows: plans } = await EsimPlan.findAndCountAll({
            where: whereClause,
            include: [
                {
                    model: Country,
                    as: 'countries',
                    through: { attributes: [] },
                    required: false
                },
                {
                    model: Feature,
                    as: 'features',
                    through: { attributes: [] },
                    required: false
                }
            ],
            order: [['createdAt', 'DESC']],
            limit: parseInt(limit),
            offset: (parseInt(page) - 1) * parseInt(limit),
            distinct: true
        });

        const totalPages = Math.ceil(count / limit);

        res.json({
            plans,
            total: count,
            totalPages,
            currentPage: parseInt(page),
            hasMore: parseInt(page) < totalPages
        });
    } catch (error) {
        console.error('Error searching plans:', error);
        res.status(500).json({ error: 'Failed to search plans' });
    }
};

// Get all eSIM plans
exports.getEsimPlans = async (req, res) => {
    try {
        const {
            search,
            status,
            category,
            provider,
            providerType,
            countryId,
            region,
            sortBy: reqSortBy = 'createdAt',
            sortOrder: reqSortOrder = 'DESC',
            page = 1,
            limit = 10,
            isActive
        } = req.query;

        // Validate sortBy parameter against model columns
        const validSortColumns = [
            'createdAt', 'updatedAt', 'name', 'networkName', 'buyingPrice', 
            'sellingPrice', 'validityDays', 'planData', 'stockThreshold'
        ];
        
        let validatedSortBy;
        let validatedSortOrder;
        
        // Map 'oldest' to 'createdAt' ASC for backward compatibility
        if (reqSortBy === 'oldest') {
            validatedSortBy = 'createdAt';
            validatedSortOrder = 'ASC';
        } else if (!validSortColumns.includes(reqSortBy)) {
            validatedSortBy = 'createdAt'; 
            validatedSortOrder = reqSortOrder;
        } else {
            validatedSortBy = reqSortBy;
            validatedSortOrder = reqSortOrder;
        }

        // Create cache key for this specific request
        const cacheKey = `admin_esim_plans_${JSON.stringify({
            search, status, category, provider, providerType, countryId, region, sortBy: reqSortBy, sortOrder: reqSortOrder, page, limit, isActive
        })}`;

        // Check cache first
        const cachedData = getCachedPlan(cacheKey);
        if (cachedData) {
            console.log(`🚀 Cache HIT for admin esim plans`);
            return res.json(cachedData);
        }

        // Log memory status before heavy query
        memoryMonitor.logMemoryStatus();
        console.log(`💾 Cache MISS for admin esim plans, generating fresh data...`);

        // Get local plans
        let whereClause = {
            // Only show active plans by default, unless isActive is explicitly set
            isActive: isActive !== undefined ? (isActive === 'true') : true
        };

        // Add default status filter if not specified in the request
        if (!status) {
            whereClause.status = 'visible';
        }

        const include = [
            {
                model: Country,
                as: 'countries',
                through: { attributes: ['isDefault'] }
            },
            {
                model: Provider,
                as: 'provider',
                attributes: ['id', 'name', 'type', 'country']
            }
        ];

        // Search filter (case-insensitive)
        if (search) {
            whereClause[Op.or] = [
                { name: { [Op.iLike]: `%${search}%` } },
                { productId: { [Op.iLike]: `%${search}%` } }
            ];
        }

        // Status filter
        if (status) {
            whereClause.status = status;
        }

        // Category filter
        if (category) {
            whereClause.category = category;
        }

        // Provider filter
        if (provider) {
            whereClause.providerId = provider;
        }

        // Provider type filter
        if (providerType) {
            include[1].where = { type: providerType };
        }

        // Country filter
        if (countryId) {
            include[0].where = { id: countryId };
        }

        // Region filter (case-insensitive)
        if (region && region !== 'all') {
            whereClause.region = { [Op.iLike]: `%${region}%` };
        }

        // Get total count first
        const totalCount = await EsimPlan.count({
            where: whereClause,
            include: include,
            distinct: true
        });

        // Calculate pagination
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const offset = (pageNum - 1) * limitNum;
        const totalPages = Math.ceil(totalCount / limitNum);

        // Validate page number
        if (pageNum > totalPages && totalCount > 0) {
            return res.status(400).json({
                message: 'Invalid page number',
                currentPage: pageNum,
                totalPages: totalPages
            });
        }

        // Get paginated plans
        const plans = await EsimPlan.findAll({
            where: whereClause,
            include: include,
            order: [[validatedSortBy, validatedSortOrder]],
            limit: limitNum,
            offset: offset,
            distinct: true
        });

        // Get all countries and regions for filters
        const countries = await Country.findAll({
            where: { isActive: true },
            attributes: ['id', 'name', 'iso3'],
            order: [['name', 'ASC']]
        });

        const regions = await EsimPlan.findAll({
            attributes: [
                [sequelize.fn('DISTINCT', sequelize.col('region')), 'region']
            ],
            where: {
                region: { [Op.not]: null }
            },
            raw: true
        });

        const processedRegions = regions
            .map(r => r.region)
            .filter(Boolean)
            .flatMap(region => region.split(',').map(r => r.trim()))
            .filter((value, index, self) => self.indexOf(value) === index)
            .sort();

        // Get stock counts for local plans
        const stockCounts = await sequelize.models.EsimStock.findAll({
            attributes: [
                'esimPlanId',
                [sequelize.fn('COUNT', sequelize.col('id')), 'stockCount']
            ],
            where: {
                esimPlanId: plans.map(plan => plan.id),
                status: 'available'
            },
            group: ['esimPlanId'],
            raw: true
        });

        // Create a map of plan ID to stock count
        const stockCountMap = stockCounts.reduce((acc, stock) => {
            acc[stock.esimPlanId] = parseInt(stock.stockCount);
            return acc;
        }, {});

        // Add stock counts to local plans
        const plansWithStock = plans.map(plan => {
            const planData = typeof plan.toJSON === 'function' ? plan.toJSON() : plan;
            
            if (planData.provider?.type === 'API') {
                planData.stockCount = "Unlimited";
            } else {
                planData.stockCount = stockCountMap[planData.id] || 0;
            }
            
            return planData;
        });

        const responseData = {
            plans: plansWithStock,
            page: pageNum,
            totalPages,
            totalItems: totalCount,
            countries,
            regions: processedRegions
        };

        // Cache for 5 minutes
        setCachePlan(cacheKey, responseData, 300);

        console.log(`✅ Generated fresh admin esim plans data (${plansWithStock.length} plans)`);
        memoryMonitor.logMemoryStatus();

        res.json(responseData);
    } catch (error) {
        console.error('Error fetching eSIM plans:', error);
        res.status(500).json({
            message: 'Failed to fetch eSIM plans',
            error: error.message
        });
    }
};

// Get a single eSIM plan by ID
exports.getEsimPlan = async (req, res) => {
    try {
        const plan = await EsimPlan.findByPk(req.params.id, {
            include: [
                {
                    model: Country,
                    as: 'countries',
                    through: { attributes: ['isDefault'] }
                },
                {
                    model: Provider,
                    as: 'provider',
                    attributes: ['id', 'name', 'type', 'country']
                }
            ]
        });

        if (!plan) {
            return res.status(404).json({ message: 'eSIM plan not found' });
        }

        res.json(plan);
    } catch (error) {
        console.error('Error fetching eSIM plan:', error);
        res.status(500).json({ message: 'Failed to fetch eSIM plan' });
    }
};

// Create new eSIM plan
exports.createEsimPlan = async (req, res) => {
    try {
        const {
            countries,
            defaultCountryId,
            ...planData
        } = req.body;

        const effectiveDefaultCountryId = defaultCountryId || countries[0];

        const providerPlansHidden = await isProviderPlansHidden(planData.providerId);

        // Handle voiceMin for Data Only plans
        const processedPlanData = {
            ...planData,
            voiceMin: planData.planCategory === 'Data Only' ? null : planData.voiceMin,
            voiceMinUnit: planData.planCategory === 'Data Only' ? null : planData.voiceMinUnit,
            startDateEnabled: planData.startDateEnabled || false,
            is_voice: planData.planCategory === 'Voice and Data' ? 'Available' : 'Not Available',
            is_sms: planData.is_sms || 'Not Available',
            sms: planData.is_sms === 'Available' ? planData.sms : null,
            activationPolicy: planData.startDateEnabled ? 'Activation upon travel date' : (planData.activationPolicy || 'Activation upon purchase'),
            status: providerPlansHidden ? 'hidden' : (planData.status || 'visible'),
            id: uuidv4()
        };

        // Create the plan
        const plan = await EsimPlan.create(processedPlanData);

        // Associate countries with the plan
        await Promise.all(countries.map(countryId =>
            plan.addCountry(countryId, {
                through: {
                    id: uuidv4(),
                    isDefault: countryId === effectiveDefaultCountryId
                }
            })
        ));

        // Invalidate product cache since new plan was created
        productCacheService.invalidateAllProductCaches();

        // Fetch the plan with its countries and provider
        const createdPlan = await EsimPlan.findByPk(plan.id, {
            include: [
                {
                    model: Country,
                    as: 'countries',
                    through: { attributes: ['isDefault'] }
                },
                {
                    model: Provider,
                    as: 'provider',
                    attributes: ['id', 'name', 'type', 'country']
                }
            ]
        });

        res.status(201).json(createdPlan);
    } catch (error) {
        console.error('Error creating eSIM plan:', error);
        res.status(400).json({
            message: 'Failed to create eSIM plan',
            error: error.message
        });
    }
};

// Create multiple eSIM plans from bulk upload
exports.createBulkEsimPlans = async (req, res) => {
    try {
        const plansData = req.body;

        if (!Array.isArray(plansData) || plansData.length === 0) {
            return res.status(400).json({
                message: 'Invalid data format. Expected an array of plan objects.'
            });
        }

        const createdPlans = [];
        const errors = [];

        const baseTimestamp = new Date();

        // Process each plan individually to provide detailed error reporting
        for (let i = 0; i < plansData.length; i++) {
            const planData = plansData[i];
            const rowIndex = planData.rowIndex || i + 1;

            try {
                const requiredFields = ['name', 'networkName', 'buyingPrice', 'validityDays', 'countries'];
                const missingFields = requiredFields.filter(field => {
                    if (field === 'countries') {
                        return !planData.countries || !Array.isArray(planData.countries) || planData.countries.length === 0;
                    }
                    return !planData[field];
                });

                if (missingFields.length > 0) {
                    errors.push({
                        row: rowIndex,
                        error: `Missing required fields: ${missingFields.join(', ')}`
                    });
                    continue;
                }

                // Validate enum values
                const validPlanTypes = ['Fixed', 'Unlimited', 'Custom'];
                const validCategories = ['esim_realtime', 'esim_addon', 'esim_replacement'];
                const validPlanCategories = ['Voice and Data', 'Data Only'];
                const validVoiceOptions = ['Available', 'Not Available'];
                const validSmsOptions = ['Available', 'Not Available'];
                const validTopUpOptions = ['Available', 'Not Available'];
                const validHotspotOptions = ['Available', 'Not Available'];
                const validActivationPolicies = ['Activation upon purchase', 'Activation upon first usage', 'Activation upon travel date'];
                const validSpeedOptions = ['Restricted', 'Unrestricted'];
                const validDataUnits = ['MB', 'GB', 'TB'];
                const validVoiceUnits = ['Min', 'Hr', 'Sec'];

                if (planData.planType && !validPlanTypes.includes(planData.planType)) {
                    errors.push({
                        row: rowIndex,
                        error: `Invalid plan type: ${planData.planType}. Must be one of: ${validPlanTypes.join(', ')}`
                    });
                    continue;
                }

                if (planData.category && !validCategories.includes(planData.category)) {
                    errors.push({
                        row: rowIndex,
                        error: `Invalid category: ${planData.category}. Must be one of: ${validCategories.join(', ')}`
                    });
                    continue;
                }

                if (planData.planCategory && !validPlanCategories.includes(planData.planCategory)) {
                    errors.push({
                        row: rowIndex,
                        error: `Invalid plan category: ${planData.planCategory}. Must be one of: ${validPlanCategories.join(', ')}`
                    });
                    continue;
                }

                if (planData.planData && planData.planDataUnit && !validDataUnits.includes(planData.planDataUnit)) {
                    errors.push({
                        row: rowIndex,
                        error: `Invalid plan data unit: ${planData.planDataUnit}. Must be one of: ${validDataUnits.join(', ')}`
                    });
                    continue;
                }

                if (planData.voiceMin && planData.voiceMinUnit && !validVoiceUnits.includes(planData.voiceMinUnit)) {
                    errors.push({
                        row: rowIndex,
                        error: `Invalid voice unit: ${planData.voiceMinUnit}. Must be one of: ${validVoiceUnits.join(', ')}`
                    });
                    continue;
                }

                let countryIds = [];

                if (planData.countries && planData.countries.length > 0) {
                    const firstCountry = planData.countries[0];
                    const isCountryId = typeof firstCountry === 'string' && firstCountry.length <= 3;

                    if (isCountryId) {
                        const countries = await Country.findAll({
                            where: {
                                id: {
                                    [Op.in]: planData.countries
                                }
                            }
                        });

                        if (countries.length !== planData.countries.length) {
                            const foundCountryIds = countries.map(c => c.id);
                            const invalidCountries = planData.countries.filter(id => !foundCountryIds.includes(id));
                            errors.push({
                                row: rowIndex,
                                error: `Invalid country codes: ${invalidCountries.join(', ')}`
                            });
                            continue;
                        }
                        countryIds = planData.countries;
                    } else {
                        // Convert country names to IDs
                        const countries = await Country.findAll();
                        const countryMap = new Map();
                        countries.forEach(country => {
                            countryMap.set(country.name.toLowerCase(), country.id);
                        });

                        const invalidCountryNames = [];
                        countryIds = [];

                        for (const countryName of planData.countries) {
                            const countryId = countryMap.get(countryName.toLowerCase());
                            if (countryId) {
                                countryIds.push(countryId);
                            } else {
                                invalidCountryNames.push(countryName);
                            }
                        }

                        if (invalidCountryNames.length > 0) {
                            errors.push({
                                row: rowIndex,
                                error: `Invalid country names: ${invalidCountryNames.join(', ')}`
                            });
                            continue;
                        }
                    }
                } else {
                    errors.push({
                        row: rowIndex,
                        error: 'No countries specified'
                    });
                    continue;
                }

                // Check if provider's plans are currently hidden
                const providerPlansHidden = await isProviderPlansHidden(planData.providerId);

                // Prepare plan data for creation with incremented timestamp to maintain order
                const planTimestamp = new Date(baseTimestamp.getTime() + (i * 1000)); // Add 1 second per plan
                const processedPlanData = {
                    name: planData.name,
                    description: planData.description || null,
                    providerId: planData.providerId || null,
                    networkName: planData.networkName,
                    networkType: planData.networkType || '4G/LTE',
                    region: planData.region || null,
                    buyingPrice: planData.buyingPrice,
                    sellingPrice: planData.sellingPrice || null,
                    validityDays: planData.validityDays,
                    planType: planData.planType || 'Fixed',
                    planData: planData.planData || null,
                    planDataUnit: planData.planDataUnit || null,
                    customPlanData: planData.customPlanData || null,
                    category: planData.category || 'esim_realtime',
                    planCategory: planData.planCategory || 'Data Only',
                    is_voice: planData.is_voice || 'Not Available',
                    voiceMin: planData.voiceMin || null,
                    voiceMinUnit: planData.voiceMinUnit || null,
                    is_sms: planData.is_sms || 'Not Available',
                    sms: planData.sms || null,
                    top_up: planData.top_up || 'Not Available',
                    hotspot: planData.hotspot || 'Available',
                    activationPolicy: planData.activationPolicy || 'Activation upon purchase',
                    speed: planData.speed || 'Unrestricted',
                    stockThreshold: planData.stockThreshold || 10,
                    instructions: planData.instructions || null,
                    planInfo: planData.planInfo || null,
                    additionalInfo: planData.additionalInfo || null,
                    status: providerPlansHidden ? 'hidden' : 'visible',
                    isActive: true,
                    createdAt: planTimestamp,
                    updatedAt: planTimestamp
                };

                // Create the plan
                const plan = await EsimPlan.create(processedPlanData);

                // Associate countries with the plan (use converted country IDs)
                await Promise.all(countryIds.map(countryId =>
                    plan.addCountry(countryId, {
                        through: {
                            id: uuidv4(),
                            isDefault: countryId === countryIds[0] 
                        }
                    })
                ));

                // Fetch the created plan with associations
                const createdPlan = await EsimPlan.findByPk(plan.id, {
                    include: [
                        {
                            model: Country,
                            as: 'countries',
                            through: { attributes: ['isDefault'] }
                        },
                        {
                            model: Provider,
                            as: 'provider',
                            attributes: ['id', 'name', 'type', 'country']
                        }
                    ]
                });

                createdPlans.push(createdPlan);

            } catch (error) {
                console.error(`Error creating plan at row ${rowIndex}:`, error);
                errors.push({
                    row: rowIndex,
                    error: error.message || 'Failed to create plan'
                });
            }
        }

        // Invalidate product cache if any plans were created
        if (createdPlans.length > 0) {
            productCacheService.invalidateAllProductCaches();
        }

        // Return results
        const response = {
            message: `Bulk upload completed. Created: ${createdPlans.length}, Errors: ${errors.length}`,
            plans: createdPlans,
            errors: errors,
            summary: {
                total: plansData.length,
                created: createdPlans.length,
                failed: errors.length
            }
        };

        if (errors.length > 0) {
            return res.status(207).json(response); // 207 Multi-Status for partial success
        }

        res.status(201).json(response);

    } catch (error) {
        console.error('Error in bulk plan creation:', error);
        res.status(500).json({
            message: 'Failed to process bulk plan creation',
            error: error.message
        });
    }
};

// Update eSIM plan
exports.updateEsimPlan = async (req, res) => {
    try {
        const { id } = req.params;
        const updatedPlan = await EsimPlan.findByPk(id);

        if (!updatedPlan) {
            return res.status(404).json({ message: 'Plan not found' });
        }

        await updatedPlan.update(req.body);

        if (req.body.countries) {
            await updatedPlan.setCountries(req.body.countries);
        }

        // Invalidate cache for this plan
        invalidatePlanCache(id);

        productCacheService.invalidateAllProductCaches();

        // Fetch the updated plan with its associations
        const plan = await EsimPlan.findByPk(id, {
            include: [{
                model: Country,
                as: 'countries',
                through: { attributes: [] }
            }]
        });

        res.json(plan);
    } catch (error) {
        console.error('Error updating eSIM plan:', error);
        res.status(500).json({ message: 'Failed to update eSIM plan' });
    }
};

// Delete eSIM plan
exports.deleteEsimPlan = async (req, res) => {
    try {
        const plan = await EsimPlan.findByPk(req.params.id);
        if (!plan) {
            return res.status(404).json({ message: 'eSIM plan not found' });
        }

        // Check if there are any orders associated with this plan
        const orderCount = await sequelize.models.Order.count({
            where: { esimPlanId: req.params.id }
        });

        if (orderCount > 0) {
            await plan.update({
                isActive: false,
                status: 'hidden'
            });

            // Invalidate product cache since plan was deactivated
            productCacheService.invalidateAllProductCaches();

            return res.json({
                message: `eSIM plan deactivated successfully. Cannot delete plan with ${orderCount} associated orders. Plan has been hidden and deactivated instead.`,
                action: 'deactivated',
                orderCount: orderCount
            });
        }

        await plan.destroy();

        // Invalidate product cache since plan was deleted
        productCacheService.invalidateAllProductCaches();

        res.json({
            message: 'eSIM plan deleted successfully',
            action: 'deleted'
        });
    } catch (error) {
        console.error('Error deleting eSIM plan:', error);

        // Handle foreign key constraint error
        if (error.name === 'SequelizeForeignKeyConstraintError') {
            return res.status(400).json({
                message: 'Cannot delete eSIM plan with associated orders. Plan has been deactivated instead.',
                error: 'Foreign key constraint violation'
            });
        }

        res.status(500).json({ message: 'Failed to delete eSIM plan' });
    }
};

// Update eSIM plan status
exports.updateEsimPlanStatus = async (req, res) => {
    try {
        const { id } = req.params;
        const { status } = req.body;

        const plan = await EsimPlan.findByPk(id);
        if (!plan) {
            return res.status(404).json({ message: 'Plan not found' });
        }

        await plan.update({ status });

        invalidatePlanCache(null);

        // Emit WebSocket event for real-time updates
        const websocketService = req.app.get('websocketService');
        if (websocketService) {
            websocketService.broadcastPlanVisibilityChange({
                type: 'single',
                planId: id,
                status: status
            });
        }

        res.json({ message: 'Plan status updated successfully' });
    } catch (error) {
        console.error('Error updating plan status:', error);
        res.status(500).json({ message: 'Failed to update plan status' });
    }
};

// Update all eSIM plans visibility for a specific provider
exports.updateProviderPlansVisibility = async (req, res) => {
    try {
        const { providerId } = req.params;
        const { status } = req.body;

        // Validate status
        if (!['visible', 'hidden'].includes(status)) {
            return res.status(400).json({ message: 'Invalid status. Must be "visible" or "hidden"' });
        }

        // Check if provider exists
        const provider = await Provider.findByPk(providerId);
        if (!provider) {
            return res.status(404).json({ message: 'Provider not found' });
        }

        // Update all plans for this provider
        const [updatedCount] = await EsimPlan.update(
            { status },
            {
                where: {
                    providerId: providerId,
                    isActive: true
                }
            }
        );

        // Invalidate cache for all plans (since we updated multiple plans)
        invalidatePlanCache(null);

        // Emit WebSocket event for real-time updates
        const websocketService = req.app.get('websocketService');
        if (websocketService) {
            websocketService.broadcastPlanVisibilityChange({
                type: 'provider',
                providerId: providerId,
                status: status
            });
        }

        res.json({
            message: `Successfully updated ${updatedCount} plans to ${status} status`,
            updatedCount
        });
    } catch (error) {
        console.error('Error updating provider plans visibility:', error);
        res.status(500).json({ message: 'Failed to update provider plans visibility' });
    }
};

// Update eSIM plan selling price
exports.updateEsimPlanPrice = async (req, res) => {
    try {
        const { id } = req.params;
        const { sellingPrice } = req.body;

        const plan = await EsimPlan.findByPk(id);
        if (!plan) {
            return res.status(404).json({ message: 'Plan not found' });
        }

        await plan.update({ sellingPrice });

        invalidatePlanCache(id);

        productCacheService.invalidateAllProductCaches();

        res.json({ message: 'Plan price updated successfully' });
    } catch (error) {
        console.error('Error updating plan price:', error);
        res.status(500).json({ message: 'Failed to update plan price' });
    }
};

// Reset eSIM plan selling price
exports.resetEsimPlanPrice = async (req, res) => {
    try {
        const plan = await EsimPlan.findByPk(req.params.id);

        if (!plan) {
            return res.status(404).json({ message: 'eSIM plan not found' });
        }

        await plan.update({ sellingPrice: null });

        res.json({ message: 'Selling price has been reset successfully' });
    } catch (error) {
        console.error('Error resetting eSIM plan price:', error);
        res.status(500).json({ message: 'Failed to reset eSIM plan price' });
    }
};

// Get eSIM plans for partners - HIGHLY OPTIMIZED VERSION
exports.getPartnerEsimPlans = async (req, res) => {
    try {
        const { search, countryId, region, category = 'esim_realtime', page = 1, limit = 24 } = req.query;
        const partnerId = req.user.id;
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const offset = (pageNum - 1) * limitNum;



        // Create cache key for this specific request
        const cacheKey = `partner_${partnerId}_plans_${JSON.stringify({
            search, countryId, region, category, page, limit
        })}`;

        // Check cache first - but skip cache for search queries to ensure relevance scoring works
        let cachedData = null;
        if (!search || search.trim().length === 0) {
            cachedData = getCachedPlan(cacheKey);
            if (cachedData) {
                console.log(`Cache hit for partner plans: ${cacheKey}`);
                return res.json(cachedData);
            }
        } else {
            console.log(`Cache bypass: Skipping cache for search query to apply relevance scoring`);
        }

        console.log(`Cache miss for partner plans: ${cacheKey}`);

        // Add performance tracking with timeout protection
        const startTime = Date.now();
        const QUERY_TIMEOUT = 10000; // 10 second timeout for queries

        // ENHANCED: Start comprehensive memory monitoring
        const canProceed = emergencyMemoryProtection.startOperation(`Partner Plans Search: "${search || 'no-search'}"`);
        if (!canProceed) {
            return res.status(503).json({
                error: 'Service temporarily unavailable',
                message: 'System memory is critically high. Please try again in a few minutes.',
                code: 'MEMORY_CRITICAL'
            });
        }

        // Start search-specific memory management with predictive analysis
        const searchType = search ? (
            ['global', 'globa', 'premium', 'business', 'unlimited'].some(term =>
                search.toLowerCase().trim() === term || search.toLowerCase().trim().startsWith(term)
            ) ? 'broad' : 'specific'
        ) : 'none';

        // Get memory prediction for this operation
        const currentMemory = process.memoryUsage().heapUsed / 1024 / 1024;
        const operationInfo = {
            type: 'partner_search',
            searchType,
            searchTerm: search || '',
            resultCount: 0 // Will be updated later
        };

        const memoryPrediction = predictiveMemoryManager.shouldProceedWithOperation(operationInfo, currentMemory);

        // Check if operation should proceed based on prediction
        if (!memoryPrediction.proceed) {
            emergencyMemoryProtection.endOperation();
            console.error(`🚨 [PREDICTIVE] Operation blocked: ${memoryPrediction.reason}`);
            return res.status(503).json({
                error: 'Operation blocked by predictive memory management',
                message: `Predicted memory usage too high: ${memoryPrediction.reason}`,
                code: 'PREDICTIVE_MEMORY_BLOCK',
                prediction: memoryPrediction.prediction,
                projectedMemory: memoryPrediction.projectedMemory
            });
        }

        // Log warning if high risk operation
        if (memoryPrediction.warning) {
            console.warn(`⚠️ [PREDICTIVE] High risk operation proceeding: ${memoryPrediction.reason}`);
            console.warn(`   Recommendations:`, memoryPrediction.recommendations);
        }

        const searchOperation = searchMemoryManager.startSearchOperation(
            `partner_${partnerId}_${Date.now()}`,
            searchType,
            search || ''
        );

        // DETAILED PROFILING: Start comprehensive profiling for broad searches
        if (search) {
            const searchLowerForProfiling = search.toLowerCase().trim();
            const preciseSearchTermsForProfiling = ['global', 'globa', 'premium', 'business', 'unlimited'];
            const isBroadSearchForProfiling = preciseSearchTermsForProfiling.some(term => searchLowerForProfiling === term || searchLowerForProfiling.startsWith(term));

            if (isBroadSearchForProfiling) {
                console.log(`🔬 [DETAILED PROFILER] Starting comprehensive profiling for broad search: "${search}"`);
                detailedMemoryProfiler.startProfiling(`broad-search-${search}`);
                detailedMemoryProfiler.takeSnapshot('controller-start');
            }
        }

        // Get partner for markup calculation (cached separately)
        const partnerCacheKey = `partner_${partnerId}`;
        let partner = getCachedPlan(partnerCacheKey);
        if (!partner) {
            partner = await User.findByPk(partnerId, {
                attributes: ['id', 'markupPercentage', 'isActive']
            });
            if (!partner) {
                return res.status(404).json({ message: 'Partner not found' });
            }
            setCachePlan(partnerCacheKey, partner, 1800); // Cache for 30 minutes
        }

        // Build optimized where clause
        let whereClause = {
            status: 'visible',
            isActive: true,
            category
        };

        // Smart search filter with precision for broad terms
        if (search) {

            // Comprehensive search across all relevant fields (case-insensitive)
            whereClause[Op.and] = whereClause[Op.and] || [];
            whereClause[Op.and].push({
                [Op.or]: [
                    { name: { [Op.iLike]: `%${search}%` } },
                    { networkName: { [Op.iLike]: `%${search}%` } },
                    { region: { [Op.iLike]: `%${search}%` } },
                    { planType: { [Op.iLike]: `%${search}%` } },
                    { planCategory: { [Op.iLike]: `%${search}%` } }
                ]
            });
        }

        // Add region filter if provided - using safe parameterized queries for precise comma-separated matching (case-insensitive)
        if (region && region !== 'all') {
            whereClause[Op.and] = whereClause[Op.and] || [];
            whereClause[Op.and].push({
                [Op.or]: [
                    { region: { [Op.iLike]: region } }, // Exact match for single region (case-insensitive)
                    { region: { [Op.iLike]: `${region},%` } }, // Region at start
                    { region: { [Op.iLike]: `%, ${region},%` } }, // Region in middle
                    { region: { [Op.iLike]: `%, ${region}` } } // Region at end
                ]
            });
        }

        // Optimized include with minimal data
        const include = [
            {
                model: Provider,
                as: 'provider',
                attributes: ['id', 'name', 'type'],
                where: { status: 'active' },
                required: true
            }
        ];

        // Add country filter if specified
        if (countryId && countryId !== 'all') {
            include.push({
                model: Country,
                as: 'countries',
                attributes: ['id', 'name', 'iso3', 'flagUrl'],
                through: { attributes: [] },
                where: { id: countryId },
                required: true
            });
        } else {
            include.push({
                model: Country,
                as: 'countries',
                attributes: ['id', 'name', 'iso3', 'flagUrl'],
                through: { attributes: [] }
            });
        }

        // Create a simplified where clause for count query (without country name searches)
        let countWhereClause = {
            status: 'visible',
            isActive: true,
            category
        };

        // Add search filter for count with same precision logic
        if (search) {
            const searchLower = search.toLowerCase().trim();
            const preciseSearchTerms = ['global', 'globa', 'premium', 'business', 'unlimited'];
            const isPreciseSearch = preciseSearchTerms.some(term => searchLower === term || searchLower.startsWith(term));

            countWhereClause[Op.and] = countWhereClause[Op.and] || [];

            if (isPreciseSearch) {
                // For broad terms, search ONLY in plan names for precision (case-insensitive)
                countWhereClause[Op.and].push({
                    name: { [Op.iLike]: `%${search}%` }
                });
            } else {
                // For specific terms, use comprehensive search (case-insensitive)
                countWhereClause[Op.and].push({
                    [Op.or]: [
                        { name: { [Op.iLike]: `%${search}%` } },
                        { networkName: { [Op.iLike]: `%${search}%` } },
                        { region: { [Op.iLike]: `%${search}%` } },
                        { planType: { [Op.iLike]: `%${search}%` } },
                        { planCategory: { [Op.iLike]: `%${search}%` } }
                    ]
                });
            }
        }

        // Add region filter if provided (case-insensitive)
        if (region && region !== 'all') {
            countWhereClause[Op.and] = countWhereClause[Op.and] || [];
            countWhereClause[Op.and].push({
                [Op.or]: [
                    { region: { [Op.iLike]: region } }, // Exact match for single region (case-insensitive)
                    { region: { [Op.iLike]: `${region},%` } }, // Region at start
                    { region: { [Op.iLike]: `%, ${region},%` } }, // Region in middle
                    { region: { [Op.iLike]: `%, ${region}` } } // Region at end
                ]
            });
        }

        // First, get the count with a simplified query
        const countQuery = {
            where: countWhereClause,
            include: [
                {
                    model: Provider,
                    as: 'provider',
                    where: { status: 'active' },
                    required: true,
                    attributes: [] // Don't select provider data for count
                }
            ]
        };

        // Add country join only if needed for count
        if (countryId && countryId !== 'all') {
            countQuery.include.push({
                model: Country,
                as: 'countries',
                through: { attributes: [] },
                where: { id: countryId },
                required: true,
                attributes: [] // Don't select country data for count
            });
        }

        // PROFILING: Before count query
        if (search && detailedMemoryProfiler.isActive) {
            detailedMemoryProfiler.takeSnapshot('before-count-query');
        }

        // Add timeout protection for count query
        const countStartTime = Date.now();
        const totalItems = await EsimPlan.count(countQuery);
        const countDuration = Date.now() - countStartTime;

        // PROFILING: After count query
        if (search && detailedMemoryProfiler.isActive) {
            detailedMemoryProfiler.takeSnapshot('after-count-query');
            detailedMemoryProfiler.recordObjectCounts('after-count-query');
        }

        // Determine if we need smart sorting (country filter or search query) - MOVED UP
        const shouldPrioritizeCountry = countryId && countryId !== 'all';
        const shouldPrioritizeSearch = search && search.trim().length > 0;
        const shouldPrioritize = shouldPrioritizeCountry || shouldPrioritizeSearch;

        if (countDuration > 5000) {
            console.warn(`Count query took ${countDuration}ms - performance issue detected`);
        }

        // SMART FILTERING: Apply precise filtering for broad search terms
        if (shouldPrioritizeSearch && totalItems > 1000) {
            console.log(`Large result set detected (${totalItems} items) for search "${search}" - applying smart filtering`);

            // SMART APPROACH: For very large datasets, apply more focused filtering
            if (totalItems > 5000) {
                console.log(`🎯 SMART FILTERING: "${search}" search - applying focused filtering to reduce ${totalItems} results`);

                // For very large datasets, prioritize name matches over other fields
                if (whereClause[Op.and]) {
                    // Find and update the existing search condition to prioritize name matches
                    const searchConditionIndex = whereClause[Op.and].findIndex(condition => condition[Op.or]);
                    if (searchConditionIndex !== -1) {
                        // Keep the original search but add name priority
                        const originalSearch = whereClause[Op.and][searchConditionIndex];
                        whereClause[Op.and][searchConditionIndex] = {
                            [Op.or]: [
                                { name: { [Op.iLike]: `%${search}%` } }, // Prioritize name matches (case-insensitive)
                                originalSearch[Op.or] // Keep original broader search
                            ]
                        };
                        console.log(`🎯 Applied focused filtering: prioritizing name matches for "${search}"`);
                    }
                }
            }

            // Emergency memory protection - abort if still too many results after filtering
            if (totalItems > 8000) {
                console.error(`🚨 EMERGENCY: Search "${search}" returned ${totalItems} items - aborting to prevent memory leak`);
                return res.status(400).json({
                    error: 'Search too broad',
                    message: 'Please refine your search query to get more specific results',
                    totalItems,
                    suggestion: 'Try using more specific search terms'
                });
            }
        }

        // For prioritized queries, we need to fetch enough plans to ensure we can fill the requested page
        const minPlansNeeded = pageNum * limitNum;



        // Optimize fetch limit based on search specificity and total items
        let fetchLimit;
        if (shouldPrioritize) {
            if (shouldPrioritizeSearch && search) {
                // MEMORY MANAGEMENT: Apply reasonable limits based on search specificity
                let maxFetchForSearch;
                if (totalItems > 10000) {
                    // For very large datasets, limit fetch size regardless of search term
                    maxFetchForSearch = Math.min(200, Math.max(minPlansNeeded * 2, 100));
                    console.warn(`🚨 LARGE DATASET: Search "${search}" has ${totalItems} results - limiting fetch to ${maxFetchForSearch} plans`);
                } else {
                    // Increased limits for search queries to ensure name matches are found
                    maxFetchForSearch = Math.min(totalItems, 500); // Fetch up to 500 plans for relevance scoring
                }

                // For search queries, prioritize relevance scoring over pagination efficiency
                fetchLimit = Math.min(totalItems, maxFetchForSearch);

                // Additional protection for very large datasets - but allow more for search queries
                if (totalItems > 5000) {
                    // Only apply strict limits for very large datasets (5000+)
                    fetchLimit = Math.min(fetchLimit, shouldPrioritizeSearch ? 200 : 50);
                    console.warn(`🚨 LARGE DATASET PROTECTION: ${totalItems} items - limiting fetch to ${fetchLimit} plans (search: ${shouldPrioritizeSearch})`);
                } else if (totalItems > 2000 && !shouldPrioritizeSearch) {
                    // For non-search queries with medium datasets, apply moderate limits
                    fetchLimit = Math.min(fetchLimit, 100);
                    console.warn(`Medium dataset: ${totalItems} items - limiting fetch to ${fetchLimit} plans`);
                }
            } else {
                fetchLimit = Math.min(Math.max(minPlansNeeded, limitNum * 2), Math.min(totalItems, 100));
            }
        } else {
            fetchLimit = limitNum;
        }

        const fetchOffset = shouldPrioritize ? 0 : offset;

        // ENHANCED: Check memory before data fetch
        if (!emergencyMemoryProtection.checkMemoryDuringOperation()) {
            emergencyMemoryProtection.endOperation();
            searchMemoryManager.abortSearchOperation(searchOperation.id, 'memory_limit_exceeded');
            return res.status(503).json({
                error: 'Memory limit exceeded',
                message: 'Operation aborted to prevent system crash',
                code: 'MEMORY_LIMIT_EXCEEDED'
            });
        }

        // Check search-specific memory constraints
        const memoryCheck = searchMemoryManager.checkSearchMemory(searchOperation.id, 'before-fetch');
        if (!memoryCheck.canContinue) {
            emergencyMemoryProtection.endOperation();
            return res.status(503).json({
                error: 'Search memory limit exceeded',
                message: `Operation aborted: ${memoryCheck.reason}`,
                code: 'SEARCH_MEMORY_EXCEEDED',
                memoryIncrease: memoryCheck.memoryIncrease
            });
        }

        // PROFILING: Before main data fetch
        if (search && detailedMemoryProfiler.isActive) {
            detailedMemoryProfiler.takeSnapshot('before-main-fetch');
            detailedMemoryProfiler.recordObjectCounts('before-main-fetch');
        }

        // Then get the actual data with full includes
        const allPlans = await EsimPlan.findAll({
            where: whereClause,
            include,
            order: [['createdAt', 'DESC']],
            limit: fetchLimit,
            offset: fetchOffset
        });

        // PROFILING: After main data fetch
        if (search && detailedMemoryProfiler.isActive) {
            detailedMemoryProfiler.takeSnapshot('after-main-fetch');
            detailedMemoryProfiler.recordObjectCounts('after-main-fetch');
            console.log(`🔬 [PROFILER] Fetched ${allPlans.length} plans from database`);
        }

        // ENHANCED: Check memory after data fetch
        if (!emergencyMemoryProtection.checkMemoryDuringOperation()) {
            emergencyMemoryProtection.endOperation();
            searchMemoryManager.abortSearchOperation(searchOperation.id, 'memory_limit_after_fetch');
            return res.status(503).json({
                error: 'Memory limit exceeded during data processing',
                message: 'Operation aborted to prevent system crash',
                code: 'MEMORY_LIMIT_EXCEEDED'
            });
        }

        // Check search memory after data fetch
        const postFetchMemoryCheck = searchMemoryManager.checkSearchMemory(searchOperation.id, 'after-fetch');
        if (!postFetchMemoryCheck.canContinue) {
            emergencyMemoryProtection.endOperation();
            return res.status(503).json({
                error: 'Search memory limit exceeded after data fetch',
                message: `Operation aborted: ${postFetchMemoryCheck.reason}`,
                code: 'SEARCH_MEMORY_EXCEEDED',
                memoryIncrease: postFetchMemoryCheck.memoryIncrease
            });
        }

        const totalPages = Math.ceil(totalItems / limitNum);

        // Apply markup-based pricing for all plans (optimized)
        const markupPercentage = partner.markupPercentage || 0;
        let processedPlans = allPlans.map(plan => {
            const planData = plan.toJSON();

            // Calculate display price based on markup (optimized calculation)
            if (!planData.sellingPrice && markupPercentage > 0) {
                const basePrice = parseFloat(planData.buyingPrice);
                planData.displayPrice = (basePrice * (1 + markupPercentage / 100)).toFixed(2);
            } else {
                planData.displayPrice = planData.sellingPrice || planData.buyingPrice;
            }

            // Ensure all countries have flagUrl
            if (planData.countries && Array.isArray(planData.countries)) {
                planData.countries = planData.countries.map(country => ({
                    ...country,
                    flagUrl: country.flagUrl || `https://flagcdn.com/w20/${country.id.toLowerCase()}.png`
                }));
            }

            return planData;
        });

        // PARTNER PORTAL: Apply relevance scoring for search queries
        if (shouldPrioritizeSearch && search) {
            const searchQuery = search.toLowerCase().trim();
            const searchTerms = searchQuery.split(/\s+/).filter(term => term.length > 0);

            // Detect if this is likely a country search (common country names/codes)
            const commonCountryTerms = ['usa', 'uk', 'us', 'canada', 'australia', 'germany', 'france', 'italy', 'spain', 'japan', 'korea', 'china', 'india', 'brazil', 'mexico'];
            const isLikelyCountrySearch = commonCountryTerms.some(country =>
                searchQuery === country || searchTerms.some(term => term === country)
            );

            // Get country name for country-based scoring
            let countryName = '';
            if (countryId && countryId !== 'all') {
                const selectedCountry = await Country.findByPk(countryId, { attributes: ['name'] });
                countryName = selectedCountry ? selectedCountry.name.toLowerCase() : '';
            }

            // Debug: Log first few plan names to see what we're working with
            console.log(`🔍 SAMPLE PLANS being scored:`, processedPlans.slice(0, 5).map(p => p.name));

            // Sort plans by relevance priority
            const sortedPlans = processedPlans
                .map(plan => {
                    let relevanceScore = 0;
                    const planCountries = plan.countries || [];
                    const countryCount = planCountries.length;
                    const planName = (plan.name || '').toLowerCase();
                    const networkName = (plan.networkName || '').toLowerCase();
                    const planRegion = Array.isArray(plan.region)
                        ? plan.region.join(', ').toLowerCase()
                        : (plan.region || '').toLowerCase();

                    // PRIORITY 1: Search term matches in plan name (Score: 3000-5000) - HIGHEST PRIORITY
                    if (searchQuery) {
                        if (planName.includes(searchQuery)) {
                            // Exact search match gets highest priority
                            relevanceScore += 5000;
                        } else {
                            const matchCount = searchTerms.filter(term =>
                                planName.includes(term) || networkName.includes(term)
                            ).length;
                            if (matchCount > 0) {
                                relevanceScore += 3000 + (matchCount * 500);
                            }
                        }
                    }

                    // PRIORITY 2: Country name in plan name (Score: 2500-3500)
                    if (countryName && planName.includes(countryName)) {
                        // Boost plans with country name in plan name
                        if (countryCount === 1) {
                            relevanceScore += 3500;
                        } else {
                            relevanceScore += 2500;
                        }
                    }

                    // PRIORITY 3: Country-specific plans (Score: 200-2000)
                    if (countryId && countryId !== 'all') {
                        // When a specific country is selected, prioritize plans available in that country
                        const hasTargetCountry = planCountries.some(c => c.id === countryId);
                        if (hasTargetCountry) {
                            if (countryCount === 1) {
                                relevanceScore += 2000;
                            } else if (countryCount <= 5) {
                                relevanceScore += 1500 - (countryCount * 100);
                            } else if (countryCount <= 20) {
                                relevanceScore += 1000 - (countryCount * 50);
                            } else {
                                relevanceScore += 500 - Math.min(countryCount * 10, 250);
                            }
                        }
                    } else {
                        // When no specific country is selected, prioritize based on search type
                        if (isLikelyCountrySearch) {
                            // For country searches, strongly prefer single-country plans
                            if (countryCount === 1) {
                                relevanceScore += 1500; // Higher bonus for country searches
                            } else if (countryCount <= 3) {
                                relevanceScore += 800 - (countryCount * 100);
                            } else {
                                relevanceScore += 200 - Math.min(countryCount * 20, 150);
                            }
                        } else {
                            // For general searches, use standard country prioritization
                            if (countryCount === 1) {
                                relevanceScore += 1000;
                            } else if (countryCount <= 5) {
                                relevanceScore += 800 - (countryCount * 50);
                            } else if (countryCount <= 20) {
                                relevanceScore += 500 - (countryCount * 25);
                            } else {
                                relevanceScore += 200 - Math.min(countryCount * 5, 150);
                            }
                        }
                    }

                    // PRIORITY 4: Network name matches (Score: 400)
                    if (searchQuery && networkName.includes(searchQuery)) {
                        relevanceScore += 400;
                    }

                    // PRIORITY 5: Region matches (Score: 200-300)
                    if (searchQuery && planRegion.includes(searchQuery)) {
                        relevanceScore += 200;
                    }

                    // PRIORITY 6: Country matches in plan countries (Score: 100-600, with exact match bonus)
                    if (searchQuery) {
                        let exactCountryMatches = 0;
                        let partialCountryMatches = 0;

                        planCountries.forEach(c => {
                            const countryName = c.name.toLowerCase();

                            // Check for exact country name matches (higher priority)
                            if (countryName === searchQuery ||
                                searchTerms.some(term => countryName === term)) {
                                exactCountryMatches++;
                            }
                            // Special handling for common country variations
                            else if ((searchQuery === 'usa' || searchQuery === 'us') &&
                                     (countryName === 'united states' || countryName === 'usa' || countryName === 'us')) {
                                exactCountryMatches++;
                            }
                            else if ((searchQuery === 'uk') &&
                                     (countryName === 'united kingdom' || countryName === 'uk')) {
                                exactCountryMatches++;
                            }
                            // Check for partial matches (lower priority, but exclude common false positives)
                            else if (searchQuery.length >= 3) { // Only for searches 3+ chars to avoid false matches
                                if (countryName.includes(searchQuery) ||
                                    searchTerms.some(term => term.length >= 3 && countryName.includes(term))) {
                                    partialCountryMatches++;
                                }
                            }
                        });

                        if (exactCountryMatches > 0) {
                            // Exact country matches get high priority
                            relevanceScore += exactCountryMatches * 800;
                        } else if (partialCountryMatches > 0) {
                            // Partial matches get lower priority, capped to prevent spam
                            const partialBonus = Math.min(partialCountryMatches * 150, 300);
                            relevanceScore += partialBonus;
                        }
                    }

                    return { ...plan, _relevanceScore: relevanceScore };
                })
                .sort((a, b) => {
                    if (b._relevanceScore !== a._relevanceScore) {
                        return b._relevanceScore - a._relevanceScore;
                    }
                    return new Date(b.createdAt) - new Date(a.createdAt);
                });



            // Apply pagination to the sorted results
            const startIndex = offset;
            const endIndex = offset + limitNum;
            const availablePlansForPage = sortedPlans.slice(startIndex, endIndex);

            processedPlans = availablePlansForPage.map(plan => {
                const { _relevanceScore, ...cleanPlan } = plan;
                return cleanPlan;
            });
        } else {
            // No search query - apply regular pagination
            const startIndex = offset;
            const endIndex = offset + limitNum;
            processedPlans = processedPlans.slice(startIndex, endIndex);
        }

        // Get cached regions to avoid repeated queries
        const regionsCacheKey = `regions_${category}`;
        let processedRegions = getCachedPlan(regionsCacheKey);

        if (!processedRegions) {
            // Get all unique regions from active plans (optimized query)
            const regions = await EsimPlan.findAll({
                attributes: ['region'],
                where: {
                    status: 'visible',
                    isActive: true,
                    region: { [Op.not]: null },
                    '$provider.status$': 'active'  
                },
                include: [{
                    model: Provider,
                    as: 'provider',
                    attributes: [],
                    required: true
                }]
            });

            // Process and deduplicate regions 
            processedRegions = regions
                .map(r => r.region)
                .filter(Boolean)
                .flatMap(region =>
                    typeof region === 'string'
                        ? region.split(',').map(r => r.trim())
                        : Array.isArray(region)
                            ? region.map(r => r.trim())
                            : []
                )
                .filter((value, index, self) => value && self.indexOf(value) === index)
                .sort();

            // Cache regions for 30 minutes
            setCachePlan(regionsCacheKey, processedRegions, 1800);
        }

        // Get cached countries to avoid repeated queries
        const countriesCacheKey = 'active_countries';
        let countries = getCachedPlan(countriesCacheKey);

        if (!countries) {
            countries = await Country.findAll({
                where: { isActive: true },
                attributes: ['id', 'name', 'iso3', 'flagUrl'],
                order: [['name', 'ASC']]
            });

            countries = countries.map(country => ({
                ...country.toJSON(),
                flagUrl: country.flagUrl || `https://flagcdn.com/w20/${country.id.toLowerCase()}.png`
            }));

            // Cache countries for 1 hour
            setCachePlan(countriesCacheKey, countries, 3600);
        }

        // PARTNER PORTAL: Relevance scoring is already handled above in the partner-specific section
        // Skip the duplicate admin portal relevance scoring logic


        // Prepare response data - keep true total for pagination, but indicate prioritization
        let finalTotalPages = totalPages;
        let finalTotalItems = totalItems;
        let isPrioritizedSubset = false;

        if (shouldPrioritize) {
            finalTotalPages = totalPages;
            finalTotalItems = totalItems;

            const fetchedCount = allPlans.length;
            if (fetchedCount < totalItems) {
                isPrioritizedSubset = true;
                console.log(`Showing prioritized subset: fetched ${fetchedCount} of ${totalItems} total plans for sorting`);
            }
        }

        // Optimize response size - exclude metadata for broad searches to prevent large responses
        const searchLower = search ? search.toLowerCase().trim() : '';
        const preciseSearchTerms = ['global', 'globa', 'premium', 'business', 'unlimited'];
        const isBroadSearch = preciseSearchTerms.some(term => searchLower === term || searchLower.startsWith(term));

        // ULTRA-EMERGENCY: Never include metadata for broad searches to keep response size minimal
        const includeMetadata = !isBroadSearch && (pageNum === 1 || !search);

        const responseData = {
            plans: processedPlans,
            page: pageNum,
            totalPages: finalTotalPages,
            totalItems: finalTotalItems,
            ...(includeMetadata && {
                countries: countries.map(c => ({ id: c.id, name: c.name, flagUrl: c.flagUrl })), // Minimal country data
                regions: processedRegions
            }),
            ...(shouldPrioritize && {
                isSmartSorted: true,
                sortingType: shouldPrioritizeSearch ? 'search_relevance' : 'country_priority',
                smartSortedPages: Math.ceil(allPlans.length / limitNum),
                isSubset: isPrioritizedSubset,
                ...(shouldPrioritizeSearch && { searchQuery: search }),
                ...(shouldPrioritizeCountry && { filteredCountry: countryId })
            })
        };

        // Cache the response - but skip caching for search queries to ensure fresh relevance scoring
        if (!shouldPrioritizeSearch) {
            const cacheDuration = 300; // 5 minutes for non-search queries
            setCachePlan(cacheKey, responseData, cacheDuration);
        }

        // Log performance metrics and pagination info
        const endTime = Date.now();
        const duration = endTime - startTime;

        if (shouldPrioritize) {
            const prioritizedPages = Math.ceil(allPlans.length / limitNum);
            const sortingType = shouldPrioritizeSearch ? 'search relevance' : 'country priority';
            const filterInfo = shouldPrioritizeSearch ? `search: "${search}"` : `country: ${countryId}`;
            const responseSize = JSON.stringify(responseData).length;
            const performanceWarning = duration > 5000 ? ' ⚠️ SLOW' : duration > 2000 ? ' ⚠️' : '';

            // Special logging for broad searches with bypass
            if (isBroadSearch && shouldPrioritizeSearch) {
                console.log(`🚨 ULTRA-EMERGENCY BYPASS completed in ${duration}ms${performanceWarning} for ${finalTotalItems} total items (${filterInfo}, page ${pageNum}/${finalTotalPages}, returned ${processedPlans.length} plans, response: ${(responseSize/1024).toFixed(1)}KB, metadata: ${includeMetadata ? 'included' : 'excluded'})`);
            } else {
                console.log(`Partner plans query with ${sortingType} completed in ${duration}ms${performanceWarning} for ${finalTotalItems} total items (${filterInfo}, page ${pageNum}/${finalTotalPages}, returned ${processedPlans.length} plans, smart pages: 1-${prioritizedPages}, response: ${(responseSize/1024).toFixed(1)}KB)`);
            }
        } else {
            console.log(`Partner plans query completed in ${duration}ms for ${finalTotalItems} items (page ${pageNum}/${finalTotalPages}, returned ${processedPlans.length} plans)`);
        }

        // Debug pagination issues
        if (processedPlans.length !== limitNum && pageNum < finalTotalPages) {
            console.warn(`Pagination issue detected: Expected ${limitNum} plans on page ${pageNum}, got ${processedPlans.length}. Total: ${finalTotalItems}, TotalPages: ${finalTotalPages}`);
        }

        // ENHANCED MEMORY CLEANUP: Record operation for predictive learning (but don't cleanup data yet)
        if (search) {
            // Complete the search operation successfully and record for predictive learning
            const operationResult = {
                type: 'partner_search',
                searchType,
                searchTerm: search || '',
                startMemory: searchOperation.startMemory,
                endMemory: process.memoryUsage().heapUsed / 1024 / 1024,
                memoryIncrease: (process.memoryUsage().heapUsed / 1024 / 1024) - searchOperation.startMemory,
                duration: Date.now() - searchOperation.startTime,
                success: true,
                resultCount: processedPlans.length,
                cleanupActions: []
            };

            // Record operation for predictive learning
            predictiveMemoryManager.recordOperation(operationResult);

            searchMemoryManager.completeSearchOperation(searchOperation.id, {
                totalPlans: totalItems,
                returnedPlans: processedPlans.length,
                searchType
            });
        } else {
            // Complete non-search operation and record for predictive learning
            const operationResult = {
                type: 'partner_search',
                searchType: 'none',
                searchTerm: '',
                startMemory: searchOperation.startMemory,
                endMemory: process.memoryUsage().heapUsed / 1024 / 1024,
                memoryIncrease: (process.memoryUsage().heapUsed / 1024 / 1024) - searchOperation.startMemory,
                duration: Date.now() - searchOperation.startTime,
                success: true,
                resultCount: processedPlans.length
            };

            predictiveMemoryManager.recordOperation(operationResult);

            searchMemoryManager.completeSearchOperation(searchOperation.id, {
                totalPlans: totalItems,
                returnedPlans: processedPlans.length,
                searchType: 'none'
            });
        }

        // PROFILING: End detailed profiling for broad searches
        if (search && detailedMemoryProfiler.isActive) {
            detailedMemoryProfiler.takeSnapshot('controller-end');
            detailedMemoryProfiler.recordObjectCounts('controller-end');

            const profilingResults = detailedMemoryProfiler.stopProfiling();
            console.log(`🔬 [PROFILER] Profiling completed for "${search}" search`);

            if (profilingResults.overallAnalysis) {
                const analysis = profilingResults.overallAnalysis;
                console.log(`🔬 [PROFILER] Overall memory change: ${(analysis.memoryDifference.heapUsed / 1024 / 1024).toFixed(1)}MB`);
                console.log(`🔬 [PROFILER] Duration: ${analysis.duration}ms`);
            }
        }

        // EMERGENCY: End memory monitoring
        const memoryStats = emergencyMemoryProtection.endOperation();
        if (memoryStats && !memoryStats.isHealthy) {
            console.warn(`⚠️ Memory concerns detected: ${JSON.stringify(memoryStats)}`);
        }

        res.json(responseData);

        // ENHANCED MEMORY CLEANUP: Cleanup data AFTER response is sent to avoid clearing response data
        setImmediate(() => {
            if (search) {
                console.log(`🧹 [ENHANCED CLEANUP] Starting post-response cleanup for: "${search}"`);

                // Prepare search data for cleanup
                const searchData = {
                    allPlans,
                    processedPlans,
                    countries,
                    processedRegions,
                    relevanceScores: {} // Add any relevance scoring data if available
                };

                // Use enhanced search memory manager for cleanup
                const cleanupResult = searchMemoryManager.cleanupSearchData(searchData, searchType);
                console.log(`🧹 [ENHANCED CLEANUP] Completed: ${cleanupResult.totalCleared} items in ${cleanupResult.duration}ms`);
            }
        });
    } catch (error) {
        console.error('Error getting partner eSIM plans:', error);

        // ENHANCED: End memory monitoring on error
        emergencyMemoryProtection.endOperation();

        // Abort search operation if it exists and record failure for predictive learning
        if (searchOperation && searchOperation.isActive) {
            const failureResult = {
                type: 'partner_search',
                searchType: searchOperation.searchType || 'unknown',
                searchTerm: searchOperation.searchTerm || '',
                startMemory: searchOperation.startMemory,
                endMemory: process.memoryUsage().heapUsed / 1024 / 1024,
                memoryIncrease: (process.memoryUsage().heapUsed / 1024 / 1024) - searchOperation.startMemory,
                duration: Date.now() - searchOperation.startTime,
                success: false,
                abortReason: 'controller_error',
                resultCount: 0
            };

            predictiveMemoryManager.recordOperation(failureResult);
            searchMemoryManager.abortSearchOperation(searchOperation.id, 'controller_error');
        }

        // Check if this is a memory-related error
        if (error.message && (error.message.includes('memory') || error.message.includes('heap'))) {
            console.error('🚨 MEMORY-RELATED ERROR DETECTED');
            emergencyMemoryProtection.emergencyCleanup();

            return res.status(503).json({
                message: 'Service temporarily unavailable due to high memory usage',
                error: 'MEMORY_ERROR',
                suggestion: 'Please try again with a more specific search query'
            });
        }

        res.status(500).json({
            message: 'Failed to get eSIM plans',
            error: error.message
        });
    }
};


exports.getPartnerEsimPlan = async (req, res) => {
    try {
        const { id } = req.params;
        const partnerId = req.user.id;

        // Create cache key for this specific request
        const cacheKey = `partner_${partnerId}_plan_${id}`;

        // Get partner for markup calculation
        const partner = await User.findByPk(partnerId);
        if (!partner) {
            return res.status(404).json({ message: 'Partner not found' });
        }

        // Get plan with provider and country information
        let plan = await EsimPlan.findOne({
            where: {
                [Op.or]: [
                    { id },
                    { productId: id },
                    { externalProductId: id }
                ],
                status: 'visible',
                isActive: true
            },
            include: [
                {
                    model: Provider,
                    as: 'provider',
                    where: { status: 'active' }, // Only show plans from active providers
                    required: true
                },
                {
                    model: Country,
                    as: 'countries',
                    through: { attributes: ['isDefault'] }
                }
            ]
        });

        if (!plan) {
            return res.status(404).json({ message: 'Plan not found or not available' });
        }

        // Get stock count
        const stockCount = plan.provider?.name === 'mobimatter' ? 999 : await sequelize.models.EsimStock.count({
            where: {
                esimPlanId: plan.id,
                status: 'available'
            }
        });

        plan = plan.toJSON();
        plan.stockCount = stockCount;

        // Apply markup-based pricing if no specific selling price
        if (!plan.sellingPrice && partner.markupPercentage) {
            const markup = 1 + (partner.markupPercentage / 100);
            plan.calculatedPrice = Number((plan.buyingPrice * markup).toFixed(2));
            plan.sellingPrice = null;
        }

        // For response, use calculatedPrice if available, otherwise use sellingPrice
        plan.displayPrice = plan.calculatedPrice || plan.sellingPrice;

        // Cache the plan data
        setCachePlan(cacheKey, plan);

        res.json(plan);
    } catch (error) {
        console.error('Error getting partner eSIM plan:', error);
        res.status(500).json({ message: 'Failed to get eSIM plan' });
    }
};

// Toggle start date requirement
exports.toggleStartDate = async (req, res) => {
    try {
        const { id } = req.params;

        const plan = await EsimPlan.findByPk(id);
        if (!plan) {
            return res.status(404).json({ message: 'eSIM plan not found' });
        }

        plan.startDateEnabled = !plan.startDateEnabled;

        // Set activation policy to 'Activation upon travel date' when start date is enabled
        if (plan.startDateEnabled) {
            plan.activationPolicy = 'Activation upon travel date';
        }

        await plan.save();

        res.json({
            message: 'Start date requirement updated successfully',
            startDateEnabled: plan.startDateEnabled,
            activationPolicy: plan.activationPolicy
        });
    } catch (error) {
        console.error('Error toggling start date:', error);
        res.status(500).json({ message: 'Failed to update start date requirement' });
    }
};

// Update stock threshold for eSIM plan
exports.updateStockThreshold = async (req, res) => {
    try {
        const { id } = req.params;
        const { threshold } = req.body;

        // Validate threshold
        const newThreshold = parseInt(threshold);
        if (isNaN(newThreshold) || newThreshold < 1 || newThreshold > 1000) {
            return res.status(400).json({
                message: 'Invalid threshold value. Must be between 1 and 1000.'
            });
        }

        // Find the plan
        const plan = await EsimPlan.findByPk(id);
        if (!plan) {
            return res.status(404).json({ message: 'eSIM plan not found' });
        }

        // Get current stock count
        const stockCount = await sequelize.models.EsimStock.count({
            where: {
                esimPlanId: id,
                status: 'available'
            }
        });

        // Update threshold
        await plan.update({
            stockThreshold: newThreshold
        });

        // Check if current stock is below new threshold
        const isLowStock = stockCount < newThreshold;

        // If stock is below threshold, send notification
        if (isLowStock) {
            await checkStockAndNotify(id);
        }

        res.json({
            message: 'Stock threshold updated successfully',
            stockThreshold: newThreshold,
            currentStock: stockCount,
            isLowStock
        });
    } catch (error) {
        console.error('Error updating stock threshold:', error);
        res.status(500).json({
            message: 'Failed to update stock threshold',
            error: error.message
        });
    }
};

// Sync external plans from providers
exports.syncExternalPlans = async (req, res) => {
    try {
        res.json({
            message: 'Sync operation started in background',
            status: 'started',
            timestamp: new Date().toISOString()
        });

        const io = req.app.get('io');

        // Run the actual sync operation in background
        performSyncOperation(io).catch(error => {
            console.error('[Sync] Background sync failed:', error);
            if (io) {
                io.emit('sync_error', {
                    message: 'Sync operation failed',
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        });

    } catch (error) {
        console.error('[Sync] Error starting sync operation:', error);
        res.status(500).json({
            message: 'Failed to start sync operation',
            error: error.message
        });
    }
};

// Separate function to perform the actual sync operation
async function performSyncOperation(io) {
    try {
        if (io) {
            io.emit('sync_progress', {
                phase: 'starting',
                message: 'Starting sync operation...',
                progress: 0
            });
        }

        // Get active external providers
        const providers = await Provider.findAll({
            where: {
                type: 'API',
                status: 'active',
                name: {
                    [Op.in]: ['mobimatter', 'billionconnect']
                }
            }
        });

        const results = {
            total: 0,
            updated: 0,
            created: 0,
            failed: 0,
            inactive: 0,
            metadataUpdated: 0,
            errors: []
        };

        // Get all countries for mapping
        const allCountries = await Country.findAll({
            where: { isActive: true },
            attributes: ['id', 'name', 'iso3']
        });

        // Create a map of country names and ISO3 codes to IDs for quick lookup
        const countryMap = new Map();
        allCountries.forEach(country => {
            countryMap.set(country.name.toLowerCase(), country);
            countryMap.set(country.iso3.toLowerCase(), country);
            countryMap.set(country.id.toLowerCase(), country);
        });

        const categories = ['esim_realtime', 'esim_addon', 'esim_replacement'];

        if (io) {
            io.emit('sync_progress', {
                phase: 'preparation',
                message: 'Preparing sync operation...',
                progress: 5
            });
        }

        // STEP 1: Clear cache to ensure partners get fresh data
        invalidatePlanCache(null);
        console.log('[Sync] Cleared plan cache');

        // STEP 2: Mark all external provider plans as inactive before syncing
        await EsimPlan.update(
            { isActive: false },
            {
                where: {
                    providerId: providers.map(p => p.id)
                }
            }
        );
        console.log('[Sync] Marked all external plans as inactive');

        if (io) {
            io.emit('sync_progress', {
                phase: 'fetching',
                message: 'Fetching plans from external providers...',
                progress: 10
            });
        }

        const fetchedExternalPlanIds = new Set();
        const processedPlanIds = [];

        let totalCategories = providers.length * categories.length;
        let processedCategories = 0;

        for (const provider of providers) {
            try {
                const providerService = providerFactory.getProvider(provider.name);

                for (const category of categories) {
                    try {
                        console.log(`[Sync] Fetching ${category} plans from ${provider.name}...`);

                        if (io) {
                            io.emit('sync_progress', {
                                phase: 'fetching',
                                message: `Fetching ${category} plans from ${provider.name}...`,
                                progress: 10 + (processedCategories / totalCategories) * 40
                            });
                        }

                        await delay(1000); // 1 second delay

                        const externalPlans = await providerService.getProducts(category);

                        if (!Array.isArray(externalPlans)) {
                            throw new Error(`Invalid response from provider ${provider.name} for category ${category}`);
                        }

                        console.log(`[Sync] Found ${externalPlans.length} ${category} plans from ${provider.name}`);

                        processedCategories++;

                        for (const [index, externalPlan] of externalPlans.entries()) {
                            try {
                                // Add a small delay every 5 plans to avoid database connection issues
                                if (index > 0 && index % 5 === 0) {
                                    await delay(300); // 300ms delay
                                }

                                results.total++;
                                const standardizedPlan = await providerFactory.standardizeProduct(provider.name, externalPlan);

                                // Add external plan ID to tracking set
                                if (standardizedPlan.externalProductId) {
                                    fetchedExternalPlanIds.add(standardizedPlan.externalProductId);
                                }
                                if (standardizedPlan.externalSkuId) {
                                    fetchedExternalPlanIds.add(standardizedPlan.externalSkuId);
                                }

                                standardizedPlan.category = category;

                                // Check if plan already exists - prioritize externalProductId for exact matching
                                let plan = await EsimPlan.findOne({
                                    where: {
                                        externalProductId: standardizedPlan.externalProductId
                                    }
                                });

                                // If not found by externalProductId, try externalSkuId as fallback
                                if (!plan && !standardizedPlan.externalProductId) {
                                    plan = await EsimPlan.findOne({
                                        where: {
                                            externalSkuId: standardizedPlan.externalSkuId
                                        }
                                    });
                                }

                                if (plan) {
                                    // Fields to preserve from the existing plan
                                    const fieldsToPreserve = {
                                        sellingPrice: plan.sellingPrice,
                                        status: plan.status,
                                        stockThreshold: plan.stockThreshold,
                                        startDateEnabled: plan.startDateEnabled,
                                        features: plan.features,
                                        instructions: plan.instructions,
                                        description: plan.description,
                                        activationPolicy: plan.activationPolicy,
                                        hotspot: plan.hotspot,
                                        speed: plan.speed,
                                        top_up: plan.top_up,
                                    };

                                    // Preserve existing buying price if the new one is zero or invalid
                                    if (!standardizedPlan.buyingPrice || standardizedPlan.buyingPrice <= 0) {
                                        if (plan.buyingPrice && plan.buyingPrice > 0) {
                                            fieldsToPreserve.buyingPrice = plan.buyingPrice;
                                            console.log(`   ⚠️  Preserving existing buying price $${plan.buyingPrice} for ${plan.name} (API returned: $${standardizedPlan.buyingPrice})`);
                                        }
                                    }

                                    // Update existing plan while preserving manual fields and setting isActive to true
                                    await plan.update({
                                        ...standardizedPlan,
                                        ...fieldsToPreserve,
                                        providerId: provider.id,
                                        isActive: true 
                                    });
                                    results.updated++;
                                    processedPlanIds.push(plan.id);
                                } else {
                                    // Check if provider's plans are currently hidden
                                    const providerPlansHidden = await isProviderPlansHidden(provider.id);

                                    // Create new plan with isActive = true
                                    plan = await EsimPlan.create({
                                        ...standardizedPlan,
                                        providerId: provider.id,
                                        productId: await generateProductId(),
                                        status: providerPlansHidden ? 'hidden' : 'visible',
                                        isActive: true 
                                    });
                                    results.created++;
                                    processedPlanIds.push(plan.id);
                                }

                                // Get supported countries from the plan
                                const supportedCountries =
                                    externalPlan.countries ||
                                    externalPlan.supportedCountries ||
                                    (externalPlan.providerMetadata?.originalData?.countries || []);

                                await plan.setCountries([]);

                                // Add new country associations
                                if (Array.isArray(supportedCountries)) {
                                    const countryAssociations = [];
                                    supportedCountries.forEach(countryCode => {
                                        const country = countryMap.get(countryCode.toLowerCase());
                                        if (country) {
                                            countryAssociations.push({
                                                countryId: country.id,
                                                isDefault: countryAssociations.length === 0
                                            });
                                        }
                                    });

                                    // Add all countries at once
                                    if (countryAssociations.length > 0) {
                                        await plan.addCountries(
                                            countryAssociations.map(assoc => assoc.countryId),
                                            {
                                                through: countryAssociations.map(assoc => ({
                                                    isDefault: assoc.isDefault,
                                                    id: uuidv4()
                                                }))
                                            }
                                        );
                                    }
                                }

                            } catch (error) {
                                console.error(`[Sync] Error processing plan ${externalPlan.id}:`, error);
                                results.failed++;
                                results.errors.push({
                                    planId: externalPlan.id,
                                    error: error.message
                                });
                            }
                        }
                    } catch (error) {
                        console.error(`[Sync] Error fetching ${category} plans from ${provider.name}:`, error);
                        results.errors.push({
                            provider: provider.name,
                            category,
                            error: error.message
                        });
                    }
                }
            } catch (error) {
                console.error(`[Sync] Error processing provider ${provider.name}:`, error);
                results.errors.push({
                    provider: provider.name,
                    error: error.message
                });
            }
        }

        // Count how many plans remain inactive after sync
        const inactivePlans = await EsimPlan.count({
            where: {
                providerId: providers.map(p => p.id),
                isActive: false
            }
        });
        results.inactive = inactivePlans;

        // STEP 3: Run metadata update process on all plans that were created or updated
        console.log(`[Sync] Running metadata update for ${processedPlanIds.length} plans...`);
        if (processedPlanIds.length > 0) {
            try {
                // Find all processed plans
                const plansToUpdate = await EsimPlan.findAll({
                    where: {
                        id: {
                            [Op.in]: processedPlanIds
                        },
                        providerMetadata: {
                            [Op.not]: null
                        }
                    }
                });

                console.log(`[Sync] Found ${plansToUpdate.length} plans with provider metadata to update`);

                for (const [index, plan] of plansToUpdate.entries()) {
                    try {
                        if (index > 0 && index % 10 === 0) {
                            await delay(500); // 500ms delay
                        }

                        const metadata = plan.providerMetadata;
                        let planInfoContent = '';
                        let additionalInfoContent = '';

                        // Extract plan details from customData
                        if (metadata.customData) {
                            const planDetails = metadata.customData.find(d => d.name?.trim() === 'PLAN_DETAILS');
                            if (planDetails?.value) {
                                try {
                                    const parsed = JSON.parse(planDetails.value);

                                    // Store description in planInfo
                                    if (parsed.description) {
                                        planInfoContent += `<div>${parsed.description}</div>`;
                                    }

                                    // Store key features in planInfo
                                    if (parsed.items && Array.isArray(parsed.items) && parsed.items.length > 0) {
                                        planInfoContent += `
                                        <div class="mt-4">
                                            <h4 class="text-sm font-semibold mb-2">Key Features:</h4>
                                            <ul class="list-disc list-inside space-y-1">
                                                ${parsed.items.map(item => `<li>${item}</li>`).join('\n')}
                                            </ul>
                                        </div>`;
                                    }
                                } catch (e) {
                                    console.error(`[Sync] Error parsing PLAN_DETAILS for plan ${plan.id}:`, e.message);
                                }
                            }
                        }

                        // Extract additional information like usage tracking
                        if (metadata.usageTracking) {
                            additionalInfoContent += `
                            <div class="mt-4">
                                <h4 class="text-sm font-semibold mb-2">Usage Tracking:</h4>
                                <p>${metadata.usageTracking}</p>
                            </div>`;
                        }

                        // Extract any other useful information from metadata
                        if (metadata.productDetails && Array.isArray(metadata.productDetails)) {
                            const heading = metadata.productDetails.find(detail => detail.name === "heading")?.value;
                            if (heading) {
                                additionalInfoContent += `
                                <div class="mt-4">
                                    <h4 class="text-sm font-semibold mb-2">Product Details:</h4>
                                    <p>${heading}</p>
                                </div>`;
                            }

                            // Add other product details that might be useful
                            metadata.productDetails
                                .filter(detail =>
                                    detail.name !== "heading" &&
                                    detail.name !== "PLAN_DATA_LIMIT" &&
                                    detail.value)
                                .forEach(detail => {
                                    additionalInfoContent += `
                                    <div class="mt-2">
                                        <strong>${detail.name}:</strong> ${detail.value}
                                    </div>`;
                                });
                        }

                        // Extract original data if available
                        if (metadata.originalData) {
                            if (metadata.originalData.additionalDetails) {
                                additionalInfoContent += `
                                <div class="mt-4">
                                    <h4 class="text-sm font-semibold mb-2">Additional Details:</h4>
                                    <p>${metadata.originalData.additionalDetails}</p>
                                </div>`;
                            }
                        }

                        // Update the plan only if we have content to add
                        if (planInfoContent || additionalInfoContent) {
                            await plan.update({
                                planInfo: planInfoContent || plan.planInfo,
                                additionalInfo: additionalInfoContent || plan.additionalInfo
                            });
                            results.metadataUpdated++;
                            console.log(`[Sync] Updated metadata for plan: ${plan.id} - ${plan.name}`);
                        }
                    } catch (error) {
                        console.error(`[Sync] Error updating metadata for plan ${plan.id}:`, error);
                    }
                }

                console.log(`[Sync] Metadata update completed. Updated ${results.metadataUpdated} plans.`);

                if (io) {
                    io.emit('sync_progress', {
                        phase: 'metadata',
                        message: `Metadata update completed. Updated ${results.metadataUpdated} plans.`,
                        progress: 95
                    });
                }
            } catch (error) {
                console.error('[Sync] Error updating plan metadata:', error);
                results.errors.push({
                    phase: 'metadata_update',
                    error: error.message
                });
            }
        }

        if (io) {
            io.emit('sync_complete', {
                message: 'Sync operation completed successfully',
                results: results,
                timestamp: new Date().toISOString()
            });
        }

        console.log('[Sync] Operation completed:', results);

    } catch (error) {
        console.error('[Sync] Error in background sync operation:', error);

        if (io) {
            io.emit('sync_error', {
                message: 'Sync operation failed',
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }

        throw error; 
    }
}

// Sync BillionConnect plans specifically
exports.syncBillionConnectPlans = async (req, res) => {
    try {
        res.json({
            message: 'BillionConnect sync operation started in background',
            status: 'started',
            timestamp: new Date().toISOString()
        });

        const io = req.app.get('io');

        performBillionConnectSyncOperation(io).catch(error => {
            console.error('[BillionConnect Sync] Background sync failed:', error);
            if (io) {
                io.emit('billionconnect_sync_error', {
                    message: 'BillionConnect sync operation failed',
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        });

    } catch (error) {
        console.error('Error starting BillionConnect sync:', error);
        res.status(500).json({
            message: 'Failed to start BillionConnect sync operation',
            error: error.message
        });
    }
};

// Separate function to perform BillionConnect sync operation
async function performBillionConnectSyncOperation(io) {
    try {
        if (io) {
            io.emit('billionconnect_sync_progress', {
                phase: 'starting',
                message: 'Starting BillionConnect sync operation...',
                progress: 0
            });
        }

        // Get BillionConnect provider
        const provider = await Provider.findOne({
            where: {
                type: 'API',
                status: 'active',
                name: 'billionconnect'
            }
        });

        if (!provider) {
            throw new Error('BillionConnect provider not found or not active');
        }

        const results = {
            total: 0,
            updated: 0,
            created: 0,
            failed: 0,
            inactive: 0,
            errors: []
        };

        // Get all countries for mapping
        const allCountries = await Country.findAll({
            where: { isActive: true },
            attributes: ['id', 'name', 'iso3']
        });

        // Create a map of country names and ISO3 codes to IDs for quick lookup
        const countryMap = new Map();
        allCountries.forEach(country => {
            countryMap.set(country.name.toLowerCase(), country);
            countryMap.set(country.iso3.toLowerCase(), country);
            countryMap.set(country.id.toLowerCase(), country);
        });

        if (io) {
            io.emit('billionconnect_sync_progress', {
                phase: 'fetching',
                message: 'Fetching plans from BillionConnect API...',
                progress: 10
            });
        }

        // Mark all BillionConnect plans as inactive before syncing
        await EsimPlan.update(
            { isActive: false },
            {
                where: {
                    providerId: provider.id
                }
            }
        );
        console.log('[BillionConnect Sync] Marked all BillionConnect plans as inactive');

        try {
            const providerService = providerFactory.getProvider(provider.name);

            // Get product variants from BillionConnect with prices
            const externalPlans = await providerService.getProductsWithPrices();

            if (!Array.isArray(externalPlans)) {
                throw new Error('Invalid response from BillionConnect API');
            }

            console.log(`[BillionConnect Sync] Found ${externalPlans.length} plans from BillionConnect`);

            // Emit progress update
            if (io) {
                io.emit('billionconnect_sync_progress', {
                    phase: 'processing',
                    message: `Processing ${externalPlans.length} BillionConnect plans...`,
                    progress: 30
                });
            }

            for (const [index, externalPlan] of externalPlans.entries()) {
                try {
                    results.total++;
                    const standardizedPlan = await providerFactory.standardizeProduct(provider.name, externalPlan);


                    // Check if plan already exists (use externalProductId for variants)
                    let plan = await EsimPlan.findOne({
                        where: {
                            externalProductId: standardizedPlan.externalProductId
                        }
                    });

                    if (plan) {
                        // Fields to preserve from the existing plan
                        const fieldsToPreserve = {
                            name: plan.name, 
                            description: plan.description,
                            sellingPrice: plan.sellingPrice,
                            status: plan.status,
                            stockThreshold: plan.stockThreshold,
                            startDateEnabled: plan.startDateEnabled,
                            features: plan.features,
                            instructions: plan.instructions
                        };

                        // Preserve existing buying price if the new one is zero or invalid
                        if (!standardizedPlan.buyingPrice || standardizedPlan.buyingPrice <= 0) {
                            if (plan.buyingPrice && plan.buyingPrice > 0) {
                                fieldsToPreserve.buyingPrice = plan.buyingPrice;
                                console.log(`   ⚠️  Preserving existing buying price $${plan.buyingPrice} for ${plan.name} (API returned: $${standardizedPlan.buyingPrice})`);
                            }
                        }

                        // Update existing plan while preserving manual fields and setting isActive to true
                        await plan.update({
                            ...standardizedPlan,
                            ...fieldsToPreserve,
                            providerId: provider.id,
                            isActive: true
                        });
                        console.log(`   📝 [BillionConnect Sync] Preserved existing name: "${plan.name}" for SKU: ${standardizedPlan.externalSkuId}`);
                        results.updated++;
                    } else {
                        const providerPlansHidden = await isProviderPlansHidden(provider.id);

                        // Create new plan with isActive = true
                        plan = await EsimPlan.create({
                            ...standardizedPlan,
                            providerId: provider.id,
                            productId: await generateProductId(),
                            status: providerPlansHidden ? 'hidden' : 'visible',
                            isActive: true
                        });
                        results.created++;
                    }

                    const supportedCountries = standardizedPlan.supportedCountries || [];

                    await plan.setCountries([]);

                    // Add new country associations
                    if (Array.isArray(supportedCountries) && supportedCountries.length > 0) {
                        const countryAssociations = [];
                        supportedCountries.forEach(countryCode => {
                            const country = countryMap.get(countryCode.toLowerCase());
                            if (country) {
                                countryAssociations.push({
                                    countryId: country.id,
                                    isDefault: countryAssociations.length === 0
                                });
                            }
                        });

                        // Add all countries at once
                        if (countryAssociations.length > 0) {
                            await plan.addCountries(
                                countryAssociations.map(assoc => assoc.countryId),
                                {
                                    through: countryAssociations.map(assoc => ({
                                        isDefault: assoc.isDefault,
                                        id: uuidv4()
                                    }))
                                }
                            );
                        }
                    }

                    // Emit progress update every 10 plans
                    if (index > 0 && index % 10 === 0) {
                        const progress = 30 + (index / externalPlans.length) * 60;
                        if (io) {
                            io.emit('billionconnect_sync_progress', {
                                phase: 'processing',
                                message: `Processed ${index + 1}/${externalPlans.length} BillionConnect plans...`,
                                progress: Math.round(progress)
                            });
                        }
                    }

                } catch (error) {
                    console.error(`[BillionConnect Sync] Error processing plan ${externalPlan.id}:`, error);
                    results.failed++;
                    results.errors.push({
                        planId: externalPlan.id,
                        error: error.message
                    });
                }
            }

        } catch (error) {
            console.error('[BillionConnect Sync] Error fetching plans from BillionConnect:', error);
            results.errors.push({
                provider: provider.name,
                error: error.message
            });
        }

        // Count how many plans remain inactive after sync
        const inactivePlans = await EsimPlan.count({
            where: {
                providerId: provider.id,
                isActive: false
            }
        });
        results.inactive = inactivePlans;

        // Clear cache to ensure partners get fresh data
        invalidatePlanCache(null);
        console.log('[BillionConnect Sync] Cleared plan cache');

        if (io) {
            io.emit('billionconnect_sync_complete', {
                message: 'BillionConnect sync operation completed successfully',
                results: results,
                timestamp: new Date().toISOString()
            });
        }

        console.log('[BillionConnect Sync] Operation completed:', results);

    } catch (error) {
        console.error('[BillionConnect Sync] Error in background sync operation:', error);

        if (io) {
            io.emit('billionconnect_sync_error', {
                message: 'BillionConnect sync operation failed',
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }

        throw error;
    }
}

// Sync Mobimatter plans
exports.syncMobimatterPlans = async (req, res) => {
    try {
        const io = req.app.get('io');

        // Start the sync operation in the background
        performMobimatterSyncOperation(io).catch(error => {
            console.error('[Sync] Error syncing Mobimatter plans:', error);
            if (io) {
                io.emit('sync_error', {
                    message: 'Sync operation failed',
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        });

        res.json({
            message: 'Mobimatter plans sync operation started',
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('[Sync] Error starting Mobimatter sync operation:', error);
        res.status(500).json({
            message: 'Failed to start Mobimatter sync operation',
            error: error.message
        });
    }
};

// Separate function to perform the Mobimatter sync operation
async function performMobimatterSyncOperation(io) {
    try {
        if (io) {
            io.emit('sync_progress', {
                phase: 'starting',
                message: 'Starting Mobimatter sync operation...',
                progress: 0
            });
        }

        // Get Mobimatter provider
        const provider = await Provider.findOne({
            where: {
                type: 'API',
                status: 'active',
                name: 'mobimatter'
            }
        });

        if (!provider) {
            throw new Error('Mobimatter provider not found or not active');
        }

        const results = {
            total: 0,
            updated: 0,
            created: 0,
            failed: 0,
            inactive: 0,
            metadataUpdated: 0,
            errors: []
        };

        // Get all countries for mapping
        const allCountries = await Country.findAll({
            where: { isActive: true },
            attributes: ['id', 'name', 'iso3']
        });

        // Create a map of country names and ISO3 codes to IDs for quick lookup
        const countryMap = new Map();
        allCountries.forEach(country => {
            countryMap.set(country.name.toLowerCase(), country);
            countryMap.set(country.iso3.toLowerCase(), country);
            countryMap.set(country.id.toLowerCase(), country);
        });

        const categories = ['esim_realtime', 'esim_addon', 'esim_replacement'];

        if (io) {
            io.emit('sync_progress', {
                phase: 'preparation',
                message: 'Preparing Mobimatter sync operation...',
                progress: 5
            });
        }

        invalidatePlanCache(provider.id);
        console.log('[Sync] Cleared Mobimatter plan cache');

        // Mark all Mobimatter plans as inactive before syncing
        await EsimPlan.update(
            { isActive: false },
            {
                where: {
                    providerId: provider.id
                }
            }
        );
        console.log('[Sync] Marked all Mobimatter plans as inactive');

        // Emit progress update
        if (io) {
            io.emit('sync_progress', {
                phase: 'fetching',
                message: 'Fetching plans from Mobimatter...',
                progress: 10
            });
        }

        const fetchedExternalPlanIds = new Set();
        const processedPlanIds = [];

        let totalCategories = categories.length;
        let processedCategories = 0;

        const providerService = providerFactory.getProvider(provider.name);

        for (const category of categories) {
            try {
                console.log(`[Sync] Fetching ${category} plans from Mobimatter...`);

                if (io) {
                    io.emit('sync_progress', {
                        phase: 'fetching',
                        message: `Fetching ${category} plans from Mobimatter...`,
                        progress: 10 + (processedCategories / totalCategories) * 40
                    });
                }

                await delay(1000); // 1 second delay

                const externalPlans = await providerService.getProducts(category);

                if (!Array.isArray(externalPlans)) {
                    throw new Error(`Invalid response from Mobimatter for category ${category}`);
                }

                console.log(`[Sync] Found ${externalPlans.length} ${category} plans from Mobimatter`);

                processedCategories++;

                for (const [index, externalPlan] of externalPlans.entries()) {
                    try {
                        if (index > 0 && index % 5 === 0) {
                            await delay(300); // 300ms delay
                        }

                        results.total++;
                        const standardizedPlan = await providerFactory.standardizeProduct(provider.name, externalPlan);

                        // Add external plan ID to tracking set
                        if (standardizedPlan.externalProductId) {
                            fetchedExternalPlanIds.add(standardizedPlan.externalProductId);
                        }
                        if (standardizedPlan.externalSkuId) {
                            fetchedExternalPlanIds.add(standardizedPlan.externalSkuId);
                        }

                        standardizedPlan.category = category;

                        // Check if plan already exists
                        let plan = await EsimPlan.findOne({
                            where: {
                                [Op.or]: [
                                    { externalProductId: standardizedPlan.externalProductId },
                                    { externalSkuId: standardizedPlan.externalSkuId }
                                ]
                            }
                        });

                        if (plan) {
                            // Fields to preserve from the existing plan
                            const fieldsToPreserve = {
                                sellingPrice: plan.sellingPrice,
                                status: plan.status,
                                stockThreshold: plan.stockThreshold,
                                startDateEnabled: plan.startDateEnabled,
                                features: plan.features,
                                instructions: plan.instructions,
                                description: plan.description,
                                activationPolicy: plan.activationPolicy,
                                hotspot: plan.hotspot,
                                speed: plan.speed,
                                top_up: plan.top_up
                            };

                            // Update existing plan while preserving manual fields and setting isActive to true
                            await plan.update({
                                ...standardizedPlan,
                                ...fieldsToPreserve,
                                providerId: provider.id,
                                isActive: true
                            });
                            results.updated++;
                            processedPlanIds.push(plan.id);
                        } else {
                            const providerPlansHidden = await isProviderPlansHidden(provider.id);

                            // Create new plan with isActive = true
                            plan = await EsimPlan.create({
                                ...standardizedPlan,
                                providerId: provider.id,
                                status: providerPlansHidden ? 'hidden' : 'visible',
                                isActive: true
                            });
                            results.created++;
                            processedPlanIds.push(plan.id);
                        }

                        // Emit progress update
                        if (io && index % 10 === 0) {
                            io.emit('sync_progress', {
                                phase: 'processing',
                                message: `Processing ${category} plans (${index + 1}/${externalPlans.length})...`,
                                progress: 50 + (processedCategories / totalCategories) * 40
                            });
                        }

                    } catch (error) {
                        console.error(`[Sync] Error processing plan:`, error);
                        results.failed++;
                        results.errors.push({
                            plan: externalPlan,
                            error: error.message
                        });
                    }
                }

            } catch (error) {
                console.error(`[Sync] Error fetching ${category} plans from Mobimatter:`, error);
                results.errors.push({
                    category,
                    error: error.message
                });
            }
        }

        // Emit completion event
        if (io) {
            io.emit('sync_complete', {
                message: 'Mobimatter plans sync completed',
                results,
                timestamp: new Date().toISOString()
            });
        }

        console.log('[Sync] Mobimatter sync operation completed:', results);
        return results;

    } catch (error) {
        console.error('[Sync] Error in Mobimatter sync operation:', error);
        throw error;
    }
}

// Export plans to CSV with aggressive memory optimization
exports.exportPlans = async (req, res) => {
    try {
        const { category = 'esim_realtime' } = req.query;
        console.log(`Starting export for category: ${category}`);

        // Start memory monitoring
        memoryMonitor.startMonitoring();
        memoryMonitor.logMemoryStatus();

        // Get processing strategy based on available memory
        const strategy = memoryMonitor.getProcessingStrategy();
        console.log(`🎯 Processing Strategy: ${strategy.description}`);

        // Set response headers for CSV download first
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename=esim-plans-${category}-${new Date().toISOString().split('T')[0]}.csv`);

        // Get all countries for mapping (optimized query)
        const allCountries = await Country.findAll({
            attributes: ['id', 'name', 'iso3'],
            raw: true // Use raw queries to reduce object overhead
        });

        // Create a lightweight map for country lookups
        const countryMap = new Map();
        allCountries.forEach(country => {
            const key1 = country.name.toLowerCase();
            const key2 = country.iso3.toLowerCase();
            const key3 = country.id.toLowerCase();
            countryMap.set(key1, country.name);
            countryMap.set(key2, country.name);
            countryMap.set(key3, country.name);
        });

        // Write CSV header immediately
        const csvHeader = 'Product ID,Name,Network,Validity (Days),Data,Stock,Provider,Buying Price ($),Selling Price ($),Status,Region,Countries\n';
        res.write(csvHeader);

        const categoryFilter = category === 'topup' ? 'esim_addon' : category;

        // Pre-calculate stock counts for API providers to avoid repeated queries
        const apiProviderNames = ['mobimatter', 'billionconnect'];

        console.log('📊 Loading all plans with optimized single query...');

        // Check memory safety before loading all plans
        if (!memoryMonitor.isSafeToProcess()) {
            console.log('⚠️  Memory usage critical, waiting for memory to be available...');
            try {
                await memoryMonitor.waitForMemory(15000); // Wait up to 15 seconds
            } catch (error) {
                console.error('❌ Export aborted due to memory constraints:', error.message);
                throw new Error('Export aborted: Insufficient memory available');
            }
        }

        memoryMonitor.logMemoryStatus();

        // Load ALL plans in a single optimized query
        const plans = await sequelize.query(`
            SELECT
                p.id, p.productId, p.name, p.networkName, p.validityDays,
                p.planType, p.planData, p.planDataUnit, p.customPlanData,
                p.buyingPrice, p.sellingPrice, p.status, p.region,
                p.providerMetadata,
                pr.name as providerName, pr.type as providerType
            FROM esimplans p
            LEFT JOIN providers pr ON p.providerId = pr.id
            WHERE p.category = :category
            ORDER BY p.createdAt DESC
        `, {
            replacements: { category: categoryFilter },
            type: sequelize.QueryTypes.SELECT
        });

        console.log(`✅ Loaded ${plans.length} plans successfully`);
        memoryMonitor.logMemoryStatus();

        if (plans.length === 0) {
            console.log('No plans found for export');
            res.end();
            memoryMonitor.stopMonitoring();
            return;
        }

        // Get ALL countries for all plans in a single query
        const planIds = plans.map(p => p.id);
        console.log('📊 Loading plan countries...');

        const planCountries = await sequelize.query(`
            SELECT epc.esimPlanId, c.name as countryName
            FROM esimplancountries epc
            JOIN countries c ON epc.countryId = c.id
            WHERE epc.esimPlanId IN (:planIds)
        `, {
            replacements: { planIds },
            type: sequelize.QueryTypes.SELECT
        });

        // Create a map of plan countries
        const planCountryMap = new Map();
        planCountries.forEach(pc => {
            if (!planCountryMap.has(pc.esimPlanId)) {
                planCountryMap.set(pc.esimPlanId, []);
            }
            planCountryMap.get(pc.esimPlanId).push(pc.countryName);
        });

        console.log('📊 Loading stock counts...');

        // Get stock counts for non-API providers in a single query
        const nonApiPlans = plans.filter(p =>
            !apiProviderNames.includes(p.providerName?.toLowerCase())
        );

        let stockCounts = new Map();
        if (nonApiPlans.length > 0) {
            const stockResults = await sequelize.query(`
                SELECT esimPlanId, COUNT(*) as stockCount
                FROM esimstocks
                WHERE esimPlanId IN (:planIds) AND status = 'available'
                GROUP BY esimPlanId
            `, {
                replacements: { planIds: nonApiPlans.map(p => p.id) },
                type: sequelize.QueryTypes.SELECT
            });

            stockResults.forEach(sr => {
                stockCounts.set(sr.esimPlanId, sr.stockCount);
            });
        }

        console.log('📊 Processing and streaming all plans...');
        memoryMonitor.logMemoryStatus();

        let totalProcessed = 0;

        // Process ALL plans at once with adaptive memory management
        for (let i = 0; i < plans.length; i++) {
            const plan = plans[i];

            // Check memory periodically during processing (adaptive frequency)
            const checkFrequency = Math.min(100, Math.max(10, strategy.batchSize));
            if (i % checkFrequency === 0 && i > 0) {
                memoryMonitor.logMemoryStatus();

                // Force GC based on strategy frequency
                if (i % strategy.gcFrequency === 0 && memoryMonitor.isMemoryWarning()) {
                    console.log(`🗑️  Scheduled GC at item ${i} (${strategy.strategy} strategy)`);
                    memoryMonitor.forceGarbageCollection();
                }
            }
                try {
                    // Get stock count
                    let stockCount;
                    if (apiProviderNames.includes(plan.providerName?.toLowerCase())) {
                        stockCount = 'Unlimited';
                    } else {
                        stockCount = stockCounts.get(plan.id) || 0;
                    }

                    // Get data value
                    let dataValue;
                    if (plan.planType === 'Unlimited') {
                        dataValue = 'Unlimited';
                    } else if (plan.planType === 'Custom') {
                        dataValue = plan.customPlanData || '';
                    } else {
                        dataValue = `${plan.planData || ''} ${plan.planDataUnit || ''}`.trim();
                    }

                    // Handle countries
                    let countries = '';
                    if (plan.providerName?.toLowerCase() === 'mobimatter') {
                        try {
                            const metadata = typeof plan.providerMetadata === 'string'
                                ? JSON.parse(plan.providerMetadata)
                                : plan.providerMetadata;
                            const supportedCountries = metadata?.originalData?.supportedCountries || [];
                            countries = supportedCountries
                                .map(code => countryMap.get(code.toLowerCase()) || code)
                                .filter(Boolean)
                                .join('; ');
                        } catch (e) {
                            countries = '';
                        }
                    } else {
                        const planCountriesList = planCountryMap.get(plan.id) || [];
                        countries = planCountriesList.join('; ');
                    }

                    // Handle regions
                    let regions = '';
                    if (plan.region) {
                        try {
                            regions = Array.isArray(plan.region) ? plan.region.join('; ') : plan.region;
                        } catch (e) {
                            regions = plan.region || '';
                        }
                    }

                    // Create CSV row with minimal string operations
                    const csvRow = [
                        plan.productId || '',
                        `"${(plan.name || '').replace(/"/g, '""')}"`,
                        `"${(plan.networkName || '').replace(/"/g, '""')}"`,
                        plan.validityDays || '',
                        `"${dataValue}"`,
                        stockCount,
                        `"${plan.providerName || 'N/A'}"`,
                        plan.buyingPrice || '',
                        plan.sellingPrice || '',
                        plan.status || '',
                        `"${regions || 'Global'}"`,
                        `"${countries}"`
                    ].join(',') + '\n';

                    res.write(csvRow);
                    totalProcessed++;

                } catch (planError) {
                    console.error(`Error processing plan ${plan.id}:`, planError.message);
                }
        }

        // Clear references to help garbage collection
        plans.length = 0;
        planCountries.length = 0;
        stockCounts.clear();
        planCountryMap.clear();

        console.log(`Export completed successfully. Total plans processed: ${totalProcessed}`);
        memoryMonitor.stopMonitoring();
        memoryMonitor.logMemoryStatus();
        res.end();

    } catch (error) {
        console.error('Error exporting plans:', error);
        memoryMonitor.stopMonitoring();

        if (!res.headersSent) {
            res.status(500).json({
                message: 'Failed to export plans',
                error: error.message
            });
        } else {
            res.end();
        }
    }
};

// Get exchange rate information
exports.getExchangeRateInfo = async (req, res) => {
    try {
        const exchangeRateInfo = await exchangeRateService.getExchangeRateInfo();

        res.json({
            success: true,
            data: exchangeRateInfo,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error getting exchange rate info:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get exchange rate information',
            error: error.message
        });
    }
};

// Refresh exchange rate cache
exports.refreshExchangeRate = async (req, res) => {
    try {
        exchangeRateService.clearCache();

        const newRate = await exchangeRateService.getCNYToUSDRate(true);

        res.json({
            success: true,
            message: 'Exchange rate refreshed successfully',
            data: {
                newRate: newRate,
                timestamp: new Date().toISOString()
            }
        });
    } catch (error) {
        console.error('Error refreshing exchange rate:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to refresh exchange rate',
            error: error.message
        });
    }
};

// Export all controller functions
module.exports = {
    getAllPlans: exports.getAllPlans,
    getPlanById: exports.getPlanById,
    searchPlans: exports.searchPlans,
    getEsimPlans: exports.getEsimPlans,
    getEsimPlan: exports.getEsimPlan,
    createEsimPlan: exports.createEsimPlan,
    createBulkEsimPlans: exports.createBulkEsimPlans,
    updateEsimPlan: exports.updateEsimPlan,
    deleteEsimPlan: exports.deleteEsimPlan,
    updateEsimPlanStatus: exports.updateEsimPlanStatus,
    updateProviderPlansVisibility: exports.updateProviderPlansVisibility,
    updateEsimPlanPrice: exports.updateEsimPlanPrice,
    resetEsimPlanPrice: exports.resetEsimPlanPrice,
    getPartnerEsimPlans: exports.getPartnerEsimPlans,
    getPartnerEsimPlan: exports.getPartnerEsimPlan,
    toggleStartDate: exports.toggleStartDate,
    updateStockThreshold: exports.updateStockThreshold,
    syncExternalPlans: exports.syncExternalPlans,
    syncBillionConnectPlans: exports.syncBillionConnectPlans,
    syncMobimatterPlans: exports.syncMobimatterPlans,
    exportPlans: exports.exportPlans,
    getExchangeRateInfo: exports.getExchangeRateInfo,
    refreshExchangeRate: exports.refreshExchangeRate
};
