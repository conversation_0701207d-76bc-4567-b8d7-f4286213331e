const models = require('../models');
const { Op } = require('sequelize');
const { processStoredNotification } = require('../controllers/orderController');

/**
 * Background service for processing stored notifications
 */
class NotificationProcessorService {
    constructor() {
        this.isProcessing = false;
        this.maxRetries = 3;
        this.retryDelayMs = 5000; // 5 seconds
        this.batchSize = 10;
        this.defaultInterval = 60000;
    }

    /**
     * Process pending notifications in batches
     */
    async processPendingNotifications() {
        if (this.isProcessing) {
            // Silently skip if already processing - no need to log this
            return;
        }

        this.isProcessing = true;
        let hadWork = false;

        try {
            // Get pending notifications
            const pendingNotifications = await models.NotificationMessage.findAll({
                where: {
                    status: 'pending'
                },
                order: [['receivedAt', 'ASC']],
                limit: this.batchSize
            });

            if (pendingNotifications.length > 0) {
                hadWork = true;
                console.log('Starting notification processing batch...');
                console.log(`Processing ${pendingNotifications.length} pending notifications`);

                // Process each notification
                for (const notification of pendingNotifications) {
                    try {
                        await processStoredNotification(notification.id);
                        console.log(`Successfully processed notification: ${notification.id}`);
                    } catch (error) {
                        console.error(`Error processing notification ${notification.id}:`, error);
                    }
                }
            }

            // Process failed notifications that can be retried
            const retriedCount = await this.retryFailedNotifications();
            if (retriedCount > 0) {
                hadWork = true;
            }

        } catch (error) {
            console.error('Error in notification processing batch:', error);
        } finally {
            this.isProcessing = false;
            // Only log completion when there was actual work done
            if (hadWork) {
                console.log('Notification processing batch completed');
            }
        }
    }

    /**
     * Retry failed notifications that haven't exceeded max retry attempts
     */
    async retryFailedNotifications() {
        let failedNotifications = [];

        try {
            // First, let's log how many failed notifications exist
            const totalFailedCount = await models.NotificationMessage.count({
                where: { status: 'failed' }
            });

            failedNotifications = await models.NotificationMessage.findAll({
                where: {
                    status: 'failed',
                    processingAttempts: {
                        [Op.lt]: this.maxRetries
                    },
                    [Op.or]: [
                        // Regular retry delay for most failures
                        {
                            lastProcessingAttempt: {
                                [Op.lt]: new Date(Date.now() - this.retryDelayMs)
                            },
                            processingError: {
                                [Op.notLike]: '%race condition%'
                            }
                        },
                        // Faster retry for race condition failures (2 seconds instead of 5)
                        {
                            lastProcessingAttempt: {
                                [Op.lt]: new Date(Date.now() - 2000)
                            },
                            processingError: {
                                [Op.like]: '%race condition%'
                            }
                        }
                    ]
                },
                order: [['lastProcessingAttempt', 'ASC']],
                limit: 5 // Limit retries per batch
            });

            if (failedNotifications.length === 0) {
                // Only log if there are failed notifications that aren't eligible
                if (totalFailedCount > 0) {
                    console.log(`Failed notifications status: ${totalFailedCount} total failed, 0 eligible for retry (max attempts reached or within retry delay)`);
                }
                return 0;
            }

            console.log(`Failed notifications status: ${totalFailedCount} total failed, ${failedNotifications.length} eligible for retry`);
            console.log(`Retrying ${failedNotifications.length} failed notifications`);

            for (const notification of failedNotifications) {
                try {
                    console.log(`Retrying notification ${notification.id} (attempt ${notification.processingAttempts + 1}/${this.maxRetries})`);

                    // Reset status to pending for retry - don't reset processingAttempts
                    // The processingAttempts will be incremented during processing
                    await notification.update({
                        status: 'pending'
                    });

                    await processStoredNotification(notification.id);
                    console.log(`Successfully retried notification: ${notification.id}`);
                } catch (error) {
                    console.error(`Error retrying notification ${notification.id}:`, error);
                }
            }
        } catch (error) {
            console.error('Error retrying failed notifications:', error);
            return 0;
        }

        return failedNotifications.length;
    }

    /**
     * Get detailed status of failed notifications for debugging
     */
    async getFailedNotificationStatus() {
        try {
            const failedNotifications = await models.NotificationMessage.findAll({
                where: { status: 'failed' },
                attributes: ['id', 'processingAttempts', 'lastProcessingAttempt', 'processingError', 'createdAt'],
                order: [['lastProcessingAttempt', 'DESC']],
                limit: 10
            });

            console.log('Failed Notifications Status:');
            failedNotifications.forEach(notification => {
                const timeSinceLastAttempt = Date.now() - new Date(notification.lastProcessingAttempt).getTime();
                const canRetry = notification.processingAttempts < this.maxRetries && timeSinceLastAttempt > this.retryDelayMs;

                console.log(`  ID: ${notification.id}`);
                console.log(`    Attempts: ${notification.processingAttempts}/${this.maxRetries}`);
                console.log(`    Last Attempt: ${notification.lastProcessingAttempt}`);
                console.log(`    Time Since: ${Math.round(timeSinceLastAttempt / 1000)}s (delay: ${this.retryDelayMs / 1000}s)`);
                console.log(`    Can Retry: ${canRetry}`);
                console.log(`    Error: ${notification.processingError}`);
                console.log('    ---');
            });

            return failedNotifications;
        } catch (error) {
            console.error('Error getting failed notification status:', error);
            return [];
        }
    }

    /**
     * Clean up old processed notifications
     * @param {number} daysOld - Number of days old to consider for cleanup
     */
    async cleanupOldNotifications(daysOld = 30) {
        try {
            const cutoffDate = new Date(Date.now() - (daysOld * 24 * 60 * 60 * 1000));

            const result = await models.NotificationMessage.destroy({
                where: {
                    status: ['completed', 'ignored'],
                    processedAt: {
                        [Op.lt]: cutoffDate
                    }
                }
            });

            if (result > 0) {
                console.log(`Cleaned up ${result} old notification records`);
            }
        } catch (error) {
            console.error('Error cleaning up old notifications:', error);
        }
    }

    /**
     * Get processing statistics
     */
    async getProcessingStats() {
        try {
            const stats = await models.NotificationMessage.findAll({
                attributes: [
                    'status',
                    [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count']
                ],
                group: ['status'],
                raw: true
            });

            const result = {
                pending: 0,
                processing: 0,
                completed: 0,
                failed: 0,
                ignored: 0
            };

            stats.forEach(stat => {
                result[stat.status] = parseInt(stat.count);
            });

            return result;
        } catch (error) {
            console.error('Error getting processing stats:', error);
            return null;
        }
    }

    /**
     * Start the background processor with interval or event-driven mode
     * @param {number} intervalMs - Processing interval in milliseconds (0 for event-driven only)
     * @param {boolean} enableQuietMode - If true, reduces logging noise
     */
    startProcessor(intervalMs = 0, enableQuietMode = true) { // Default: event-driven mode
        this.quietMode = enableQuietMode;
        this.intervalMs = intervalMs;

        if (intervalMs > 0) {
            console.log(`Starting notification processor with ${intervalMs}ms interval${enableQuietMode ? ' (quiet mode enabled)' : ''}`);

            // Process immediately
            this.processPendingNotifications();

            // Set up interval processing
            this.processingInterval = setInterval(() => {
                this.processPendingNotifications();
            }, intervalMs);
        } else {
            console.log('Starting notification processor in event-driven mode (triggers only when needed)');
        }

        // Set up daily cleanup
        this.cleanupInterval = setInterval(() => {
            this.cleanupOldNotifications();
        }, 24 * 60 * 60 * 1000); // Daily
    }

    /**
     * Trigger processing manually (for event-driven mode)
     * @param {number} delayMs - Optional delay before processing (for race conditions)
     */
    triggerProcessing(delayMs = 0) {
        // Don't trigger if already processing
        if (this.isProcessing) {
            return;
        }

        if (delayMs > 0) {
            // Clear any existing scheduled processing
            if (this.scheduledProcessing) {
                clearTimeout(this.scheduledProcessing);
            }

            this.scheduledProcessing = setTimeout(() => {
                this.scheduledProcessing = null;
                this.processPendingNotifications();
            }, delayMs);
        } else {
            // Process immediately if not already processing
            setImmediate(() => {
                this.processPendingNotifications();
            });
        }
    }

    /**
     * Schedule a retry for race condition scenarios
     */
    scheduleRetry(delayMs = 2000) {
        // Don't schedule retry if already processing
        if (this.isProcessing) {
            return;
        }

        // Clear any existing scheduled retry
        if (this.scheduledRetry) {
            clearTimeout(this.scheduledRetry);
        }

        this.scheduledRetry = setTimeout(() => {
            this.scheduledRetry = null;
            this.processPendingNotifications();
        }, delayMs);
    }

    /**
     * Stop the background processor
     */
    stopProcessor() {
        if (this.processingInterval) {
            clearInterval(this.processingInterval);
            this.processingInterval = null;
        }

        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }

        // Clear any scheduled processing
        if (this.scheduledProcessing) {
            clearTimeout(this.scheduledProcessing);
            this.scheduledProcessing = null;
        }

        if (this.scheduledRetry) {
            clearTimeout(this.scheduledRetry);
            this.scheduledRetry = null;
        }

        console.log('Notification processor stopped');
    }
}

module.exports = new NotificationProcessorService();
