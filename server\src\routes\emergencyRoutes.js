const express = require('express');
const router = express.Router();
const memoryMonitor = require('../utils/memoryMonitor');
const { emergencyCacheClear, getCacheStats } = require('../utils/cacheManager');

/**
 * Emergency memory cleanup endpoint
 * Use this when the application is experiencing memory issues
 */
router.post('/memory/cleanup', async (req, res) => {
    try {
        console.log('🚨 EMERGENCY: Manual memory cleanup requested');
        
        const beforeUsage = memoryMonitor.getMemoryUsage();
        const beforeCache = getCacheStats();
        
        console.log(`📊 Before cleanup: ${beforeUsage.heapUsedMB}MB (${beforeUsage.heapUsagePercent.toFixed(1)}%)`);
        console.log(`💾 Before cleanup: ${beforeCache.totalEntries} cache entries`);
        
        // Clear all caches
        emergencyCacheClear();
        
        // Clear product cache service
        const productCacheService = require('../services/productCache.service');
        if (productCacheService && typeof productCacheService.invalidateAllProductCaches === 'function') {
            productCacheService.invalidateAllProductCaches();
        }
        
        // Clear cache service
        const cacheService = require('../services/cache.service');
        if (cacheService && typeof cacheService.flush === 'function') {
            cacheService.flush();
        }
        
        // Force garbage collection
        const freed = memoryMonitor.forceGarbageCollection();
        
        // Wait a moment for GC to complete
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const afterUsage = memoryMonitor.getMemoryUsage();
        const afterCache = getCacheStats();
        
        const memoryFreed = beforeUsage.heapUsedMB - afterUsage.heapUsedMB;
        const cacheEntriesCleared = beforeCache.totalEntries - afterCache.totalEntries;
        
        console.log(`📊 After cleanup: ${afterUsage.heapUsedMB}MB (${afterUsage.heapUsagePercent.toFixed(1)}%)`);
        console.log(`💾 After cleanup: ${afterCache.totalEntries} cache entries`);
        console.log(`✅ Memory freed: ${memoryFreed.toFixed(1)}MB, Cache entries cleared: ${cacheEntriesCleared}`);
        
        res.json({
            success: true,
            message: 'Emergency memory cleanup completed',
            data: {
                before: {
                    memoryUsageMB: beforeUsage.heapUsedMB,
                    memoryUsagePercent: beforeUsage.heapUsagePercent,
                    cacheEntries: beforeCache.totalEntries
                },
                after: {
                    memoryUsageMB: afterUsage.heapUsedMB,
                    memoryUsagePercent: afterUsage.heapUsagePercent,
                    cacheEntries: afterCache.totalEntries
                },
                freed: {
                    memoryMB: memoryFreed,
                    cacheEntries: cacheEntriesCleared,
                    gcFreedMB: freed
                }
            }
        });
        
    } catch (error) {
        console.error('Error during emergency cleanup:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'CLEANUP_FAILED',
                message: 'Emergency cleanup failed: ' + error.message
            }
        });
    }
});

/**
 * Memory status endpoint
 */
router.get('/memory/status', (req, res) => {
    try {
        const usage = memoryMonitor.getMemoryUsage();
        const cacheStats = getCacheStats();
        const leakCheck = memoryMonitor.checkForMemoryLeaks();
        
        res.json({
            success: true,
            data: {
                memory: {
                    heapUsedMB: usage.heapUsedMB,
                    heapTotalMB: usage.heapTotalMB,
                    heapUsagePercent: usage.heapUsagePercent,
                    maxHeapMB: Math.round(memoryMonitor.maxHeapUsage / 1024 / 1024),
                    rss: usage.rss,
                    external: usage.external
                },
                cache: {
                    totalEntries: cacheStats.totalEntries,
                    validEntries: cacheStats.validEntries,
                    expiredEntries: cacheStats.expiredEntries,
                    hitRate: cacheStats.hitRate
                },
                memoryLeak: {
                    detected: leakCheck.detected,
                    score: leakCheck.score,
                    indicators: leakCheck.indicators
                },
                status: {
                    isSafeToProcess: memoryMonitor.isSafeToProcess(),
                    isWarning: memoryMonitor.isMemoryWarning(),
                    isCritical: memoryMonitor.isMemoryCritical()
                },
                recommendations: generateRecommendations(usage, cacheStats, leakCheck)
            }
        });
        
    } catch (error) {
        console.error('Error getting memory status:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'STATUS_FAILED',
                message: 'Failed to get memory status: ' + error.message
            }
        });
    }
});

/**
 * Force garbage collection endpoint
 */
router.post('/memory/gc', (req, res) => {
    try {
        const beforeUsage = memoryMonitor.getMemoryUsage();
        const freed = memoryMonitor.forceGarbageCollection();
        const afterUsage = memoryMonitor.getMemoryUsage();
        
        res.json({
            success: true,
            message: 'Garbage collection completed',
            data: {
                before: {
                    heapUsedMB: beforeUsage.heapUsedMB,
                    heapUsagePercent: beforeUsage.heapUsagePercent
                },
                after: {
                    heapUsedMB: afterUsage.heapUsedMB,
                    heapUsagePercent: afterUsage.heapUsagePercent
                },
                freedMB: freed
            }
        });
        
    } catch (error) {
        console.error('Error during garbage collection:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'GC_FAILED',
                message: 'Garbage collection failed: ' + error.message
            }
        });
    }
});

/**
 * Generate recommendations based on current status
 */
function generateRecommendations(usage, cacheStats, leakCheck) {
    const recommendations = [];
    
    if (usage.heapUsagePercent > 90) {
        recommendations.push('URGENT: Memory usage is critical - restart application immediately');
        recommendations.push('Clear all caches using /emergency/memory/cleanup');
    } else if (usage.heapUsagePercent > 75) {
        recommendations.push('WARNING: Memory usage is high - monitor closely');
        recommendations.push('Consider clearing caches');
    }
    
    if (cacheStats.totalEntries > 400) {
        recommendations.push('Cache size is high - consider reducing cache TTL');
    }
    
    if (cacheStats.expiredEntries > cacheStats.validEntries * 0.3) {
        recommendations.push('Many expired cache entries - run cleanup');
    }
    
    if (leakCheck.detected) {
        recommendations.push('Memory leak detected - investigate root cause');
        if (leakCheck.score >= 3) {
            recommendations.push('High leak score - restart application soon');
        }
    }
    
    if (recommendations.length === 0) {
        recommendations.push('System is running well - no immediate actions needed');
    }
    
    return recommendations;
}

module.exports = router;
