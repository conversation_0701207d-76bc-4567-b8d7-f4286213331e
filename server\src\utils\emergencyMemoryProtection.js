/**
 * Emergency Memory Protection System
 * Prevents memory leaks and crashes during intensive operations
 */

const MEMORY_THRESHOLD_MB = 1500; // 1.5GB threshold
const CRITICAL_THRESHOLD_MB = 1700; // 1.7GB critical threshold

class EmergencyMemoryProtection {
    constructor() {
        this.isMonitoring = false;
        this.operationStartMemory = 0;
        this.maxAllowedIncrease = 500; // 500MB max increase per operation
    }

    /**
     * Start monitoring memory for an operation
     * @param {string} operationName - Name of the operation being monitored
     * @returns {boolean} - True if operation can proceed, false if memory too high
     */
    startOperation(operationName = 'Unknown') {
        const currentMemory = process.memoryUsage();
        const currentMB = currentMemory.heapUsed / 1024 / 1024;
        
        console.log(`🔍 [${operationName}] Starting memory monitoring (Current: ${currentMB.toFixed(1)}MB)`);
        
        // Check if we're already at critical levels
        if (currentMB > CRITICAL_THRESHOLD_MB) {
            console.error(`🚨 [${operationName}] CRITICAL: Memory already at ${currentMB.toFixed(1)}MB - ABORTING OPERATION`);
            return false;
        }
        
        // Check if we're at warning levels
        if (currentMB > MEMORY_THRESHOLD_MB) {
            console.warn(`⚠️ [${operationName}] WARNING: Memory at ${currentMB.toFixed(1)}MB - proceeding with caution`);
        }
        
        this.isMonitoring = true;
        this.operationStartMemory = currentMB;
        this.operationName = operationName;
        
        return true;
    }

    /**
     * Check memory during operation
     * @returns {boolean} - True if operation can continue, false if should abort
     */
    checkMemoryDuringOperation() {
        if (!this.isMonitoring) return true;
        
        const currentMemory = process.memoryUsage();
        const currentMB = currentMemory.heapUsed / 1024 / 1024;
        const increase = currentMB - this.operationStartMemory;
        
        // Check for critical memory levels
        if (currentMB > CRITICAL_THRESHOLD_MB) {
            console.error(`🚨 [${this.operationName}] CRITICAL: Memory reached ${currentMB.toFixed(1)}MB - ABORTING`);
            this.endOperation();
            return false;
        }
        
        // Check for excessive memory increase
        if (increase > this.maxAllowedIncrease) {
            console.error(`🚨 [${this.operationName}] MEMORY LEAK: Increased by ${increase.toFixed(1)}MB - ABORTING`);
            this.endOperation();
            return false;
        }
        
        // Log warning if memory is increasing rapidly
        if (increase > 100) {
            console.warn(`⚠️ [${this.operationName}] Memory increased by ${increase.toFixed(1)}MB (Current: ${currentMB.toFixed(1)}MB)`);
        }
        
        return true;
    }

    /**
     * End operation monitoring
     * @returns {object} - Memory usage statistics
     */
    endOperation() {
        if (!this.isMonitoring) return null;
        
        const currentMemory = process.memoryUsage();
        const currentMB = currentMemory.heapUsed / 1024 / 1024;
        const increase = currentMB - this.operationStartMemory;
        
        const stats = {
            operationName: this.operationName,
            startMemory: this.operationStartMemory,
            endMemory: currentMB,
            memoryIncrease: increase,
            isHealthy: increase < 100 && currentMB < MEMORY_THRESHOLD_MB
        };
        
        if (stats.isHealthy) {
            console.log(`✅ [${this.operationName}] Completed successfully (Memory: ${currentMB.toFixed(1)}MB, +${increase.toFixed(1)}MB)`);
        } else {
            console.warn(`⚠️ [${this.operationName}] Completed with concerns (Memory: ${currentMB.toFixed(1)}MB, +${increase.toFixed(1)}MB)`);
        }
        
        this.isMonitoring = false;
        this.operationStartMemory = 0;
        this.operationName = null;
        
        return stats;
    }

    /**
     * Force garbage collection if available
     */
    forceGarbageCollection() {
        if (global.gc) {
            const beforeGC = process.memoryUsage().heapUsed / 1024 / 1024;
            global.gc();
            const afterGC = process.memoryUsage().heapUsed / 1024 / 1024;
            const freed = beforeGC - afterGC;
            console.log(`🗑️ Emergency GC: Freed ${freed.toFixed(1)}MB (${beforeGC.toFixed(1)}MB → ${afterGC.toFixed(1)}MB)`);
            return freed;
        } else {
            console.warn('⚠️ Garbage collection not available (run with --expose-gc)');
            return 0;
        }
    }

    /**
     * Get current memory status
     * @returns {object} - Current memory information
     */
    getMemoryStatus() {
        const memory = process.memoryUsage();
        const heapUsedMB = memory.heapUsed / 1024 / 1024;
        const heapTotalMB = memory.heapTotal / 1024 / 1024;
        const externalMB = memory.external / 1024 / 1024;
        
        return {
            heapUsed: heapUsedMB,
            heapTotal: heapTotalMB,
            external: externalMB,
            rss: memory.rss / 1024 / 1024,
            isWarning: heapUsedMB > MEMORY_THRESHOLD_MB,
            isCritical: heapUsedMB > CRITICAL_THRESHOLD_MB,
            percentUsed: (heapUsedMB / heapTotalMB) * 100
        };
    }

    /**
     * Emergency memory cleanup
     */
    emergencyCleanup() {
        console.log('🚨 EMERGENCY MEMORY CLEANUP INITIATED');
        
        // Force multiple garbage collection cycles
        let totalFreed = 0;
        for (let i = 0; i < 3; i++) {
            totalFreed += this.forceGarbageCollection();
        }
        
        // Clear any global caches if available
        if (global.clearAllCaches) {
            global.clearAllCaches();
        }
        
        const finalStatus = this.getMemoryStatus();
        console.log(`🚨 Emergency cleanup completed. Total freed: ${totalFreed.toFixed(1)}MB. Current: ${finalStatus.heapUsed.toFixed(1)}MB`);
        
        return finalStatus;
    }
}

// Create singleton instance
const emergencyMemoryProtection = new EmergencyMemoryProtection();

module.exports = emergencyMemoryProtection;
