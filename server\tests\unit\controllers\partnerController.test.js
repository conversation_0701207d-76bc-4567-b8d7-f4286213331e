const request = require('supertest');
const bcrypt = require('bcrypt');
const { User } = require('../../../src/models');
const sequelize = require('../../config/database');
const app = require('../../../app');
const emailService = require('../../../src/utils/emailService');

// Mock the email service to avoid sending actual emails during tests
jest.mock('../../../src/utils/emailService', () => ({
    sendPartnerWelcomeEmail: jest.fn(),
    sendAdminPartnerNotificationEmail: jest.fn()
}));

describe('Partner Controller Tests', () => {
    let server;
    let adminToken;
    let adminUser;

    beforeAll(async () => {
        try {
            await sequelize.authenticate();
            await sequelize.sync({ force: true });
            server = app.listen(0);

            // Create admin user for authentication
            const hashedPassword = await bcrypt.hash('adminpass123', 10);
            adminUser = await User.create({
                email: '<EMAIL>',
                password: hashedPassword,
                role: 'admin',
                firstName: 'Admin',
                lastName: 'User',
                isActive: true
            });

            // Get admin token
            const loginResponse = await request(server)
                .post('/api/auth/login')
                .send({
                    email: '<EMAIL>',
                    password: 'adminpass123'
                });
            
            adminToken = loginResponse.body.token;
        } catch (error) {
            console.error('Test setup failed:', error);
            throw error;
        }
    });

    afterAll(async () => {
        if (server) await server.close();
        await sequelize.close();
    });

    beforeEach(async () => {
        // Clear all users except admin
        await User.destroy({ 
            where: { 
                role: 'partner' 
            }, 
            force: true 
        });
        
        // Clear email service mocks
        jest.clearAllMocks();
    });

    describe('POST /api/partners', () => {
        const validPartnerData = {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            password: 'password123',
            businessName: 'Test Business Inc.',
            businessEmail: '<EMAIL>',
            phoneNumber: '+1234567890',
            countryId: 'US',
            markupPercentage: 15,
            billingAddressLine1: '123 Main Street',
            billingCity: 'New York',
            billingProvince: 'NY',
            billingCountryId: 'US',
            billingPostalCode: '10001'
        };

        it('should create partner successfully and send email notifications', async () => {
            const response = await request(server)
                .post('/api/partners')
                .set('Authorization', `Bearer ${adminToken}`)
                .send(validPartnerData);

            expect(response.status).toBe(201);
            expect(response.body).toHaveProperty('message', 'Partner created successfully');
            expect(response.body.partner).toHaveProperty('id');
            expect(response.body.partner.email).toBe(validPartnerData.email);

            // Verify partner was created in database
            const partner = await User.findOne({ where: { email: validPartnerData.email } });
            expect(partner).toBeTruthy();
            expect(partner.role).toBe('partner');

            // Verify welcome email was sent to partner
            expect(emailService.sendPartnerWelcomeEmail).toHaveBeenCalledWith(
                validPartnerData.email,
                expect.objectContaining({
                    email: validPartnerData.email,
                    firstName: validPartnerData.firstName,
                    lastName: validPartnerData.lastName
                }),
                validPartnerData.password
            );

            // Verify admin notification email was sent
            expect(emailService.sendAdminPartnerNotificationEmail).toHaveBeenCalledWith(
                [adminUser.email],
                expect.objectContaining({
                    email: validPartnerData.email,
                    firstName: validPartnerData.firstName,
                    lastName: validPartnerData.lastName
                })
            );
        });

        it('should create partner even if email sending fails', async () => {
            // Mock email service to throw error
            emailService.sendPartnerWelcomeEmail.mockRejectedValue(new Error('Email service error'));
            emailService.sendAdminPartnerNotificationEmail.mockRejectedValue(new Error('Email service error'));

            const response = await request(server)
                .post('/api/partners')
                .set('Authorization', `Bearer ${adminToken}`)
                .send(validPartnerData);

            expect(response.status).toBe(201);
            expect(response.body).toHaveProperty('message', 'Partner created successfully');

            // Verify partner was still created despite email failures
            const partner = await User.findOne({ where: { email: validPartnerData.email } });
            expect(partner).toBeTruthy();
        });

        it('should fail with duplicate email', async () => {
            // Create existing partner
            await User.create({
                ...validPartnerData,
                role: 'partner'
            });

            const response = await request(server)
                .post('/api/partners')
                .set('Authorization', `Bearer ${adminToken}`)
                .send(validPartnerData);

            expect(response.status).toBe(400);
            expect(response.body).toHaveProperty('message', 'Email already registered');

            // Verify no emails were sent for failed creation
            expect(emailService.sendPartnerWelcomeEmail).not.toHaveBeenCalled();
            expect(emailService.sendAdminPartnerNotificationEmail).not.toHaveBeenCalled();
        });

        it('should fail with invalid markup percentage', async () => {
            const invalidData = {
                ...validPartnerData,
                markupPercentage: 150 // Invalid: > 100
            };

            const response = await request(server)
                .post('/api/partners')
                .set('Authorization', `Bearer ${adminToken}`)
                .send(invalidData);

            expect(response.status).toBe(400);
            expect(response.body.message).toContain('Markup percentage must be between 0 and 100');

            // Verify no emails were sent for failed creation
            expect(emailService.sendPartnerWelcomeEmail).not.toHaveBeenCalled();
            expect(emailService.sendAdminPartnerNotificationEmail).not.toHaveBeenCalled();
        });

        it('should require admin authentication', async () => {
            const response = await request(server)
                .post('/api/partners')
                .send(validPartnerData);

            expect(response.status).toBe(401);
        });
    });
});
