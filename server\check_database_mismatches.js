const { Sequelize, DataTypes } = require('sequelize');
require('dotenv').config();

// Database configuration using environment variables
const sequelize = new Sequelize(
    process.env.DB_NAME || 'esim_demo',
    process.env.DB_USER || 'root',
    process.env.DB_PASSWORD || '',
    {
        host: process.env.DB_HOST || 'localhost',
        dialect: 'mysql',
        logging: false,
        pool: {
            max: 5,
            min: 0,
            acquire: 30000,
            idle: 10000
        },
        dialectOptions: {
            insecureAuth: true,
            charset: 'utf8mb4',
            connectTimeout: 60000
        }
    }
);

async function checkDatabaseMismatches() {
    try {
        console.log('🔍 Comprehensive Database Mismatch Check\n');
        console.log('=' .repeat(60));

        // Connect to database
        await sequelize.authenticate();
        console.log('✅ Database connection established\n');

        let totalIssues = 0;

        // Check 1: Name vs Validity Days Mismatches
        console.log('📋 CHECK 1: Name vs Validity Days Mismatches');
        console.log('-'.repeat(50));
        
        const nameValidityMismatches = await sequelize.query(`
            SELECT id, externalProductId, externalSkuId, name, validityDays, providerId
            FROM esimplans 
            WHERE (
                (name LIKE '%(1 Day)%' AND validityDays != 1) OR
                (name LIKE '%(2 Days)%' AND validityDays != 2) OR
                (name LIKE '%(3 Days)%' AND validityDays != 3) OR
                (name LIKE '%(4 Days)%' AND validityDays != 4) OR
                (name LIKE '%(5 Days)%' AND validityDays != 5) OR
                (name LIKE '%(6 Days)%' AND validityDays != 6) OR
                (name LIKE '%(7 Days)%' AND validityDays != 7) OR
                (name LIKE '%(8 Days)%' AND validityDays != 8) OR
                (name LIKE '%(9 Days)%' AND validityDays != 9) OR
                (name LIKE '%(10 Days)%' AND validityDays != 10) OR
                (name LIKE '%(11 Days)%' AND validityDays != 11) OR
                (name LIKE '%(12 Days)%' AND validityDays != 12) OR
                (name LIKE '%(13 Days)%' AND validityDays != 13) OR
                (name LIKE '%(14 Days)%' AND validityDays != 14) OR
                (name LIKE '%(15 Days)%' AND validityDays != 15) OR
                (name LIKE '%(20 Days)%' AND validityDays != 20) OR
                (name LIKE '%(21 Days)%' AND validityDays != 21) OR
                (name LIKE '%(30 Days)%' AND validityDays != 30) OR
                (name LIKE '%(45 Days)%' AND validityDays != 45) OR
                (name LIKE '%(60 Days)%' AND validityDays != 60) OR
                (name LIKE '%(90 Days)%' AND validityDays != 90)
            )
            ORDER BY name, validityDays
        `, { type: Sequelize.QueryTypes.SELECT });

        if (nameValidityMismatches.length > 0) {
            console.log(`❌ Found ${nameValidityMismatches.length} name/validity mismatches:`);
            nameValidityMismatches.forEach((plan, index) => {
                const nameMatch = plan.name.match(/\((\d+) Days?\)/);
                const expectedValidity = nameMatch ? parseInt(nameMatch[1]) : 'Unknown';
                console.log(`  ${index + 1}. ${plan.name}`);
                console.log(`     Expected: ${expectedValidity} days, Actual: ${plan.validityDays} days`);
                console.log(`     ExternalProductId: ${plan.externalProductId}`);
                console.log(`     ExternalSkuId: ${plan.externalSkuId}`);
                console.log('');
            });
            totalIssues += nameValidityMismatches.length;
        } else {
            console.log('✅ No name/validity mismatches found');
        }

        // Check 2: Duplicate Plans (same externalSkuId + validityDays)
        console.log('\n📋 CHECK 2: Duplicate Plans');
        console.log('-'.repeat(50));
        
        const duplicatePlans = await sequelize.query(`
            SELECT externalSkuId, validityDays, COUNT(*) as count, 
                   GROUP_CONCAT(id) as ids, 
                   GROUP_CONCAT(name) as names,
                   GROUP_CONCAT(externalProductId) as productIds
            FROM esimplans 
            WHERE externalSkuId IS NOT NULL AND externalSkuId != ''
            GROUP BY externalSkuId, validityDays
            HAVING COUNT(*) > 1
            ORDER BY externalSkuId, validityDays
        `, { type: Sequelize.QueryTypes.SELECT });

        if (duplicatePlans.length > 0) {
            console.log(`❌ Found ${duplicatePlans.length} sets of duplicate plans:`);
            duplicatePlans.forEach((dup, index) => {
                console.log(`  ${index + 1}. SKU: ${dup.externalSkuId}, ValidityDays: ${dup.validityDays}`);
                console.log(`     Count: ${dup.count} duplicates`);
                console.log(`     Plan IDs: ${dup.ids}`);
                console.log(`     Names: ${dup.names.substring(0, 100)}${dup.names.length > 100 ? '...' : ''}`);
                console.log('');
            });
            totalIssues += duplicatePlans.reduce((sum, dup) => sum + (dup.count - 1), 0);
        } else {
            console.log('✅ No duplicate plans found');
        }

        // Check 3: ExternalProductId Format Issues
        console.log('\n📋 CHECK 3: ExternalProductId Format Issues');
        console.log('-'.repeat(50));
        
        const formatIssues = await sequelize.query(`
            SELECT id, externalProductId, externalSkuId, name, validityDays
            FROM esimplans 
            WHERE externalProductId IS NOT NULL 
            AND externalSkuId IS NOT NULL
            AND externalProductId != CONCAT(externalSkuId, '_', validityDays)
            AND externalProductId != externalSkuId  -- Allow base products without suffix
            ORDER BY name
        `, { type: Sequelize.QueryTypes.SELECT });

        if (formatIssues.length > 0) {
            console.log(`❌ Found ${formatIssues.length} externalProductId format issues:`);
            formatIssues.forEach((plan, index) => {
                const expectedFormat = `${plan.externalSkuId}_${plan.validityDays}`;
                console.log(`  ${index + 1}. ${plan.name}`);
                console.log(`     Current: ${plan.externalProductId}`);
                console.log(`     Expected: ${expectedFormat}`);
                console.log(`     ValidityDays: ${plan.validityDays}`);
                console.log('');
            });
            totalIssues += formatIssues.length;
        } else {
            console.log('✅ No externalProductId format issues found');
        }

        // Check 4: Missing ExternalProductId or ExternalSkuId
        console.log('\n📋 CHECK 4: Missing External IDs');
        console.log('-'.repeat(50));
        
        const missingIds = await sequelize.query(`
            SELECT id, name, externalProductId, externalSkuId, providerId
            FROM esimplans 
            WHERE (externalProductId IS NULL OR externalProductId = '') 
            OR (externalSkuId IS NULL OR externalSkuId = '')
            ORDER BY name
        `, { type: Sequelize.QueryTypes.SELECT });

        if (missingIds.length > 0) {
            console.log(`⚠️  Found ${missingIds.length} plans with missing external IDs:`);
            missingIds.forEach((plan, index) => {
                console.log(`  ${index + 1}. ${plan.name}`);
                console.log(`     ExternalProductId: ${plan.externalProductId || 'MISSING'}`);
                console.log(`     ExternalSkuId: ${plan.externalSkuId || 'MISSING'}`);
                console.log('');
            });
            totalIssues += missingIds.length;
        } else {
            console.log('✅ All plans have external IDs');
        }

        // Check 5: Plans with Zero or Negative Validity Days
        console.log('\n📋 CHECK 5: Invalid Validity Days');
        console.log('-'.repeat(50));
        
        const invalidValidity = await sequelize.query(`
            SELECT id, name, validityDays, category
            FROM esimplans 
            WHERE validityDays <= 0 AND category != 'esim_replacement'
            ORDER BY validityDays, name
        `, { type: Sequelize.QueryTypes.SELECT });

        if (invalidValidity.length > 0) {
            console.log(`❌ Found ${invalidValidity.length} plans with invalid validity days:`);
            invalidValidity.forEach((plan, index) => {
                console.log(`  ${index + 1}. ${plan.name}`);
                console.log(`     ValidityDays: ${plan.validityDays}`);
                console.log(`     Category: ${plan.category}`);
                console.log('');
            });
            totalIssues += invalidValidity.length;
        } else {
            console.log('✅ All plans have valid validity days');
        }

        // Check 6: Plans with Inconsistent Provider Data
        console.log('\n📋 CHECK 6: Provider Consistency Check');
        console.log('-'.repeat(50));
        
        const providerIssues = await sequelize.query(`
            SELECT p.id, p.name, p.providerId, pr.name as providerName, pr.status as providerStatus
            FROM esimplans p
            LEFT JOIN providers pr ON p.providerId = pr.id
            WHERE pr.id IS NULL OR pr.status != 'active'
            ORDER BY p.name
        `, { type: Sequelize.QueryTypes.SELECT });

        if (providerIssues.length > 0) {
            console.log(`⚠️  Found ${providerIssues.length} plans with provider issues:`);
            providerIssues.forEach((plan, index) => {
                console.log(`  ${index + 1}. ${plan.name}`);
                console.log(`     Provider: ${plan.providerName || 'MISSING'}`);
                console.log(`     Provider Status: ${plan.providerStatus || 'N/A'}`);
                console.log('');
            });
        } else {
            console.log('✅ All plans have valid active providers');
        }

        // Summary
        console.log('\n' + '='.repeat(60));
        console.log('📊 COMPREHENSIVE SUMMARY');
        console.log('='.repeat(60));
        console.log(`Total Issues Found: ${totalIssues}`);
        console.log(`Name/Validity Mismatches: ${nameValidityMismatches.length}`);
        console.log(`Duplicate Plans: ${duplicatePlans.reduce((sum, dup) => sum + (dup.count - 1), 0)}`);
        console.log(`Format Issues: ${formatIssues.length}`);
        console.log(`Missing External IDs: ${missingIds.length}`);
        console.log(`Invalid Validity Days: ${invalidValidity.length}`);
        console.log(`Provider Issues: ${providerIssues.length}`);

        if (totalIssues === 0) {
            console.log('\n🎉 EXCELLENT! No database mismatches found. Your database is clean!');
        } else {
            console.log(`\n⚠️  Found ${totalIssues} total issues that need attention.`);
            console.log('💡 Consider running the fix scripts to resolve these issues.');
        }

        console.log('\n✅ Database mismatch check completed!');

    } catch (error) {
        console.error('❌ Error during database mismatch check:', error);
    } finally {
        await sequelize.close();
    }
}

// Run the check
checkDatabaseMismatches();
