/**
 * Memory monitoring utility for preventing out-of-memory crashes
 */

class MemoryMonitor {
    constructor(options = {}) {
        // Auto-detect available memory or use provided value
        this.maxHeapUsage = options.maxHeapUsage || this.detectMaxHeapSize();
        this.warningThreshold = options.warningThreshold || 0.65; // 65% of max (more aggressive)
        this.criticalThreshold = options.criticalThreshold || 0.75; // 75% of max (more aggressive)
        this.checkInterval = options.checkInterval || 2000; // 2 seconds (less frequent)
        this.isMonitoring = false;
        this.lastGCTime = 0;
        this.gcCooldown = 5000; // 5 seconds between forced GC
        this.consecutiveCriticalCount = 0;
        this.callbacks = {
            warning: [],
            critical: [],
            normal: []
        };

        console.log(`🧠 Memory Monitor initialized with ${Math.round(this.maxHeapUsage / 1024 / 1024)}MB limit`);
        console.log(`⚠️  Warning threshold: ${(this.warningThreshold * 100).toFixed(1)}%, Critical: ${(this.criticalThreshold * 100).toFixed(1)}%`);
    }

    /**
     * Auto-detect maximum heap size based on container limits and system memory
     */
    detectMaxHeapSize() {
        // Get Node.js heap size limit
        const v8 = require('v8');
        const heapStats = v8.getHeapStatistics();
        const nodeHeapLimit = heapStats.heap_size_limit;

        // Get system memory info
        const os = require('os');
        const totalSystemMemory = os.totalmem();
        const freeSystemMemory = os.freemem();

        console.log(`🔍 System Memory: ${Math.round(totalSystemMemory / 1024 / 1024)}MB total, ${Math.round(freeSystemMemory / 1024 / 1024)}MB free`);
        console.log(`🔍 Node.js Heap Limit: ${Math.round(nodeHeapLimit / 1024 / 1024)}MB`);

        // Detect container memory limit (Docker, Render, etc.)
        const containerLimit = this.detectContainerMemoryLimit();
        if (containerLimit) {
            console.log(`🐳 Container Memory Limit: ${Math.round(containerLimit / 1024 / 1024)}MB`);
        }

        // Use the most restrictive limit
        const effectiveSystemMemory = containerLimit || totalSystemMemory;
        console.log(`📊 Effective System Memory: ${Math.round(effectiveSystemMemory / 1024 / 1024)}MB`);

        // Use 85% of Node.js heap limit as safe maximum, but respect container limits
        const safeHeapLimit = Math.min(nodeHeapLimit * 0.85, effectiveSystemMemory * 0.75);

        // Additional safety checks based on effective system memory
        let recommendedLimit;

        if (effectiveSystemMemory < 1024 * 1024 * 1024) { // Less than 1GB system memory
            recommendedLimit = Math.min(safeHeapLimit, effectiveSystemMemory * 0.6); // 60% of available
            console.log('⚠️  Low memory system detected (< 1GB)');
        } else if (effectiveSystemMemory < 2 * 1024 * 1024 * 1024) { // Less than 2GB system memory
            recommendedLimit = Math.min(safeHeapLimit, effectiveSystemMemory * 0.65); // 65% of available
            console.log('⚠️  Limited memory system detected (< 2GB)');
        } else if (effectiveSystemMemory < 4 * 1024 * 1024 * 1024) { // Less than 4GB system memory
            recommendedLimit = Math.min(safeHeapLimit, effectiveSystemMemory * 0.7); // 70% of available
            console.log('📊 Medium memory system detected (< 4GB)');
        } else {
            recommendedLimit = safeHeapLimit; // Use Node.js limit for high memory systems
            console.log('🚀 High memory system detected (>= 4GB)');
        }

        return recommendedLimit;
    }

    /**
     * Detect container memory limit from cgroups (Docker, Kubernetes, Render, etc.)
     */
    detectContainerMemoryLimit() {
        const fs = require('fs');

        try {
            // Try cgroups v2 first (newer systems)
            const cgroupV2Path = '/sys/fs/cgroup/memory.max';
            if (fs.existsSync(cgroupV2Path)) {
                const limit = fs.readFileSync(cgroupV2Path, 'utf8').trim();
                if (limit !== 'max' && !isNaN(parseInt(limit))) {
                    return parseInt(limit);
                }
            }

            // Try cgroups v1 (older systems)
            const cgroupV1Path = '/sys/fs/cgroup/memory/memory.limit_in_bytes';
            if (fs.existsSync(cgroupV1Path)) {
                const limit = parseInt(fs.readFileSync(cgroupV1Path, 'utf8').trim());
                // Ignore very large limits (usually means no limit set)
                // Use 100GB as a reasonable upper bound for real container limits
                if (limit < 100 * 1024 * 1024 * 1024) {
                    return limit;
                }
            }

            // Try Docker-specific paths
            const dockerMemoryPath = '/sys/fs/cgroup/memory.current';
            if (fs.existsSync(dockerMemoryPath)) {
                // This gives current usage, but we can check if there's a limit file nearby
                const dockerLimitPath = '/sys/fs/cgroup/memory.max';
                if (fs.existsSync(dockerLimitPath)) {
                    const limit = fs.readFileSync(dockerLimitPath, 'utf8').trim();
                    if (limit !== 'max' && !isNaN(parseInt(limit))) {
                        return parseInt(limit);
                    }
                }
            }

            // Check environment variables (some platforms set these)
            if (process.env.MEMORY_LIMIT) {
                const envLimit = parseInt(process.env.MEMORY_LIMIT);
                if (!isNaN(envLimit)) {
                    return envLimit * 1024 * 1024; // Assume MB
                }
            }

            // Cloud platform specific detection
            if (process.env.RENDER) {
                // Render.com detection - free tier is 512MB
                console.log('🌐 Render.com platform detected');
                return 512 * 1024 * 1024; // 512MB
            }

            // Heroku detection
            if (process.env.DYNO) {
                console.log('🌐 Heroku platform detected');
                // Heroku dynos have specific memory limits
                return 512 * 1024 * 1024; // Default to 512MB, can be overridden
            }

            // Railway detection
            if (process.env.RAILWAY_ENVIRONMENT) {
                console.log('🌐 Railway platform detected');
                return 512 * 1024 * 1024; // Default to 512MB
            }

            // Generic container detection - if system memory is suspiciously low
            const os = require('os');
            const totalMem = os.totalmem();
            if (totalMem <= 600 * 1024 * 1024) { // Less than 600MB suggests container limit
                console.log('🐳 Small container detected based on system memory');
                return Math.floor(totalMem * 0.9); // Use 90% of detected system memory
            }

        } catch (error) {
            console.log('🔍 Could not detect container memory limit:', error.message);
        }

        return null; // No container limit detected
    }

    /**
     * Get current memory usage information
     */
    getMemoryUsage() {
        const usage = process.memoryUsage();
        return {
            heapUsed: usage.heapUsed,
            heapTotal: usage.heapTotal,
            external: usage.external,
            rss: usage.rss,
            heapUsedMB: Math.round(usage.heapUsed / 1024 / 1024),
            heapTotalMB: Math.round(usage.heapTotal / 1024 / 1024),
            heapUsagePercent: (usage.heapUsed / this.maxHeapUsage) * 100
        };
    }

    /**
     * Check if memory usage is at warning level
     */
    isMemoryWarning() {
        const usage = this.getMemoryUsage();
        return usage.heapUsed > (this.maxHeapUsage * this.warningThreshold);
    }

    /**
     * Check if memory usage is at critical level
     */
    isMemoryCritical() {
        const usage = this.getMemoryUsage();
        return usage.heapUsed > (this.maxHeapUsage * this.criticalThreshold);
    }

    /**
     * Force garbage collection if available and not in cooldown
     */
    forceGarbageCollection() {
        const now = Date.now();

        // Check cooldown to prevent excessive GC calls
        if (now - this.lastGCTime < this.gcCooldown) {
            console.log(`⏳ GC cooldown active (${Math.round((this.gcCooldown - (now - this.lastGCTime)) / 1000)}s remaining)`);
            return 0;
        }

        if (global.gc) {
            console.log('🗑️  Forcing garbage collection...');
            const beforeUsage = this.getMemoryUsage();

            try {
                global.gc();
                this.lastGCTime = now;

                const afterUsage = this.getMemoryUsage();
                const freed = beforeUsage.heapUsedMB - afterUsage.heapUsedMB;

                if (freed > 0) {
                    console.log(`✅ Garbage collection completed. Freed ${freed}MB memory.`);
                    this.consecutiveCriticalCount = 0; // Reset critical counter on successful GC
                } else {
                    console.log(`⚠️  Garbage collection completed but freed ${freed}MB memory.`);
                    this.consecutiveCriticalCount++;
                }

                return freed;
            } catch (error) {
                console.error('❌ Error during garbage collection:', error);
                return 0;
            }
        } else {
            console.warn('⚠️  Garbage collection not available. Start node with --expose-gc flag.');
            return 0;
        }
    }

    /**
     * Add callback for memory events
     */
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    /**
     * Trigger callbacks for memory events
     */
    trigger(event, data) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in memory monitor callback:`, error);
                }
            });
        }
    }

    /**
     * Start monitoring memory usage
     */
    startMonitoring() {
        if (this.isMonitoring) return;

        this.isMonitoring = true;
        console.log('🔍 Starting memory monitoring...');

        this.monitorInterval = setInterval(() => {
            const usage = this.getMemoryUsage();

            if (this.isMemoryCritical()) {
                this.consecutiveCriticalCount++;
                console.warn(`🚨 CRITICAL: Memory usage at ${usage.heapUsagePercent.toFixed(1)}% (${usage.heapUsedMB}MB)`);

                // Check for imminent crash and activate prevention
                if (this.preventCrash()) {
                    // Emergency measures activated, skip normal processing
                    return;
                }

                // Check for imminent crash (>95% usage)
                if (usage.heapUsagePercent > 95) {
                    console.error(`🚨 IMMINENT CRASH WARNING: Memory at ${usage.heapUsagePercent.toFixed(1)}%`);
                    this.emergencyCacheClear();
                }

                this.trigger('critical', usage);

                // Force GC on critical memory usage with cooldown check
                const now = Date.now();
                if (now - this.lastGCTime > this.gcCooldown) {
                    const freed = this.forceGarbageCollection();

                    // Log GC effectiveness
                    if (freed < 1) {
                        console.warn(`⚠️  Garbage collection completed but freed ${freed}MB memory.`);
                    } else {
                        console.log(`✅ Garbage collection completed. Freed ${freed}MB memory.`);
                    }
                } else {
                    const remainingCooldown = Math.ceil((this.gcCooldown - (now - this.lastGCTime)) / 1000);
                    console.log(`⏳ GC cooldown active (${remainingCooldown}s remaining)`);
                }

                // Enhanced leak detection for consecutive critical states
                if (this.consecutiveCriticalCount > 3) {
                    console.error(`🚨 MEMORY LEAK SUSPECTED: ${this.consecutiveCriticalCount} consecutive critical states`);

                    // Check for memory leak patterns
                    const leakCheck = this.checkForMemoryLeaks();
                    if (leakCheck.detected && leakCheck.score >= 3) {
                        console.error(`💡 Consider restarting the application to prevent crash`);
                    }
                }

            } else if (this.isMemoryWarning()) {
                console.warn(`⚠️  WARNING: Memory usage at ${usage.heapUsagePercent.toFixed(1)}% (${usage.heapUsedMB}MB)`);
                this.trigger('warning', usage);

                // Proactive GC on warning level if we haven't done it recently
                const now = Date.now();
                if (now - this.lastGCTime > this.gcCooldown * 2) {
                    console.log('🗑️  Proactive garbage collection on warning level');
                    this.forceGarbageCollection();
                }
            } else {
                this.trigger('normal', usage);
                // Reset consecutive critical counter when memory is normal
                if (this.consecutiveCriticalCount > 0) {
                    this.consecutiveCriticalCount = 0;
                }
            }
        }, this.checkInterval);
    }

    /**
     * Stop monitoring memory usage
     */
    stopMonitoring() {
        if (!this.isMonitoring) return;
        
        this.isMonitoring = false;
        if (this.monitorInterval) {
            clearInterval(this.monitorInterval);
            this.monitorInterval = null;
        }
        console.log('🔍 Memory monitoring stopped.');
    }

    /**
     * Check if it's safe to continue processing
     */
    isSafeToProcess() {
        return !this.isMemoryCritical();
    }

    /**
     * Wait for memory to be available
     */
    async waitForMemory(maxWaitTime = 30000) {
        const startTime = Date.now();
        
        while (this.isMemoryCritical() && (Date.now() - startTime) < maxWaitTime) {
            console.log('⏳ Waiting for memory to be available...');
            this.forceGarbageCollection();
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        if (this.isMemoryCritical()) {
            throw new Error('Memory usage still critical after waiting. Aborting operation.');
        }
    }

    /**
     * Log current memory status
     */
    logMemoryStatus() {
        const usage = this.getMemoryUsage();
        console.log(`📊 Memory Status: ${usage.heapUsedMB}MB / ${Math.round(this.maxHeapUsage / 1024 / 1024)}MB (${usage.heapUsagePercent.toFixed(1)}%)`);
    }

    /**
     * Get recommended batch size based on available memory
     */
    getRecommendedBatchSize() {
        const maxHeapMB = this.maxHeapUsage / 1024 / 1024;

        if (maxHeapMB < 512) {
            return 10; // Very small batches for low memory
        } else if (maxHeapMB < 1024) {
            return 25; // Small batches for limited memory
        } else if (maxHeapMB < 2048) {
            return 50; // Medium batches for moderate memory
        } else {
            return 100; // Larger batches for high memory
        }
    }

    /**
     * Get memory-appropriate processing strategy
     */
    getProcessingStrategy() {
        const maxHeapMB = this.maxHeapUsage / 1024 / 1024;

        if (maxHeapMB < 512) {
            return {
                strategy: 'ultra-conservative',
                batchSize: 10,
                gcFrequency: 50, // Force GC every 50 items
                description: 'Ultra-conservative processing for very low memory systems'
            };
        } else if (maxHeapMB < 1024) {
            return {
                strategy: 'conservative',
                batchSize: 25,
                gcFrequency: 100, // Force GC every 100 items
                description: 'Conservative processing for limited memory systems'
            };
        } else if (maxHeapMB < 2048) {
            return {
                strategy: 'balanced',
                batchSize: 50,
                gcFrequency: 250, // Force GC every 250 items
                description: 'Balanced processing for moderate memory systems'
            };
        } else {
            return {
                strategy: 'aggressive',
                batchSize: 100,
                gcFrequency: 500, // Force GC every 500 items
                description: 'Aggressive processing for high memory systems'
            };
        }
    }

    /**
     * Check if we should use single-query or batch processing
     */
    shouldUseSingleQuery(estimatedRecordCount = 1000) {
        const maxHeapMB = this.maxHeapUsage / 1024 / 1024;
        const estimatedMemoryUsageMB = estimatedRecordCount * 0.1; // Rough estimate: 0.1MB per record

        // Use single query if estimated usage is less than 40% of available memory (more conservative)
        const shouldUse = estimatedMemoryUsageMB < (maxHeapMB * 0.4);

        console.log(`🤔 Memory Analysis: ${estimatedRecordCount} records ≈ ${estimatedMemoryUsageMB.toFixed(1)}MB`);
        console.log(`🤔 Available: ${maxHeapMB.toFixed(1)}MB, Threshold: ${(maxHeapMB * 0.4).toFixed(1)}MB`);
        console.log(`🎯 Strategy: ${shouldUse ? 'Single Query' : 'Batch Processing'}`);

        return shouldUse;
    }

    /**
     * Check for potential memory leaks
     */
    checkForMemoryLeaks() {
        const usage = this.getMemoryUsage();

        // Memory leak indicators
        const indicators = {
            highConsecutiveCritical: this.consecutiveCriticalCount > 5,
            lowGCEfficiency: this.lastGCTime > 0 && (Date.now() - this.lastGCTime) < 30000, // Recent GC but still high memory
            highHeapUsage: usage.heapUsagePercent > 90,
            suspiciousGrowth: usage.heapUsed > (this.maxHeapUsage * 0.9)
        };

        const leakScore = Object.values(indicators).filter(Boolean).length;

        if (leakScore >= 2) {
            console.error(`🚨 MEMORY LEAK DETECTED (Score: ${leakScore}/4)`);
            console.error(`   Indicators:`, indicators);
            console.error(`   Current usage: ${usage.heapUsagePercent.toFixed(1)}% (${usage.heapUsedMB}MB)`);
            console.error(`   Consecutive critical states: ${this.consecutiveCriticalCount}`);

            // Enhanced leak detection with emergency actions
            if (leakScore >= 3 || usage.heapUsagePercent > 95) {
                console.error(`🚨 CRITICAL MEMORY ALERT 🚨`);
                console.error(`   Leak score: ${leakScore}/4`);
                console.error(`   Current usage: ${usage.heapUsagePercent.toFixed(1)}%`);
                console.error(`   Indicators:`, indicators);
                console.error(`   URGENT: Application may crash soon - restart recommended`);

                // Emergency cache clearing
                this.emergencyCacheClear();
            }

            return {
                detected: true,
                score: leakScore,
                indicators,
                usage
            };
        }

        return {
            detected: false,
            score: leakScore,
            indicators,
            usage
        };
    }

    /**
     * Emergency cache clearing to prevent crash
     */
    emergencyCacheClear() {
        try {
            console.log('🚨 EMERGENCY: Clearing all caches to prevent crash...');

            // Clear plan cache
            const { planCache, emergencyCacheClear } = require('./cacheManager');
            if (emergencyCacheClear && typeof emergencyCacheClear === 'function') {
                emergencyCacheClear();
            } else if (planCache && typeof planCache.clear === 'function') {
                const cacheSize = planCache.size;
                planCache.clear();
                console.log(`🗑️ Cleared plan cache (${cacheSize} entries)`);
            }

            // Clear cache service
            const cacheService = require('../services/cache.service');
            if (cacheService && typeof cacheService.flush === 'function') {
                cacheService.flush();
                console.log('🗑️ Cleared cache service');
            }

            // Clear product cache service
            const productCacheService = require('../services/productCache.service');
            if (productCacheService && typeof productCacheService.invalidateAllProductCaches === 'function') {
                productCacheService.invalidateAllProductCaches();
                console.log('🗑️ Cleared product cache service');
            }

            // Force aggressive garbage collection multiple times
            console.log('🗑️  Forcing garbage collection...');
            const freed1 = this.forceGarbageCollection();

            // Wait a moment and try again
            setTimeout(() => {
                const freed2 = this.forceGarbageCollection();
                console.log(`🗑️  Total memory freed: ${freed1 + freed2}MB`);
            }, 1000);

            console.log('🚨 Emergency cache clearing completed');
            this.logMemoryStatus();

        } catch (error) {
            console.error('Error during emergency cache clear:', error);
        }
    }

    /**
     * Emergency shutdown prevention - last resort before crash
     */
    preventCrash() {
        const usage = this.getMemoryUsage();

        if (usage.heapUsagePercent > 98) {
            console.error('🚨 CRITICAL: Preventing imminent crash at 98%+ memory usage');

            // Emergency measures
            this.emergencyCacheClear();

            // Clear all intervals to reduce memory pressure
            if (this.monitorInterval) {
                clearInterval(this.monitorInterval);
                console.log('🚨 Stopped memory monitoring to reduce overhead');
            }

            // Force multiple GC cycles with delays
            this.emergencyGarbageCollection();

            console.error('🚨 EMERGENCY MEASURES ACTIVATED - Application may need restart');
            return true;
        }

        return false;
    }

    /**
     * Emergency garbage collection - multiple aggressive cycles
     */
    emergencyGarbageCollection() {
        console.log('🚨 EMERGENCY: Running aggressive garbage collection cycles...');

        // Immediate GC
        const freed1 = this.forceGarbageCollection();
        console.log(`🗑️ Emergency GC cycle 1: freed ${freed1}MB`);

        // Additional cycles with delays
        setTimeout(() => {
            const freed2 = this.forceGarbageCollection();
            console.log(`🗑️ Emergency GC cycle 2: freed ${freed2}MB`);
        }, 500);

        setTimeout(() => {
            const freed3 = this.forceGarbageCollection();
            console.log(`🗑️ Emergency GC cycle 3: freed ${freed3}MB`);

            // Final memory status
            setTimeout(() => {
                this.logMemoryStatus();
                console.log('🚨 Emergency GC cycles completed');
            }, 500);
        }, 1000);
    }
}

// Create singleton instance
const memoryMonitor = new MemoryMonitor();

module.exports = memoryMonitor;
