const hybridCache = require('../services/hybridCache.service');
const memoryMonitor = require('./memoryMonitor');
const crypto = require('crypto');
const zlib = require('zlib');
const { promisify } = require('util');

const gzip = promisify(zlib.gzip);
const gunzip = promisify(zlib.gunzip);

class OptimizedCacheManager {
    constructor() {
        // Cache duration strategies based on data volatility
        this.cacheDurations = {
            // Static data - cache longer
            countries: 7200,        // 2 hours
            regions: 7200,          // 2 hours
            providers: 3600,        // 1 hour
            
            // Semi-static data - moderate caching
            plans: 1800,            // 30 minutes
            planDetails: 1800,      // 30 minutes
            partnerConfig: 900,     // 15 minutes
            
            // Dynamic data - short caching
            dashboard: 300,         // 5 minutes
            searchResults: 300,     // 5 minutes
            apiResponses: 180,      // 3 minutes
            
            // Very dynamic data - minimal caching
            userSessions: 60,       // 1 minute
            realTimeData: 30        // 30 seconds
        };

        // Compression thresholds
        this.compressionThreshold = 1024; // 1KB - compress larger objects
        this.maxCacheSize = 10 * 1024 * 1024; // 10MB max per cache entry
    }

    /**
     * Generate optimized cache key with namespace and hashing
     */
    generateCacheKey(namespace, identifier, params = {}) {
        const baseKey = `${namespace}:${identifier}`;
        
        // If no params, return simple key
        if (Object.keys(params).length === 0) {
            return baseKey;
        }

        // Create deterministic hash for complex parameters
        const paramString = JSON.stringify(params, Object.keys(params).sort());
        const hash = crypto.createHash('md5').update(paramString).digest('hex').substring(0, 8);
        
        return `${baseKey}:${hash}`;
    }

    /**
     * Intelligent data compression based on size
     */
    async compressData(data) {
        const serialized = JSON.stringify(data);
        const size = Buffer.byteLength(serialized, 'utf8');
        
        // Don't compress small data
        if (size < this.compressionThreshold) {
            return {
                data: serialized,
                compressed: false,
                originalSize: size,
                finalSize: size
            };
        }

        try {
            const compressed = await gzip(serialized);
            const compressedSize = compressed.length;
            
            // Only use compression if it provides significant savings
            if (compressedSize < size * 0.8) {
                return {
                    data: compressed.toString('base64'),
                    compressed: true,
                    originalSize: size,
                    finalSize: compressedSize
                };
            }
        } catch (error) {
            console.warn('Compression failed, using uncompressed data:', error.message);
        }

        return {
            data: serialized,
            compressed: false,
            originalSize: size,
            finalSize: size
        };
    }

    /**
     * Decompress cached data
     */
    async decompressData(cacheEntry) {
        if (!cacheEntry.compressed) {
            return JSON.parse(cacheEntry.data);
        }

        try {
            const buffer = Buffer.from(cacheEntry.data, 'base64');
            const decompressed = await gunzip(buffer);
            return JSON.parse(decompressed.toString());
        } catch (error) {
            console.error('Decompression failed:', error);
            throw new Error('Failed to decompress cached data');
        }
    }

    /**
     * Smart cache retrieval with decompression
     */
    async get(namespace, identifier, params = {}) {
        try {
            const key = this.generateCacheKey(namespace, identifier, params);
            const cached = await hybridCache.get(key);
            
            if (!cached) {
                return null;
            }

            // Handle both old format (direct data) and new format (with metadata)
            if (cached.compressed !== undefined) {
                return await this.decompressData(cached);
            }
            
            return cached;
            
        } catch (error) {
            console.error('Cache get error:', error);
            return null;
        }
    }

    /**
     * Smart cache storage with compression and TTL optimization
     */
    async set(namespace, identifier, data, params = {}, customTtl = null) {
        try {
            // Check memory pressure
            if (memoryMonitor.isMemoryCritical()) {
                console.warn('⚠️ Memory critical - skipping cache');
                return false;
            }

            const key = this.generateCacheKey(namespace, identifier, params);
            const ttl = customTtl || this.cacheDurations[namespace] || 1800;

            // Check data size limits
            const dataSize = JSON.stringify(data).length;
            if (dataSize > this.maxCacheSize) {
                console.warn(`⚠️ Data too large to cache: ${dataSize} bytes`);
                return false;
            }

            // Compress data intelligently
            const compressed = await this.compressData(data);
            
            // Log compression stats for monitoring
            if (compressed.compressed) {
                const savings = ((compressed.originalSize - compressed.finalSize) / compressed.originalSize * 100).toFixed(1);
                console.log(`📦 Compressed cache entry: ${compressed.originalSize}B → ${compressed.finalSize}B (${savings}% savings)`);
            }

            return await hybridCache.set(key, compressed, ttl);
            
        } catch (error) {
            console.error('Cache set error:', error);
            return false;
        }
    }

    /**
     * Batch cache operations for better performance
     */
    async mget(requests) {
        const results = {};
        const promises = requests.map(async (req) => {
            const { namespace, identifier, params = {} } = req;
            const key = this.generateCacheKey(namespace, identifier, params);
            const value = await this.get(namespace, identifier, params);
            return { key: `${namespace}:${identifier}`, value };
        });

        const resolved = await Promise.allSettled(promises);
        resolved.forEach((result, index) => {
            if (result.status === 'fulfilled') {
                results[result.value.key] = result.value.value;
            }
        });

        return results;
    }

    /**
     * Batch cache set operations
     */
    async mset(entries) {
        const promises = entries.map(entry => {
            const { namespace, identifier, data, params = {}, ttl = null } = entry;
            return this.set(namespace, identifier, data, params, ttl);
        });

        const results = await Promise.allSettled(promises);
        return results.filter(r => r.status === 'fulfilled' && r.value).length;
    }

    /**
     * Cache invalidation with pattern support
     */
    async invalidate(namespace, identifier = null, params = {}) {
        try {
            if (identifier) {
                const key = this.generateCacheKey(namespace, identifier, params);
                return await hybridCache.del(key);
            }

            // For namespace-wide invalidation, we'd need Redis SCAN
            // For now, just clear the specific pattern
            console.log(`🗑️ Invalidating cache namespace: ${namespace}`);
            return true;
            
        } catch (error) {
            console.error('Cache invalidation error:', error);
            return false;
        }
    }

    /**
     * Cache warming for frequently accessed data
     */
    async warmCache(warmingConfig) {
        console.log('🔥 Starting cache warming...');
        let warmed = 0;

        for (const config of warmingConfig) {
            try {
                const { namespace, loader, params = {} } = config;
                const key = this.generateCacheKey(namespace, 'warm', params);
                
                // Check if already cached
                const existing = await this.get(namespace, 'warm', params);
                if (existing) {
                    console.log(`🔥 ${namespace} already warm`);
                    continue;
                }

                // Load and cache data
                const data = await loader(params);
                await this.set(namespace, 'warm', data, params);
                warmed++;
                
                console.log(`🔥 Warmed ${namespace} cache`);
                
            } catch (error) {
                console.error(`Failed to warm ${config.namespace}:`, error);
            }
        }

        console.log(`🔥 Cache warming completed: ${warmed} entries`);
        return warmed;
    }

    /**
     * Get comprehensive cache statistics
     */
    async getStats() {
        const hybridStats = hybridCache.getStats();
        
        return {
            ...hybridStats,
            compressionThreshold: this.compressionThreshold,
            maxCacheSize: this.maxCacheSize,
            cacheDurations: this.cacheDurations,
            memoryStatus: memoryMonitor.getMemoryUsage()
        };
    }

    /**
     * Cache health check
     */
    async healthCheck() {
        try {
            const testKey = 'health_check_' + Date.now();
            const testData = { timestamp: Date.now(), test: true };
            
            // Test set
            const setResult = await this.set('health', 'check', testData);
            if (!setResult) {
                return { healthy: false, error: 'Failed to set test data' };
            }

            // Test get
            const getData = await this.get('health', 'check');
            if (!getData || getData.timestamp !== testData.timestamp) {
                return { healthy: false, error: 'Failed to retrieve test data' };
            }

            // Test delete
            await this.invalidate('health', 'check');

            return { 
                healthy: true, 
                stats: await this.getStats(),
                timestamp: Date.now()
            };
            
        } catch (error) {
            return { 
                healthy: false, 
                error: error.message,
                timestamp: Date.now()
            };
        }
    }
}

module.exports = new OptimizedCacheManager();
