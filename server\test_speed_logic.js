// Test speed logic for BillionConnect products
require('dotenv').config();

const billionconnectService = require('./src/services/billionconnect.service');

async function testSpeedLogic() {
    console.log('🚀 Testing BillionConnect Speed Logic...\n');

    try {
        // Get real commodities to test speed logic
        const commodities = await billionconnectService.getCommodities();
        console.log(`📦 Retrieved ${commodities.length} commodities for speed testing`);

        // Test speed logic with different limitFlowSpeed values
        const testCases = [
            {
                name: 'No Speed Limit',
                product: {
                    ...commodities[0],
                    limitFlowSpeed: null
                }
            },
            {
                name: 'Empty Speed Limit',
                product: {
                    ...commodities[0],
                    limitFlowSpeed: ''
                }
            },
            {
                name: 'Zero Speed Limit',
                product: {
                    ...commodities[0],
                    limitFlowSpeed: '0'
                }
            },
            {
                name: 'Speed Limit 384kbps',
                product: {
                    ...commodities[0],
                    limitFlowSpeed: '384'
                }
            },
            {
                name: 'Speed Limit 1Mbps',
                product: {
                    ...commodities[0],
                    limitFlowSpeed: '1024'
                }
            }
        ];

        console.log('🔍 Testing Speed Logic Cases:\n');

        testCases.forEach((testCase, index) => {
            const transformedProduct = billionconnectService.transformProduct(testCase.product);
            
            console.log(`${index + 1}. ${testCase.name}:`);
            console.log(`   limitFlowSpeed: "${testCase.product.limitFlowSpeed}"`);
            console.log(`   Speed Result: "${transformedProduct.speed}"`);
            console.log(`   Expected: ${testCase.product.limitFlowSpeed && testCase.product.limitFlowSpeed !== '0' && testCase.product.limitFlowSpeed !== '' ? 'Restrictive' : 'Unrestricted'}`);
            
            const isCorrect = (
                (testCase.product.limitFlowSpeed && testCase.product.limitFlowSpeed !== '0' && testCase.product.limitFlowSpeed !== '' && transformedProduct.speed === 'Restrictive') ||
                ((!testCase.product.limitFlowSpeed || testCase.product.limitFlowSpeed === '0' || testCase.product.limitFlowSpeed === '') && transformedProduct.speed === 'Unrestricted')
            );
            
            console.log(`   ✅ ${isCorrect ? 'CORRECT' : 'INCORRECT'}\n`);
        });

        // Test with real products that have limitFlowSpeed
        console.log('🔍 Testing Real Products with Speed Limits:\n');
        
        const productsWithSpeedLimits = commodities.filter(product => 
            product.limitFlowSpeed && product.limitFlowSpeed !== '0' && product.limitFlowSpeed !== ''
        );

        if (productsWithSpeedLimits.length > 0) {
            console.log(`Found ${productsWithSpeedLimits.length} products with speed limits:`);
            
            productsWithSpeedLimits.forEach((product, index) => {
                const transformed = billionconnectService.transformProduct(product);
                console.log(`\n${index + 1}. ${product.name}:`);
                console.log(`   limitFlowSpeed: "${product.limitFlowSpeed}"`);
                console.log(`   Speed: "${transformed.speed}"`);
                console.log(`   Description: ${product.desc}`);
            });
        } else {
            console.log('No products found with speed limits in current dataset');
        }

        // Test capacity-based plan type logic
        console.log('\n🔍 Testing Capacity-Based Plan Type Logic:\n');

        const planTypeTestCases = [
            {
                name: 'Unlimited Plan',
                product: {
                    ...commodities[0],
                    highFlowSize: '-1'
                }
            },
            {
                name: 'Fixed Plan 1GB',
                product: {
                    ...commodities[0],
                    highFlowSize: '1048576' // 1GB in KB
                }
            },
            {
                name: 'Fixed Plan 500MB',
                product: {
                    ...commodities[0],
                    highFlowSize: '512000' // 500MB in KB
                }
            }
        ];

        planTypeTestCases.forEach((testCase, index) => {
            const transformedProduct = billionconnectService.transformProduct(testCase.product);
            
            console.log(`${index + 1}. ${testCase.name}:`);
            console.log(`   highFlowSize: "${testCase.product.highFlowSize}"`);
            console.log(`   Capacity: ${transformedProduct.capacity}`);
            console.log(`   Plan Type: "${transformedProduct.isUnlimited ? 'Unlimited' : 'Fixed'}"`);
            console.log(`   Data Display: ${transformedProduct.planData} ${transformedProduct.planDataUnit}`);
            
            const expectedType = testCase.product.highFlowSize === '-1' ? 'Unlimited' : 'Fixed';
            const actualType = transformedProduct.isUnlimited ? 'Unlimited' : 'Fixed';
            const isCorrect = expectedType === actualType;
            
            console.log(`   ✅ ${isCorrect ? 'CORRECT' : 'INCORRECT'}\n`);
        });

        console.log('🎉 Speed and Plan Type Logic Testing Complete!');

    } catch (error) {
        console.error('❌ Speed logic test failed:', error.message);
    }
}

testSpeedLogic();
