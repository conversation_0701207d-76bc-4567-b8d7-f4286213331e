const express = require('express');
const router = express.Router();
const orderController = require('../controllers/orderController');
const { isAuthenticated, isAdmin } = require('../middleware/auth');

// Public webhook endpoint (no authentication required)
router.post('/webhook/billionconnect', orderController.handleBillionConnectWebhook);

// All other routes require authentication
router.use((req, res, next) => {
    // Skip authentication for webhook
    if (req.path === '/webhook/billionconnect' && req.method === 'POST') {
        return next();
    }
    return isAuthenticated(req, res, next);
});

// Admin routes
router.get('/admin/all', isAdmin, orderController.getAllOrders);
router.get('/admin/export', isAdmin, orderController.exportOrders);
router.get('/admin/user/:userId', isAdmin, orderController.getOrdersByUserId);
router.get('/admin/:id', isAdmin, orderController.getAllOrderById);

// User routes
router.get('/', orderController.getUserOrders);
router.post('/', orderController.createOrder);
router.get('/:id', orderController.getOrderById);
router.get('/:id/usage', orderController.getOrderUsage);
router.get('/:id/top-up-plans', orderController.getTopUpPlans);

module.exports = router;
