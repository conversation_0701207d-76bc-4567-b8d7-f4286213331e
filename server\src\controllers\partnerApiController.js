const { User } = require('../models');
const { generate<PERSON>pi<PERSON><PERSON>, hash<PERSON><PERSON><PERSON><PERSON>, mask<PERSON>pi<PERSON><PERSON> } = require('../utils/apiKeyGenerator');
const sequelize = require('../config/database');

/**
 * Get the partner's API key details
 */
exports.getApiKeyInfo = async (req, res) => {
    try {
        const userId = req.user.id;
        
        const partner = await User.findByPk(userId, {
            attributes: ['id', 'apiKey', 'apiKeyLastReset']
        });
        
        if (!partner) {
            return res.status(404).json({ message: 'Partner not found' });
        }
        
        // Format the response
        const apiKeyInfo = {
            partnerId: `partner_${partner.id.substring(0, 5)}`, // Create a partner_id prefix
            apiKey: partner.apiKey ? maskApiKey(partner.apiKey) : null,
            lastReset: partner.apiKeyLastReset
        };
        
        res.json(apiKeyInfo);
    } catch (error) {
        console.error('Error getting API key info:', error);
        res.status(500).json({ message: 'Error retrieving API key information' });
    }
};

/**
 * Generate or regenerate an API key for the partner
 */
exports.generateApiKey = async (req, res) => {
    const transaction = await sequelize.transaction();
    
    try {
        const userId = req.user.id;
        
        const partner = await User.findByPk(userId, { transaction });
        
        if (!partner) {
            await transaction.rollback();
            return res.status(404).json({ message: 'Partner not found' });
        }
        
        // Generate a new API key
        const apiKey = generateApiKey();
        const apiKeyHash = await hashApiKey(apiKey);
        
        // Update the partner record
        await partner.update({
            apiKey: apiKey, // We store the actual key temporarily for the response
            apiKeyHash,
            apiKeyLastReset: new Date()
        }, { transaction });
        
        // Log the API key regeneration
        console.log(`API key regenerated for partner ${partner.id} at ${new Date().toISOString()}`);
        
        await transaction.commit();
        
        res.json({
            message: 'API key generated successfully',
            apiKey
        });
        
        // After sending the response, remove the plain API key from the database
        // for security (we'll keep only the hash)
        await partner.update({ apiKey: null });
    } catch (error) {
        await transaction.rollback();
        console.error('Error generating API key:', error);
        res.status(500).json({ message: 'Error generating API key' });
    }
};

/**
 * Get full API key (reveal)
 */
exports.revealApiKey = async (req, res) => {
    try {
        const userId = req.user.id;
        
        const partner = await User.findByPk(userId);
        
        if (!partner) {
            return res.status(404).json({ message: 'Partner not found' });
        }
        
        if (!partner.apiKey) {
            return res.status(400).json({ 
                message: 'API key not available. Please regenerate your API key.' 
            });
        }
        
        res.json({
            apiKey: partner.apiKey
        });
    } catch (error) {
        console.error('Error revealing API key:', error);
        res.status(500).json({ message: 'Error revealing API key' });
    }
};

