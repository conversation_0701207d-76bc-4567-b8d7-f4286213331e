/**
 * <PERSON><PERSON>t to check and optionally fix region mappings for existing eSIM plans
 * Run with: node check_region_mappings.js [--fix]
 */

const { EsimPlan, Country, EsimPlanCountries } = require('./src/models');
const { Op } = require('sequelize');
const mapCountriesToRegion = require('./src/utils/regionMapper');

console.log('🔍 Checking Region Mappings for eSIM Plans\n');

async function checkRegionMappings() {
    try {
        const shouldFix = process.argv.includes('--fix');
        
        // Get all Mobimatter plans
        const mobimatterPlans = await EsimPlan.findAll({
            where: {
                '$provider.name$': 'Mobimatter'
            },
            include: [
                {
                    model: require('./src/models').Provider,
                    as: 'provider',
                    attributes: ['name']
                },
                {
                    model: Country,
                    as: 'countries',
                    attributes: ['id', 'name', 'region'],
                    through: { attributes: [] }
                }
            ]
        });

        console.log(`Found ${mobimatterPlans.length} Mobimatter plans to check\n`);

        let checkedCount = 0;
        let incorrectCount = 0;
        let fixedCount = 0;

        for (const plan of mobimatterPlans) {
            checkedCount++;
            
            // Get country codes for this plan
            const countryCodes = plan.countries.map(c => c.id);
            
            if (countryCodes.length === 0) {
                console.log(`⚠️  Plan ${plan.id} (${plan.name}) has no countries assigned`);
                continue;
            }

            // Calculate what the region should be based on countries
            const correctRegion = await mapCountriesToRegion(countryCodes);
            const currentRegion = plan.region || '';

            // Compare current region with correct region
            if (currentRegion !== correctRegion) {
                incorrectCount++;
                console.log(`❌ Plan ${plan.id}: "${plan.name}"`);
                console.log(`   Countries: ${countryCodes.join(', ')}`);
                console.log(`   Current Region: "${currentRegion}"`);
                console.log(`   Correct Region: "${correctRegion}"`);
                
                if (shouldFix) {
                    try {
                        await plan.update({ region: correctRegion });
                        fixedCount++;
                        console.log(`   ✅ Fixed!`);
                    } catch (error) {
                        console.log(`   ❌ Failed to fix: ${error.message}`);
                    }
                }
                console.log('');
            } else {
                // Only show correct mappings for first few plans to avoid spam
                if (checkedCount <= 5) {
                    console.log(`✅ Plan ${plan.id}: "${plan.name}" - Region: "${currentRegion}" (Correct)`);
                }
            }
        }

        console.log('\n📊 Summary:');
        console.log(`Total plans checked: ${checkedCount}`);
        console.log(`Plans with incorrect regions: ${incorrectCount}`);
        
        if (shouldFix) {
            console.log(`Plans fixed: ${fixedCount}`);
            console.log(`Plans failed to fix: ${incorrectCount - fixedCount}`);
        } else {
            console.log('\n💡 To fix the incorrect mappings, run: node check_region_mappings.js --fix');
        }

        // Show some examples of region mapping logic
        console.log('\n🧪 Region Mapping Examples:');
        const examples = [
            ['IN', 'TH', 'MY'], // Asia
            ['FR', 'DE', 'IT'], // Europe  
            ['US', 'CA'], // North America
            ['IN', 'FR', 'US'], // Multiple regions
            ['US', 'GB', 'IN', 'AU', 'BR', 'ZA'] // Global
        ];

        for (const countries of examples) {
            const region = await mapCountriesToRegion(countries);
            console.log(`${countries.join(', ')} → "${region}"`);
        }

    } catch (error) {
        console.error('❌ Error checking region mappings:', error);
    } finally {
        process.exit(0);
    }
}

// Run the check
checkRegionMappings();
