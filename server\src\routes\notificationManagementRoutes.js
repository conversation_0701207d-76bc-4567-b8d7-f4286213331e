const express = require('express');
const router = express.Router();
const notificationController = require('../controllers/notificationController');
const { isAuthenticated, isAdmin } = require('../middleware/auth');

// All routes require admin authentication
router.use(isAuthenticated);
router.use(isAdmin);

/**
 * @route GET /api/admin/notifications/stats
 * @desc Get notification processing statistics
 * @access Admin
 */
router.get('/stats', notificationController.getNotificationStats);

/**
 * @route GET /api/admin/notifications
 * @desc Get notifications with filtering and pagination
 * @access Admin
 */
router.get('/', notificationController.getNotifications);

/**
 * @route GET /api/admin/notifications/:id
 * @desc Get a specific notification by ID
 * @access Admin
 */
router.get('/:id', notificationController.getNotificationById);

/**
 * @route POST /api/admin/notifications/:id/retry
 * @desc Manually retry a failed notification
 * @access Admin
 */
router.post('/:id/retry', notificationController.retryNotification);

/**
 * @route POST /api/admin/notifications/:id/ignore
 * @desc Mark a notification as ignored
 * @access Admin
 */
router.post('/:id/ignore', notificationController.ignoreNotification);

/**
 * @route POST /api/admin/notifications/cleanup
 * @desc Clean up old notifications
 * @access Admin
 */
router.post('/cleanup', notificationController.cleanupNotifications);

/**
 * @route POST /api/admin/notifications/process-pending
 * @desc Force process all pending notifications
 * @access Admin
 */
router.post('/process-pending', notificationController.processPendingNotifications);

/**
 * @route POST /api/admin/notifications/retry-batch
 * @desc Manually trigger retry of all eligible failed notifications
 * @access Admin
 */
router.post('/retry-batch', notificationController.triggerRetryBatch);

module.exports = router;
