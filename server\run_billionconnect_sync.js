// Run BillionConnect sync to import all plans
require('dotenv').config();

const { EsimPlan, Provider, Country } = require('./src/models');
const billionconnectService = require('./src/services/billionconnect.service');
const providerFactory = require('./src/services/provider.factory');

// Helper function to check if a provider's plans are currently hidden
const isProviderPlansHidden = async (providerId) => {
    if (!providerId) return false;

    try {
        // Check if there are any visible plans for this provider
        const visiblePlanCount = await EsimPlan.count({
            where: {
                providerId: providerId,
                status: 'visible',
                isActive: true
            }
        });

        // Check if there are any plans at all for this provider
        const totalPlanCount = await EsimPlan.count({
            where: {
                providerId: providerId,
                isActive: true
            }
        });

        // If there are plans but none are visible, the provider is hidden
        return totalPlanCount > 0 && visiblePlanCount === 0;
    } catch (error) {
        console.error('Error checking provider plan visibility:', error);
        return false; // Default to visible if there's an error
    }
};

async function runBillionConnectSync() {
    console.log('🚀 Running BillionConnect Sync to Import All Plans...\n');

    try {
        // Step 1: Get provider
        const provider = await Provider.findOne({ where: { name: 'billionconnect' } });
        if (!provider) {
            console.log('❌ BillionConnect provider not found');
            return;
        }
        console.log('✅ Provider found:', provider.name);

        // Step 2: Get products with prices
        console.log('\n📦 Getting products with prices...');
        const externalPlans = await billionconnectService.getProductsWithPrices();
        console.log(`✅ Retrieved ${externalPlans.length} products with prices`);

        if (externalPlans.length === 0) {
            console.log('❌ No products retrieved');
            return;
        }

        // Step 3: Get all countries for mapping
        const allCountries = await Country.findAll({
            attributes: ['id', 'name', 'iso3']
        });
        const countryMap = new Map();
        allCountries.forEach(country => {
            countryMap.set(country.name.toLowerCase(), country);
            countryMap.set(country.iso3.toLowerCase(), country);
            countryMap.set(country.id.toLowerCase(), country);
        });

        // Step 4: Process each plan
        let successCount = 0;
        let errorCount = 0;

        for (let i = 0; i < externalPlans.length; i++) {
            const product = externalPlans[i];
            console.log(`\n📋 Processing Plan ${i + 1}/${externalPlans.length}: ${product.name}`);
            console.log(`   🔑 SKU: ${product.externalSkuId}`);

            // Special logging for the specific plan from the user's sample
            if (product.externalSkuId === '1752720904694408') {
                console.log(`   🎯 FOUND TARGET PLAN: ${product.name}`);
                console.log(`   📊 Raw countries data:`, product.providerMetadata?.countries);
                console.log(`   🌍 Supported countries:`, product.supportedCountries);
            }

            try {
                // Standardize the product
                const standardizedPlan = await providerFactory.standardizeProduct(provider.name, product);
                // Category is now determined by the provider factory based on product type

                // Check if plan already exists
                let plan = await EsimPlan.findOne({
                    where: { externalSkuId: standardizedPlan.externalSkuId }
                });

                if (plan) {
                    console.log('   ✅ Plan exists, updating...');
                    // Preserve existing plan name and only update other fields
                    const existingName = plan.name;
                    await plan.update({
                        ...standardizedPlan,
                        name: existingName, // Preserve existing name
                        providerId: provider.id,
                        isActive: true
                    });
                    console.log(`   📝 Preserved existing name: "${existingName}"`);
                } else {
                    console.log('   ✅ Creating new plan...');

                    // Check if provider's plans are currently hidden
                    const providerPlansHidden = await isProviderPlansHidden(provider.id);
                    console.log(`   👁️ Provider plans hidden: ${providerPlansHidden}`);

                    plan = await EsimPlan.create({
                        ...standardizedPlan,
                        providerId: provider.id,
                        productId: `BC${Date.now()}_${i}`,
                        // Set status based on provider's current visibility
                        status: providerPlansHidden ? 'hidden' : 'visible',
                        isActive: true
                    });
                }

                // Handle country associations
                console.log(`   🌍 Processing countries for plan: ${plan.name}`);
                console.log(`   📍 Supported countries from provider: [${standardizedPlan.supportedCountries?.join(', ') || 'none'}]`);

                await plan.setCountries([]);

                const supportedCountries = standardizedPlan.supportedCountries || [];
                if (supportedCountries.length > 0) {
                    const countryAssociations = [];
                    const foundCountries = [];
                    const missingCountries = [];

                    supportedCountries.forEach(countryCode => {
                        const country = countryMap.get(countryCode.toLowerCase());
                        if (country) {
                            countryAssociations.push({
                                countryId: country.id,
                                isDefault: countryAssociations.length === 0
                            });
                            foundCountries.push(`${country.id} (${country.name})`);
                        } else {
                            missingCountries.push(countryCode);
                        }
                    });

                    if (countryAssociations.length > 0) {
                        await plan.addCountries(
                            countryAssociations.map(assoc => assoc.countryId),
                            {
                                through: countryAssociations.map(assoc => ({
                                    isDefault: assoc.isDefault
                                }))
                            }
                        );
                        console.log(`   ✅ Associated with ${countryAssociations.length} countries: ${foundCountries.join(', ')}`);
                    }

                    if (missingCountries.length > 0) {
                        console.log(`   ⚠️  Countries not found in database: ${missingCountries.join(', ')}`);
                    }
                } else {
                    console.log(`   ⚠️  No supported countries found for this plan`);
                }

                console.log(`   ✅ Plan processed successfully:`);
                console.log(`      Name: ${plan.name}`);
                console.log(`      Buying Price: $${plan.buyingPrice}`);
                console.log(`      Plan Type: ${plan.planType}`);
                console.log(`      Speed: ${plan.speed}`);
                console.log(`      Countries: ${supportedCountries.join(', ')}`);

                successCount++;

            } catch (error) {
                console.error(`   ❌ Error processing plan: ${error.message}`);
                errorCount++;
            }
        }

        // Step 5: Deactivate plans that are no longer in the filtered results
        console.log('\n🔄 Deactivating plans not in filtered results...');
        const currentSkuIds = externalPlans.map(plan => plan.externalSkuId);

        const plansToDeactivate = await EsimPlan.findAll({
            where: {
                providerId: provider.id,
                isActive: true,
                externalSkuId: {
                    [require('sequelize').Op.notIn]: currentSkuIds
                }
            }
        });

        if (plansToDeactivate.length > 0) {
            await EsimPlan.update(
                { isActive: false },
                {
                    where: {
                        providerId: provider.id,
                        isActive: true,
                        externalSkuId: {
                            [require('sequelize').Op.notIn]: currentSkuIds
                        }
                    }
                }
            );
            console.log(`   ✅ Deactivated ${plansToDeactivate.length} plans that are no longer in filtered results`);
            plansToDeactivate.forEach(plan => {
                console.log(`      - ${plan.name} (SKU: ${plan.externalSkuId})`);
            });
        } else {
            console.log('   ✅ No plans need to be deactivated');
        }

        // Step 6: Summary
        console.log('\n🎉 BillionConnect Sync Completed!');
        console.log(`\n📊 Summary:`);
        console.log(`   ✅ Successfully processed: ${successCount} plans`);
        console.log(`   ❌ Errors: ${errorCount} plans`);
        console.log(`   📦 Total plans: ${externalPlans.length}`);

        // Step 7: Verify final results
        console.log('\n🔍 Final Verification:');
        const finalPlans = await EsimPlan.findAll({
            where: { 
                providerId: provider.id,
                isActive: true
            },
            include: [
                {
                    model: Country,
                    as: 'countries',
                    through: { attributes: [] }
                }
            ]
        });

        console.log(`✅ Active BillionConnect plans in database: ${finalPlans.length}`);
        
        finalPlans.forEach((plan, index) => {
            console.log(`\n${index + 1}. ${plan.name}:`);
            console.log(`   SKU: ${plan.externalSkuId}`);
            console.log(`   Buying Price: $${plan.buyingPrice}`);
            console.log(`   Plan Type: ${plan.planType}`);
            console.log(`   Speed: ${plan.speed}`);
            console.log(`   Countries: ${plan.countries.map(c => c.name).join(', ')}`);
            console.log(`   Data: ${plan.planData} ${plan.planDataUnit}`);
            console.log(`   Validity: ${plan.validityDays} days`);
        });

        console.log('\n🚀 BillionConnect integration is now complete and ready for use!');

    } catch (error) {
        console.error('❌ Sync failed:', error);
        console.error('Stack trace:', error.stack);
    }
}

runBillionConnectSync();
