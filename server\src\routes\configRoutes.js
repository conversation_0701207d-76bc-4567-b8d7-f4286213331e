const express = require('express');
const router = express.Router();

/**
 * @route GET /api/config/env.js
 * @desc Get environment configuration as JavaScript
 * @access Public
 */
router.get('/env.js', (req, res) => {
    // Determine the environment
    const nodeEnv = process.env.NODE_ENV || 'development';
    
    // Set content type to JavaScript
    res.setHeader('Content-Type', 'application/javascript');
    // Add CSP header to allow this script to execute
    res.setHeader('Content-Security-Policy', "script-src 'self' 'unsafe-inline'");
    
    // Define the base URL based on environment variables
    const apiBaseUrl = process.env.API_BASE_URL || `${req.protocol}://${req.get('host')}/api/v1`;
    
    // Define available environments
    const environments = [
        // { 
        //     name: 'Development', 
        //     url: process.env.DEV_API_URL || `${req.protocol}://${req.get('host')}/api/v1` 
        // },
        { 
            name: 'Production', 
            url: process.env.PROD_API_URL || 'https://api.vizlync.net/api/v1' 
        }
    ];
    
    // If there's a staging environment configured, add it
    if (process.env.STAGING_API_URL) {
        environments.push({
            name: 'Staging',
            url: process.env.STAGING_API_URL
        });
    }
    
    // Generate JavaScript code that sets global variables
    const jsContent = `
        // API configuration variables
        window.API_BASE_URL = "${apiBaseUrl}";
        window.API_ENVIRONMENTS = ${JSON.stringify(environments)};
        window.API_CURRENT_ENV = "${nodeEnv}";

        // console.log("Loaded API environment configuration:", {
        //     baseUrl: window.API_BASE_URL,
        //     environments: window.API_ENVIRONMENTS,
        //     currentEnv: window.API_CURRENT_ENV
        // });
    `;
    
    res.send(jsContent);
});

/**
 * @route GET /api/config/scripts/highlight.min.js
 * @desc Serve a local copy of the highlight.js library
 * @access Public
 */
router.get('/scripts/highlight.min.js', (req, res) => {
    res.setHeader('Content-Type', 'application/javascript');
    // This will proxy the request to cdnjs
    fetch('https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js')
        .then(response => response.text())
        .then(js => {
            res.send(js);
        })
        .catch(error => {
            console.error('Error fetching highlight.js:', error);
            res.status(500).send('// Error loading highlight.js');
        });
});

/**
 * @route GET /api/config/scripts/json.min.js
 * @desc Serve a local copy of the highlight.js JSON language
 * @access Public
 */
router.get('/scripts/json.min.js', (req, res) => {
    res.setHeader('Content-Type', 'application/javascript');
    // This will proxy the request to cdnjs
    fetch('https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/json.min.js')
        .then(response => response.text())
        .then(js => {
            res.send(js);
        })
        .catch(error => {
            console.error('Error fetching json.min.js:', error);
            res.status(500).send('// Error loading json.min.js');
        });
});

/**
 * @route GET /api/config/scripts/bash.min.js
 * @desc Serve a local copy of the highlight.js Bash language
 * @access Public
 */
router.get('/scripts/bash.min.js', (req, res) => {
    res.setHeader('Content-Type', 'application/javascript');
    // This will proxy the request to cdnjs
    fetch('https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/bash.min.js')
        .then(response => response.text())
        .then(js => {
            res.send(js);
        })
        .catch(error => {
            console.error('Error fetching bash.min.js:', error);
            res.status(500).send('// Error loading bash.min.js');
        });
});

/**
 * @route GET /api/config/scripts/bootstrap.min.js
 * @desc Serve a local copy of Bootstrap
 * @access Public
 */
router.get('/scripts/bootstrap.min.js', (req, res) => {
    res.setHeader('Content-Type', 'application/javascript');
    // This will proxy the request to jsdelivr
    fetch('https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js')
        .then(response => response.text())
        .then(js => {
            res.send(js);
        })
        .catch(error => {
            console.error('Error fetching bootstrap.min.js:', error);
            res.status(500).send('// Error loading bootstrap.min.js');
        });
});

/**
 * @route GET /api/config/styles/bootstrap.min.css
 * @desc Serve a local copy of Bootstrap CSS
 * @access Public
 */
router.get('/styles/bootstrap.min.css', (req, res) => {
    res.setHeader('Content-Type', 'text/css');
    // This will proxy the request to jsdelivr
    fetch('https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css')
        .then(response => response.text())
        .then(css => {
            res.send(css);
        })
        .catch(error => {
            console.error('Error fetching bootstrap.min.css:', error);
            res.status(500).send('/* Error loading bootstrap.min.css */');
        });
});

/**
 * @route GET /api/config/styles/highlight.min.css
 * @desc Serve a local copy of the highlight.js CSS
 * @access Public
 */
router.get('/styles/highlight.min.css', (req, res) => {
    res.setHeader('Content-Type', 'text/css');
    // This will proxy the request to cdnjs
    fetch('https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/default.min.css')
        .then(response => response.text())
        .then(css => {
            res.send(css);
        })
        .catch(error => {
            console.error('Error fetching highlight.min.css:', error);
            res.status(500).send('/* Error loading highlight.min.css */');
        });
});

/**
 * @route GET /api/config/scripts/doc-main.js
 * @desc Serve the main JavaScript for the documentation page
 * @access Public
 */
router.get('/scripts/doc-main.js', (req, res) => {
    res.setHeader('Content-Type', 'application/javascript');
    // Generate the JavaScript content
    const jsContent = `
        // Function to update request body based on form inputs
        function updateRequestBody(trySection) {
            if (!trySection) return;

            const bodyTextarea = trySection.querySelector('textarea[id$="-body"]');
            if (!bodyTextarea) return;

            // Get all inputs with data-param-type="body" within this try section
            const bodyInputs = trySection.querySelectorAll('[data-param-type="body"]');

            try {
                // Parse current JSON
                let bodyData = JSON.parse(bodyTextarea.value);

                // Update values from form inputs
                bodyInputs.forEach(input => {
                    const key = input.dataset.bodyKey;
                    if (key) {
                        let value = input.value.trim();

                        // Handle different input types
                        if (input.type === 'date') {
                            value = value || null;
                        } else if (value === '') {
                            value = input.type === 'date' ? null : '';
                        }

                        bodyData[key] = value;
                    }
                });

                // Update textarea with formatted JSON
                bodyTextarea.value = JSON.stringify(bodyData, null, 2);
            } catch (error) {
                console.error('Error updating request body:', error);
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize syntax highlighting
            hljs.highlightAll();

            // Get the copy buttons
            const copyButtons = document.querySelectorAll('.copy-btn');

            // Set up environment dropdown
            const baseUrlSelect = document.getElementById('base-url');

            // Check if API_ENVIRONMENTS exists from our environment script
            if (window.API_ENVIRONMENTS && baseUrlSelect) {
                // Clear any existing options
                baseUrlSelect.innerHTML = '';

                // Populate the dropdown with environment options
                window.API_ENVIRONMENTS.forEach(env => {
                    const option = document.createElement('option');
                    option.value = env.url;
                    option.textContent = \` \${env.url}\`;
                    baseUrlSelect.appendChild(option);
                });

                // Set default value to current environment
                if (window.API_BASE_URL) {
                    // Try to find the matching environment in the dropdown
                    for (let i = 0; i < baseUrlSelect.options.length; i++) {
                        if (baseUrlSelect.options[i].value === window.API_BASE_URL) {
                            baseUrlSelect.selectedIndex = i;
                            break;
                        }
                    }
                } else if (baseUrlSelect.options.length > 0) {
                    // Default to first option if no specific base URL is set
                    baseUrlSelect.selectedIndex = 0;
                }
            }

            // Set up automatic form population for body parameter inputs
            document.querySelectorAll('[data-param-type="body"]').forEach(input => {
                input.addEventListener('input', function() {
                    // Find the closest try-it-section to determine which body to update
                    const trySection = this.closest('.try-it-section');
                    if (trySection) {
                        updateRequestBody(trySection);
                    }
                });

                input.addEventListener('change', function() {
                    // Find the closest try-it-section to determine which body to update
                    const trySection = this.closest('.try-it-section');
                    if (trySection) {
                        updateRequestBody(trySection);
                    }
                });
            });
            
            // Function to create and setup copy button
            function setupCopyButton(codeElement) {
                // Check if button already exists
                let container = codeElement.closest('.position-relative');
                if (!container) {
                    // Create a relative container if it doesn't exist
                    container = document.createElement('div');
                    container.className = 'position-relative';
                    codeElement.parentNode.insertBefore(container, codeElement);
                    container.appendChild(codeElement);
                }
                
                // Check if button already exists
                let copyButton = container.querySelector('.copy-btn');
                if (!copyButton) {
                    copyButton = document.createElement('button');
                    copyButton.className = 'btn btn-sm btn-outline-secondary copy-btn';
                    copyButton.textContent = 'Copy';
                    copyButton.setAttribute('data-code', codeElement.id);
                    container.insertBefore(copyButton, container.firstChild);
                    
                    // Add click event
                    copyButton.addEventListener('click', () => {
                        navigator.clipboard.writeText(codeElement.textContent)
                            .then(() => {
                                const originalText = copyButton.textContent;
                                copyButton.textContent = 'Copied!';
                                setTimeout(() => {
                                    copyButton.textContent = originalText;
                                }, 2000);
                            })
                            .catch(err => {
                                // console.error('Could not copy text: ', err);
                            });
                    });
                }
                
                return copyButton;
            }
            
            // Set up copy buttons
            copyButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const codeId = button.getAttribute('data-code');
                    const codeElement = document.getElementById(codeId);
                    
                    if (codeElement) {
                        navigator.clipboard.writeText(codeElement.textContent)
                            .then(() => {
                                // Change button text temporarily
                                const originalText = button.textContent;
                                button.textContent = 'Copied!';
                        setTimeout(() => {
                                    button.textContent = originalText;
                                }, 2000);
                            })
                            .catch(err => {
                                // console.error('Could not copy text: ', err);
                            });
                    }
                });
            });
            
            // Update curl examples when base URL changes
            baseUrlSelect.addEventListener('change', function() {
                updateCurlExamples();
            });
            
            // Update auth headers when API key or partner ID change
            const apiKeyInput = document.getElementById('auth-api-key');
            const partnerIdInput = document.getElementById('auth-partner-id');
            
            apiKeyInput.addEventListener('input', updateCurlExamples);
            partnerIdInput.addEventListener('input', updateCurlExamples);
            
            // Initial update of examples
            updateCurlExamples();
            
            function updateCurlExamples() {
                const baseUrl = baseUrlSelect.value;
                const apiKey = apiKeyInput.value || 'your-api-key';
                const partnerId = partnerIdInput.value || 'your-partner-id';
                
                // Update each curl example
                const examples = [
                    {
                        id: 'getProducts-curl',
                        url: \`\${baseUrl}/products\`,
                        method: 'GET'
                    },
                    {
                        id: 'getOrder-curl',
                        url: \`\${baseUrl}/order/VLZ123456\`,
                        method: 'GET'
                    },
                    {
                        id: 'createOrder-curl',
                        url: \`\${baseUrl}/order\`,
                        method: 'POST',
                        body: \`{
    "productId": "ABCDEF123456",
    "startDate": "2023-11-01"
  }\`
                    },
                    {
                        id: 'getUsage-curl',
                        url: \`\${baseUrl}/usage/VLZ123456\`,
                        method: 'GET'
                    },
                    {
                        id: 'getTopupPlans-curl',
                        url: \`\${baseUrl}/order/VLZ123456/topup-plans\`,
                        method: 'GET'
                    },
                    {
                        id: 'createTopupOrder-curl',
                        url: \`\${baseUrl}/order/VLZ123456/topup\`,
                        method: 'POST',
                        body: \`{
    "productId": "ADDON001"
  }\`
                    }
                ];
                
                examples.forEach(example => {
                    const element = document.getElementById(example.id);
                    
                    if (element) {
                        let curlCommand = \`curl -X \${example.method} "\${example.url}" \\
  -H "Authorization: Bearer \${apiKey}" \\
  -H "X-Partner-ID: \${partnerId}"\`;
                        
                        if (example.body) {
                            curlCommand += \` \\
  -H "Content-Type: application/json" \\
  -d '\${example.body}'\`;
                        }
                        
                        element.textContent = curlCommand;
                        hljs.highlightElement(element);
                    }
                });
            }
            
            // Set up "Try it" functionality
            const tryButtons = document.querySelectorAll('.try-api');
            
            tryButtons.forEach(button => {
                button.addEventListener('click', async () => {
                    const endpoint = button.getAttribute('data-endpoint');
                    const method = button.getAttribute('data-method');
                    
                    if (!endpoint || !method) {
                        // console.error('Endpoint or method missing from button data attributes');
                        return;
                    }
                    
                    const selectedApiKey = apiKeyInput.value;
                    const selectedPartnerId = partnerIdInput.value;
                    
                    if (!selectedApiKey || !selectedPartnerId) {
                        alert('Please enter your API Key and Partner ID in the Authentication section above.');
                            return;
                    }

                    button.disabled = true;
                    button.textContent = 'Loading...';
                    
                    try {
                        const selectedBaseUrl = baseUrlSelect.value;
                        
                        // Build the request
                        const options = {
                            method,
                            headers: {
                                'Authorization': \`Bearer \${selectedApiKey}\`,
                                'X-Partner-ID': selectedPartnerId
                            }
                        };
                        
                        // Add body for POST requests
                        if (method === 'POST') {
                            const bodyId = button.getAttribute('data-body-id');
                            if (bodyId) {
                                const bodyElement = document.getElementById(bodyId);
                                if (bodyElement) {
                                    options.headers['Content-Type'] = 'application/json';
                                    options.body = bodyElement.value;
                                }
                            }
                        }
                        
                        // Construct API URL
                        let apiUrl = selectedBaseUrl + endpoint;
                        
                        // Replace path parameters
                        const pathParams = button.closest('.try-it-section').querySelectorAll('input[data-param-type="path"]');
                        pathParams.forEach(input => {
                            const paramName = input.getAttribute('data-param-name');
                            if (!paramName) return;
                            
                            apiUrl = apiUrl.replace(\`{\${paramName}}\`, encodeURIComponent(input.value));
                        });
                        
                        // Add query params
                        const queryParams = new URLSearchParams();
                        const queryParamInputs = button.closest('.try-it-section').querySelectorAll('input[data-param-type="query"]');
                        queryParamInputs.forEach(input => {
                            if (input.value) {
                                queryParams.append(input.getAttribute('data-param-name'), input.value);
                            }
                        });
                        
                        if (queryParams.toString()) {
                            apiUrl += \`?\${queryParams.toString()}\`;
                        }
                        
                        // console.log(\`Making \${method} request to: \${apiUrl}\`);
                        
                        // Make the actual API request
                        const response = await fetch(apiUrl, options);
                        let responseData;
                        
                        // Try to parse as JSON first
                        const contentType = response.headers.get('content-type');
                        if (contentType && contentType.includes('application/json')) {
                            const jsonData = await response.json();
                            responseData = JSON.stringify(jsonData, null, 2);
                        } else {
                            // Fallback to text
                            responseData = await response.text();
                        }
                        
                        // Display the response
                        const endpointId = button.closest('.endpoint').id;
                        
                        // Map endpoint IDs to response element IDs
                        const responseIdMap = {
                            'get-products': 'getProducts-try-response',
                            'get-order': 'getOrder-try-response',
                            'create-order': 'createOrder-try-response',
                            'get-usage': 'getUsage-try-response',
                            'get-topup-plans': 'getTopupPlans-try-response',
                            'create-topup-order': 'createTopupOrder-try-response'
                        };
                        
                        const responseElementId = responseIdMap[endpointId] || \`\${endpointId}-try-response\`;
                        const responseElement = document.getElementById(responseElementId);
                        
                        if (responseElement) {
                            // Add status display section
                            const responseContainer = responseElement.closest('.response-container');
                            let statusElement = responseContainer.querySelector('.response-status');
                            
                            if (!statusElement) {
                                statusElement = document.createElement('div');
                                statusElement.className = 'response-status mb-2';
                                responseContainer.insertBefore(statusElement, responseContainer.firstChild);
                            }
                            
                            // Determine if response was successful
                            const isSuccess = response.status >= 200 && response.status < 300;
                            const statusClass = isSuccess ? 'text-success' : 'text-danger';
                            
                            statusElement.innerHTML = \`
                                <strong class="\${statusClass}">Status: \${response.status} \${response.statusText}</strong>
                            \`;
                            
                            responseElement.textContent = responseData;
                            hljs.highlightElement(responseElement);
                            
                            // Add copy button to the response
                            setupCopyButton(responseElement);
                        } else {
                            // console.error(\`Response element with ID '\${responseElementId}' not found. Endpoint ID: '\${endpointId}'\`);
                        }
                    } catch (error) {
                        // console.error('API request failed:', error);
                        const endpointId = button.closest('.endpoint').id;
                        
                        // Map endpoint IDs to response element IDs
                        const responseIdMap = {
                            'get-products': 'getProducts-try-response',
                            'get-order': 'getOrder-try-response',
                            'create-order': 'createOrder-try-response',
                            'get-usage': 'getUsage-try-response',
                            'get-topup-plans': 'getTopupPlans-try-response',
                            'create-topup-order': 'createTopupOrder-try-response'
                        };
                        
                        const responseElementId = responseIdMap[endpointId] || \`\${endpointId}-try-response\`;
                        const responseElement = document.getElementById(responseElementId);
                        
                        if (responseElement) {
                            // Try to format the error message nicely
                            let errorMessage = error.message || 'Failed to make request';
                            let errorOutput;
                            
                            try {
                                // If we have a response object from the failed fetch
                                if (error.response) {
                                    errorOutput = JSON.stringify({
                                        success: false,
                                        status: error.response.status,
                                        statusText: error.response.statusText,
                                        error: {
                                            code: 'API_ERROR',
                                            message: errorMessage
                                        }
                                    }, null, 2);
                                } else {
                                    errorOutput = JSON.stringify({
                                        success: false,
                                        error: {
                                            code: 'REQUEST_FAILED',
                                            message: errorMessage
                                        }
                                    }, null, 2);
                                }
                            } catch (e) {
                                // Fallback if JSON.stringify fails
                                errorOutput = \`Error: \${errorMessage}\`;
                            }
                            
                            responseElement.textContent = errorOutput;
                            hljs.highlightElement(responseElement);
                            
                            // Add copy button to the error response
                            setupCopyButton(responseElement);
                        } else {
                            // console.error(\`Error response element with ID '\${responseElementId}' not found. Endpoint ID: '\${endpointId}'\`);
                        }
                    } finally {
                        button.disabled = false;
                        button.textContent = 'Send Request';
                    }
                });
            });

            // Handle form inputs that update the request body - Create Order
            const productIdInput = document.getElementById('create-order-product-id');
            const startDateInput = document.getElementById('create-order-start-date');
            const bodyTextarea = document.getElementById('create-order-body');

            function updateCreateOrderRequestBody() {
                if (!bodyTextarea) return;
                try {
                    let bodyObj = JSON.parse(bodyTextarea.value);

                    if (productIdInput && productIdInput.value) {
                        bodyObj.productId = productIdInput.value;
                    }

                    if (startDateInput && startDateInput.value) {
                        bodyObj.startDate = startDateInput.value;
                    } else {
                        bodyObj.startDate = null;
                    }

                    bodyTextarea.value = JSON.stringify(bodyObj, null, 2);
                } catch (e) {
                    // console.error('Error updating create order request body:', e);
                }
            }

            if (productIdInput) productIdInput.addEventListener('input', updateCreateOrderRequestBody);
            if (startDateInput) startDateInput.addEventListener('input', updateCreateOrderRequestBody);

            // Initialize body with any default values
            if (productIdInput && startDateInput && bodyTextarea) {
                updateCreateOrderRequestBody();
            }

            // Handle form inputs that update the request body - Topup Order
            const topupProductIdInput = document.getElementById('create-topup-product-id');
            const topupBodyTextarea = document.getElementById('create-topup-body');

            function updateTopupOrderRequestBody() {
                if (!topupBodyTextarea) return;
                try {
                    let bodyObj = JSON.parse(topupBodyTextarea.value);

                    if (topupProductIdInput && topupProductIdInput.value) {
                        bodyObj.productId = topupProductIdInput.value;
                    } else {
                        bodyObj.productId = "";
                    }

                    topupBodyTextarea.value = JSON.stringify(bodyObj, null, 2);
                } catch (e) {
                    // console.error('Error updating topup order request body:', e);
                }
            }

            if (topupProductIdInput) topupProductIdInput.addEventListener('input', updateTopupOrderRequestBody);

            // Initialize topup body with any default values
            if (topupProductIdInput && topupBodyTextarea) {
                updateTopupOrderRequestBody();
            }
        });
    `;
    
    res.send(jsContent);
});

module.exports = router; 