#!/usr/bin/env node

/**
 * Memory Optimization Test Script
 * Tests the memory monitoring and leak detection functionality
 */

const memoryMonitor = require('../src/utils/memoryMonitor');
const memoryLeakDetector = require('../src/services/memoryLeakDetector.service');

class MemoryOptimizationTest {
    constructor() {
        this.testResults = [];
    }

    /**
     * Log test result
     */
    logResult(testName, passed, message) {
        const result = {
            test: testName,
            passed,
            message,
            timestamp: new Date().toISOString()
        };
        
        this.testResults.push(result);
        
        const status = passed ? '✅ PASS' : '❌ FAIL';
        console.log(`${status} ${testName}: ${message}`);
    }

    /**
     * Test memory monitor initialization
     */
    testMemoryMonitorInit() {
        try {
            const usage = memoryMonitor.getMemoryUsage();
            const hasRequiredFields = usage.heapUsed && usage.heapTotal && usage.heapUsagePercent;
            
            this.logResult(
                'Memory Monitor Initialization',
                hasRequiredFields,
                hasRequiredFields ? 
                    `Memory usage: ${usage.heapUsagePercent.toFixed(1)}% (${usage.heapUsedMB}MB)` :
                    'Missing required memory usage fields'
            );
        } catch (error) {
            this.logResult('Memory Monitor Initialization', false, error.message);
        }
    }

    /**
     * Test memory thresholds
     */
    testMemoryThresholds() {
        try {
            const isWarning = memoryMonitor.isMemoryWarning();
            const isCritical = memoryMonitor.isMemoryCritical();
            const isSafe = memoryMonitor.isSafeToProcess();
            
            this.logResult(
                'Memory Thresholds',
                true,
                `Warning: ${isWarning}, Critical: ${isCritical}, Safe: ${isSafe}`
            );
        } catch (error) {
            this.logResult('Memory Thresholds', false, error.message);
        }
    }

    /**
     * Test garbage collection
     */
    testGarbageCollection() {
        try {
            if (!global.gc) {
                this.logResult(
                    'Garbage Collection',
                    false,
                    'Garbage collection not available. Start with --expose-gc flag'
                );
                return;
            }

            const beforeUsage = memoryMonitor.getMemoryUsage();
            const freed = memoryMonitor.forceGarbageCollection();
            const afterUsage = memoryMonitor.getMemoryUsage();
            
            this.logResult(
                'Garbage Collection',
                true,
                `Freed ${freed}MB memory (${beforeUsage.heapUsedMB}MB → ${afterUsage.heapUsedMB}MB)`
            );
        } catch (error) {
            this.logResult('Garbage Collection', false, error.message);
        }
    }

    /**
     * Test processing strategy
     */
    testProcessingStrategy() {
        try {
            const strategy = memoryMonitor.getProcessingStrategy();
            const hasRequiredFields = strategy.strategy && strategy.batchSize && strategy.gcFrequency;
            
            this.logResult(
                'Processing Strategy',
                hasRequiredFields,
                hasRequiredFields ?
                    `Strategy: ${strategy.strategy}, Batch: ${strategy.batchSize}, GC: every ${strategy.gcFrequency} items` :
                    'Missing required strategy fields'
            );
        } catch (error) {
            this.logResult('Processing Strategy', false, error.message);
        }
    }

    /**
     * Test memory leak detection
     */
    testMemoryLeakDetection() {
        try {
            const leakCheck = memoryMonitor.checkForMemoryLeaks();
            const hasRequiredFields = leakCheck.hasOwnProperty('detected') && leakCheck.hasOwnProperty('score');
            
            this.logResult(
                'Memory Leak Detection',
                hasRequiredFields,
                hasRequiredFields ?
                    `Leak detected: ${leakCheck.detected}, Score: ${leakCheck.score}/4` :
                    'Missing required leak detection fields'
            );
        } catch (error) {
            this.logResult('Memory Leak Detection', false, error.message);
        }
    }

    /**
     * Test memory leak detector service
     */
    testMemoryLeakDetectorService() {
        try {
            const stats = memoryLeakDetector.getStats();
            const hasRequiredFields = stats.hasOwnProperty('isMonitoring') && stats.hasOwnProperty('historySize');
            
            this.logResult(
                'Memory Leak Detector Service',
                hasRequiredFields,
                hasRequiredFields ?
                    `Monitoring: ${stats.isMonitoring}, History: ${stats.historySize} entries` :
                    'Missing required service fields'
            );
        } catch (error) {
            this.logResult('Memory Leak Detector Service', false, error.message);
        }
    }

    /**
     * Test batch size recommendations
     */
    testBatchSizeRecommendations() {
        try {
            const batchSize = memoryMonitor.getRecommendedBatchSize();
            const isValidBatchSize = typeof batchSize === 'number' && batchSize > 0 && batchSize <= 100;
            
            this.logResult(
                'Batch Size Recommendations',
                isValidBatchSize,
                isValidBatchSize ?
                    `Recommended batch size: ${batchSize}` :
                    `Invalid batch size: ${batchSize}`
            );
        } catch (error) {
            this.logResult('Batch Size Recommendations', false, error.message);
        }
    }

    /**
     * Test single query vs batch processing decision
     */
    testQueryStrategy() {
        try {
            const testCases = [100, 500, 1000, 2000, 5000];
            let allValid = true;
            const results = [];
            
            for (const recordCount of testCases) {
                const shouldUseSingle = memoryMonitor.shouldUseSingleQuery(recordCount);
                results.push(`${recordCount} records: ${shouldUseSingle ? 'Single' : 'Batch'}`);
                
                if (typeof shouldUseSingle !== 'boolean') {
                    allValid = false;
                }
            }
            
            this.logResult(
                'Query Strategy',
                allValid,
                allValid ?
                    results.join(', ') :
                    'Invalid query strategy responses'
            );
        } catch (error) {
            this.logResult('Query Strategy', false, error.message);
        }
    }

    /**
     * Simulate memory pressure and test response
     */
    async testMemoryPressureResponse() {
        try {
            console.log('\n🧪 Simulating memory pressure...');
            
            // Create some memory pressure (be careful not to crash)
            const memoryHogs = [];
            const initialUsage = memoryMonitor.getMemoryUsage();
            
            // Allocate memory in small chunks
            for (let i = 0; i < 10; i++) {
                memoryHogs.push(new Array(100000).fill('memory-test-data'));
                
                const currentUsage = memoryMonitor.getMemoryUsage();
                console.log(`   Allocated chunk ${i + 1}: ${currentUsage.heapUsagePercent.toFixed(1)}%`);
                
                // Stop if we're getting close to warning threshold
                if (currentUsage.heapUsagePercent > 50) {
                    console.log('   Stopping allocation to prevent issues');
                    break;
                }
            }
            
            // Test if memory monitor detects the increase
            const finalUsage = memoryMonitor.getMemoryUsage();
            const memoryIncrease = finalUsage.heapUsed - initialUsage.heapUsed;
            const increasePercent = ((memoryIncrease / initialUsage.heapUsed) * 100).toFixed(1);
            
            // Clean up
            memoryHogs.length = 0;
            
            // Force GC to clean up
            if (global.gc) {
                global.gc();
            }
            
            const cleanupUsage = memoryMonitor.getMemoryUsage();
            
            this.logResult(
                'Memory Pressure Response',
                memoryIncrease > 0,
                `Memory increased by ${increasePercent}% (${Math.round(memoryIncrease / 1024 / 1024)}MB), cleaned up to ${cleanupUsage.heapUsagePercent.toFixed(1)}%`
            );
            
        } catch (error) {
            this.logResult('Memory Pressure Response', false, error.message);
        }
    }

    /**
     * Run all tests
     */
    async runAllTests() {
        console.log('🧪 Starting Memory Optimization Tests\n');
        
        this.testMemoryMonitorInit();
        this.testMemoryThresholds();
        this.testGarbageCollection();
        this.testProcessingStrategy();
        this.testMemoryLeakDetection();
        this.testMemoryLeakDetectorService();
        this.testBatchSizeRecommendations();
        this.testQueryStrategy();
        
        await this.testMemoryPressureResponse();
        
        this.printSummary();
    }

    /**
     * Print test summary
     */
    printSummary() {
        console.log('\n📊 Test Summary');
        console.log('================');
        
        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        const passRate = ((passed / total) * 100).toFixed(1);
        
        console.log(`Total Tests: ${total}`);
        console.log(`Passed: ${passed}`);
        console.log(`Failed: ${total - passed}`);
        console.log(`Pass Rate: ${passRate}%`);
        
        if (passed === total) {
            console.log('\n🎉 All tests passed! Memory optimization is working correctly.');
        } else {
            console.log('\n⚠️  Some tests failed. Please review the results above.');
            
            // Show failed tests
            const failed = this.testResults.filter(r => !r.passed);
            if (failed.length > 0) {
                console.log('\nFailed Tests:');
                failed.forEach(test => {
                    console.log(`  ❌ ${test.test}: ${test.message}`);
                });
            }
        }
        
        // Show current memory status
        console.log('\n📈 Current Memory Status:');
        memoryMonitor.logMemoryStatus();
    }
}

// Main execution
if (require.main === module) {
    const tester = new MemoryOptimizationTest();
    tester.runAllTests().catch(error => {
        console.error('Error running tests:', error);
        process.exit(1);
    });
}

module.exports = MemoryOptimizationTest;
