const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Provider = sequelize.define('Provider', {
    id: {
        type: DataTypes.STRING(36),
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
    },
    name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        unique: true
    },
    type: {
        type: DataTypes.ENUM('API', 'Custom'),
        allowNull: false,
        defaultValue: 'Custom'
    },
    country: {
        type: DataTypes.STRING(100),
        allowNull: true
    },
    apiEndpoint: {
        type: DataTypes.STRING(255),
        allowNull: true
    },
    apiKey: {
        type: DataTypes.STRING(255),
        allowNull: true
    },
    status: {
        type: DataTypes.ENUM('active', 'inactive'),
        allowNull: false,
        defaultValue: 'active'
    },
    description: {
        type: DataTypes.TEXT,
        allowNull: true
    }
}, {
    tableName: 'providers',
    timestamps: true
});

// Define association method to be called from models/index.js
Provider.associate = (models) => {
    Provider.hasMany(models.EsimPlan, {
        foreignKey: 'providerId',
        as: 'plans'
    });
};

module.exports = Provider;
