const { v4: uuidv4 } = require('uuid');
const sequelize = require('../config/database');
const { Op } = require('sequelize');
const CentralWallet = require('../models/centralWallet');
const Wallet = require('../models/Wallet');
const WalletTransaction = require('../models/WalletTransaction');
const { addDecimal, formatDecimal, isDecimalEqual } = require('../utils/decimalUtils');

// Get the central wallet information
exports.getCentralWallet = async (req, res) => {
    try {
        let cw = await CentralWallet.findOne();
        if (!cw) {
            cw = await CentralWallet.create({ totalBalance: 0.00 });
        }

        // Get total amount transferred to partners
        const totalTransferredToPartners = await WalletTransaction.sum('amount', {
            where: {
                type: 'credit',
                status: 'completed'
            }
        }) || 0;

        // Get total amount received back
        const totalReceivedBack = await WalletTransaction.sum('amount', {
            where: {
                type: 'debit',
                status: 'completed'
            }
        }) || 0;

        // Get total number of transactions
        const totalTransactions = await WalletTransaction.count({
            where: {
                status: 'completed'
            }
        });

        // Get recent transactions
        const recentTransactions = await WalletTransaction.findAll({
            limit: 5,
            order: [['createdAt', 'DESC']],
            where: {
                status: 'completed'
            },
            include: [{
                model: Wallet,
                as: 'wallet',
                attributes: ['userId']
            }]
        });

        res.json({
            ...cw.toJSON(),
            statistics: {
                totalTransferredToPartners: Number(totalTransferredToPartners),
                totalReceivedBack: Number(totalReceivedBack),
                totalTransactions,
                netAmountWithPartners: Number(totalTransferredToPartners - totalReceivedBack),
                recentTransactions
            }
        });
    } catch (error) {
        console.error('Error fetching central wallet:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};

// Update the central wallet total balance (admin only)
exports.updateCentralWallet = async (req, res) => {
    try {
        const { totalBalance } = req.body;
        const newTotal = Number(totalBalance);
        if (isNaN(newTotal) || newTotal < 0) {
            return res.status(400).json({ message: 'Invalid total balance' });
        }
        let cw = await CentralWallet.findOne();
        if (!cw) {
            cw = await CentralWallet.create({ totalBalance: newTotal });
        } else {
            cw.totalBalance = newTotal;
            await cw.save();
        }
        res.json({ message: 'Central wallet total balance updated', totalBalance: cw.totalBalance });
    } catch (error) {
        console.error('Error updating central wallet:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};

// Transfer funds from the central wallet to a partner's wallet
exports.addPartnerFunds = async (req, res) => {
    const t = await sequelize.transaction();
    try {
        const { partnerId } = req.params;
        const { amount, description } = req.body;
        const fundAmount = Number(amount);
        
        // Validate amount
        if (isNaN(fundAmount) || fundAmount <= 0) {
            await t.rollback();
            return res.status(400).json({ 
                message: 'Invalid fund amount',
                error: 'INVALID_AMOUNT'
            });
        }

        // Retrieve the central wallet
        let cw = await CentralWallet.findOne({ transaction: t });
        if (!cw) {
            cw = await CentralWallet.create({ totalBalance: 0.00 }, { transaction: t });
        }
        
        // Check central wallet balance
        if (Number(cw.totalBalance) < fundAmount) {
            await t.rollback();
            return res.status(400).json({ 
                message: 'Insufficient funds in central wallet',
                error: 'INSUFFICIENT_CENTRAL_FUNDS',
                available: Number(cw.totalBalance),
                requested: fundAmount
            });
        }

        // Retrieve or create the partner's wallet with row-level locking
        let partnerWallet = await Wallet.findOne({ 
            where: { userId: partnerId },
            transaction: t,
            lock: true
        });
        
        if (!partnerWallet) {
            partnerWallet = await Wallet.create({
                userId: partnerId,
                balance: 0.00,
                maxBalance: 1000.00,
                currencyCode: 'USD',
                isActive: true
            }, { transaction: t });
        }

        // Calculate new balance and check limits with proper decimal precision
        const currentBalance = Number(partnerWallet.balance);
        const maxBalance = Number(partnerWallet.maxBalance);
        const newBalance = addDecimal(currentBalance, fundAmount);

        if (newBalance > maxBalance) {
            await t.rollback();
            return res.status(400).json({ 
                message: `Cannot add $${fundAmount}. This would exceed the maximum wallet balance of $${maxBalance}. Current balance: $${currentBalance}`,
                error: 'MAX_BALANCE_EXCEEDED',
                details: {
                    currentBalance,
                    maxBalance,
                    requested: fundAmount,
                    wouldBecome: newBalance,
                    allowedToAdd: maxBalance - currentBalance
                }
            });
        }

        // 1. First deduct from central wallet
        cw.totalBalance = Number(cw.totalBalance) - fundAmount;
        await cw.save({ transaction: t });

        // 2. Create the transaction record first
        const transactionId = uuidv4();
        await sequelize.query(
            'INSERT INTO wallettransactions (id, walletId, type, amount, balance, description, status, metadata, referenceType, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())',
            {
                replacements: [
                    transactionId,
                    partnerWallet.id,
                    'credit',
                    formatDecimal(fundAmount), // Ensure proper decimal precision
                    formatDecimal(newBalance), // Ensure proper decimal precision
                    description || 'The Amount has been successfully added to your wallet.',
                    'completed',
                    JSON.stringify({ adminId: req.user.id }),
                    'other'
                ],
                type: sequelize.QueryTypes.INSERT,
                transaction: t
            }
        );

        // 3. Then update partner's wallet balance using raw query to ensure proper order
        await sequelize.query(
            'UPDATE wallets SET balance = balance + ?, updatedAt = NOW() WHERE id = ? AND (balance + ?) <= maxBalance',
            {
                replacements: [fundAmount, partnerWallet.id, fundAmount],
                type: sequelize.QueryTypes.UPDATE,
                transaction: t
            }
        );

        // 4. Verify the update was successful by checking the new balance
        const updatedWallet = await Wallet.findOne({
            where: { id: partnerWallet.id },
            transaction: t,
            lock: true
        });

        // Use proper decimal comparison to handle floating-point precision issues
        const actualBalance = Number(updatedWallet.balance);
        const expectedBalance = newBalance;

        // Use decimal utility for proper comparison
        if (!isDecimalEqual(actualBalance, expectedBalance)) {
            const balanceDifference = Math.abs(actualBalance - expectedBalance);
            console.error('Balance verification failed:', {
                expected: expectedBalance,
                actual: actualBalance,
                difference: balanceDifference,
                partnerId,
                fundAmount
            });
            await t.rollback();
            return res.status(400).json({
                message: 'Failed to update wallet balance - verification failed',
                error: 'UPDATE_FAILED',
                details: {
                    expected: expectedBalance,
                    actual: actualBalance,
                    difference: balanceDifference
                }
            });
        }

        await t.commit();

        console.log('Funds transferred successfully:', {
            partnerId,
            amount: fundAmount,
            previousBalance: currentBalance,
            newBalance: Number(updatedWallet.balance),
            transactionId
        });

        res.json({
            message: 'Funds transferred successfully',
            centralWallet: {
                totalBalance: Number(cw.totalBalance).toFixed(2)
            },
            partnerWallet: {
                balance: Number(updatedWallet.balance).toFixed(2),
                maxBalance: Number(maxBalance).toFixed(2),
                availableLimit: (maxBalance - Number(updatedWallet.balance)).toFixed(2)
            },
            transaction: {
                id: transactionId,
                amount: Number(fundAmount).toFixed(2),
                type: 'credit',
                status: 'completed'
            }
        });
    } catch (error) {
        await t.rollback();
        console.error('Error transferring funds to partner:', error);
        
        if (error.name === 'SequelizeDatabaseError' && error.parent?.code === 'ER_SIGNAL_EXCEPTION') {
            return res.status(400).json({ 
                message: error.parent.sqlMessage,
                error: 'MAX_BALANCE_EXCEEDED'
            });
        }
        
        res.status(500).json({ 
            message: 'Internal server error',
            error: 'INTERNAL_ERROR'
        });
    }
};

// Edit partner wallet funds and adjust central wallet accordingly
exports.editPartnerFunds = async (req, res) => {
    const t = await sequelize.transaction();
    try {
        const { partnerId } = req.params;
        const { newAmount, currentAmount, description } = req.body;

        const newBalance = Number(newAmount);
        const currentBalance = Number(currentAmount);
        const fundDifference = newBalance - currentBalance;

        // Validate input
        if (isNaN(newBalance) || newBalance < 0) {
            await t.rollback();
            return res.status(400).json({
                message: 'Invalid amount. Balance cannot be negative.',
                error: 'INVALID_AMOUNT'
            });
        }

        // Get partner's wallet with lock
        const partnerWallet = await Wallet.findOne({
            where: { userId: partnerId },
            transaction: t,
            lock: true
        });

        if (!partnerWallet) {
            await t.rollback();
            return res.status(404).json({
                message: 'Partner wallet not found',
                error: 'WALLET_NOT_FOUND'
            });
        }

        // Verify current amount matches actual wallet balance
        const actualCurrentBalance = Number(partnerWallet.balance);
        if (Math.abs(actualCurrentBalance - currentBalance) > 0.01) {
            await t.rollback();
            return res.status(400).json({
                message: 'Current balance mismatch. Please refresh and try again.',
                error: 'BALANCE_MISMATCH',
                details: {
                    provided: currentBalance,
                    actual: actualCurrentBalance
                }
            });
        }

        // Check if new balance exceeds maximum balance
        const maxBalance = Number(partnerWallet.maxBalance);
        if (newBalance > maxBalance) {
            await t.rollback();
            return res.status(400).json({
                message: `New balance $${newBalance.toFixed(2)} exceeds maximum balance limit of $${maxBalance.toFixed(2)}`,
                error: 'MAX_BALANCE_EXCEEDED',
                details: {
                    newBalance,
                    maxBalance,
                    currentBalance: actualCurrentBalance
                }
            });
        }

        // Get central wallet
        let centralWallet = await CentralWallet.findOne({ transaction: t });
        if (!centralWallet) {
            centralWallet = await CentralWallet.create({ totalBalance: 0.00 }, { transaction: t });
        }

        // Handle central wallet adjustments based on fund difference
        if (fundDifference < 0) {
            // Reducing partner funds - add back to central wallet
            await centralWallet.increment('totalBalance', {
                by: Math.abs(fundDifference),
                transaction: t
            });
        } else if (fundDifference > 0) {
            // Increasing partner funds - check central wallet has enough
            const centralBalance = Number(centralWallet.totalBalance);
            if (centralBalance < fundDifference) {
                await t.rollback();
                return res.status(400).json({
                    message: 'Insufficient funds in central wallet',
                    error: 'INSUFFICIENT_CENTRAL_FUNDS',
                    details: {
                        available: centralBalance,
                        requested: fundDifference
                    }
                });
            }
            // Deduct from central wallet
            await centralWallet.decrement('totalBalance', {
                by: fundDifference,
                transaction: t
            });
        }

        // For admin adjustments, we need to handle the transaction creation carefully
        // to avoid trigger validation issues when reducing balance significantly

        if (fundDifference !== 0) {
            // Create transaction record BEFORE updating wallet balance for proper validation
            const transactionRecord = await WalletTransaction.create({
                id: uuidv4(),
                walletId: partnerWallet.id,
                type: fundDifference >= 0 ? 'credit' : 'debit',
                amount: Math.abs(fundDifference),
                balance: newBalance, // Target balance after adjustment
                description: description || `Wallet balance adjusted by admin`,
                status: 'completed',
                referenceType: 'manual',
                metadata: {
                    adminId: req.user.id,
                    adjustment: {
                        from: currentBalance,
                        to: newBalance,
                        difference: fundDifference
                    },
                    adminAdjustment: true
                }
            }, { transaction: t });
        }

        // Update the wallet balance
        await partnerWallet.update({ balance: newBalance }, { transaction: t });

        // Refresh central wallet to get updated balance
        await centralWallet.reload({ transaction: t });

        await t.commit();

        res.json({
            message: 'Wallet balance adjusted successfully',
            centralWallet: {
                totalBalance: Number(centralWallet.totalBalance).toFixed(2)
            },
            partnerWallet: {
                balance: Number(newBalance).toFixed(2),
                maxBalance: Number(maxBalance).toFixed(2),
                previousBalance: Number(currentBalance).toFixed(2)
            },
            adjustment: {
                difference: Number(fundDifference).toFixed(2),
                type: fundDifference >= 0 ? 'increase' : 'decrease'
            }
        });
    } catch (error) {
        await t.rollback();
        console.error('Error adjusting wallet balance:', error);
        console.error('Error details:', {
            name: error.name,
            message: error.message,
            code: error.code,
            parent: error.parent,
            sql: error.sql,
            stack: error.stack
        });

        // Handle specific database errors
        if (error.name === 'SequelizeDatabaseError') {
            if (error.parent?.code === 'ER_SIGNAL_EXCEPTION') {
                return res.status(400).json({
                    message: error.parent.sqlMessage || 'Database validation error',
                    error: 'DATABASE_VALIDATION_ERROR',
                    details: {
                        sqlState: error.parent.sqlState,
                        sqlMessage: error.parent.sqlMessage
                    }
                });
            }

            // Handle other database errors
            return res.status(400).json({
                message: error.message || 'Database error occurred',
                error: 'DATABASE_ERROR',
                details: {
                    code: error.parent?.code,
                    errno: error.parent?.errno,
                    sqlMessage: error.parent?.sqlMessage
                }
            });
        }

        // Handle Sequelize validation errors
        if (error.name === 'SequelizeValidationError') {
            return res.status(400).json({
                message: 'Validation error',
                error: 'VALIDATION_ERROR',
                details: error.errors?.map(e => ({
                    field: e.path,
                    message: e.message,
                    value: e.value
                }))
            });
        }

        // Handle other specific errors
        const errorData = error.response?.data;
        if (errorData) {
            return res.status(error.response.status || 500).json(errorData);
        }

        res.status(500).json({
            message: 'Internal server error while adjusting wallet balance',
            error: 'INTERNAL_SERVER_ERROR',
            details: {
                name: error.name,
                message: error.message
            }
        });
    }
};

// Get partner wallet transactions
exports.getPartnerTransactions = async (req, res) => {
    try {
        const { partnerId } = req.params;
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        // Get partner's wallet
        const wallet = await Wallet.findOne({
            where: { userId: partnerId }
        });

        if (!wallet) {
            return res.status(404).json({ message: 'Partner wallet not found' });
        }

        // Get transactions with pagination
        const transactions = await WalletTransaction.findAll({
            where: { walletId: wallet.id },
            order: [['createdAt', 'DESC']],
            limit: Number(limit),
            offset: Number(offset)
        });

        res.json({
            transactions,
            page: Number(page),
            limit: Number(limit)
        });
    } catch (error) {
        console.error('Error fetching wallet transactions:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};
