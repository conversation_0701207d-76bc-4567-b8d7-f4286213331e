/**
 * Memory Management Controller
 * Provides endpoints for monitoring, managing, and optimizing memory usage
 */

const memoryCleanup = require('../utils/memoryCleanup');
const searchMemoryManager = require('../utils/searchMemoryManager');
const predictiveMemoryManager = require('../utils/predictiveMemoryManager');
const emergencyMemoryProtection = require('../utils/emergencyMemoryProtection');
const memoryMonitor = require('../utils/memoryMonitor');
const memoryTestUtils = require('../utils/memoryTestUtils');

/**
 * Get comprehensive memory status
 */
exports.getMemoryStatus = async (req, res) => {
    try {
        const memoryReport = memoryCleanup.getMemoryReport();
        const searchStats = searchMemoryManager.getActiveSearchStats();
        const memoryInsights = predictiveMemoryManager.getMemoryInsights();
        const emergencyStatus = emergencyMemoryProtection.getMemoryStatus();
        
        const status = {
            timestamp: new Date().toISOString(),
            overall: {
                status: emergencyStatus.isCritical ? 'critical' : 
                       emergencyStatus.isWarning ? 'warning' : 'healthy',
                heapUsed: memoryReport.memoryUsage.heapUsed,
                heapTotal: memoryReport.memoryUsage.heapTotal,
                heapUtilization: memoryReport.memoryUsage.heapUtilization,
                rss: memoryReport.memoryUsage.rss
            },
            detailed: memoryReport,
            activeSearches: searchStats,
            predictiveInsights: memoryInsights,
            emergency: emergencyStatus,
            recommendations: generateMemoryRecommendations(memoryReport, searchStats, memoryInsights)
        };

        res.json(status);
    } catch (error) {
        console.error('Error getting memory status:', error);
        res.status(500).json({
            error: 'Failed to get memory status',
            message: error.message
        });
    }
};

/**
 * Trigger manual memory cleanup
 */
exports.triggerMemoryCleanup = async (req, res) => {
    try {
        const { type = 'standard', aggressive = false } = req.body;
        
        console.log(`🧹 [MANUAL CLEANUP] Triggered ${type} cleanup (aggressive: ${aggressive})`);
        
        const beforeMemory = process.memoryUsage();
        let cleanupResult;

        switch (type) {
            case 'emergency':
                cleanupResult = memoryCleanup.emergencyCleanup();
                break;
            case 'proactive':
                cleanupResult = memoryCleanup.proactiveCleanup();
                break;
            case 'gc':
                cleanupResult = memoryCleanup.forceGarbageCollection({
                    cycles: aggressive ? 3 : 1,
                    measureHeapSpaces: true
                });
                break;
            case 'pools':
                memoryCleanup.clearAllPools();
                cleanupResult = { type: 'pools_cleared' };
                break;
            case 'weak_references':
                const cleared = memoryCleanup.cleanupWeakReferences();
                cleanupResult = { type: 'weak_references', cleared };
                break;
            default:
                // Standard cleanup
                memoryCleanup.executeCleanupCallbacks();
                cleanupResult = memoryCleanup.forceGarbageCollection({ cycles: 1 });
        }

        const afterMemory = process.memoryUsage();
        const memoryFreed = (beforeMemory.heapUsed - afterMemory.heapUsed) / 1024 / 1024;

        res.json({
            success: true,
            type,
            aggressive,
            memoryBefore: (beforeMemory.heapUsed / 1024 / 1024).toFixed(1) + 'MB',
            memoryAfter: (afterMemory.heapUsed / 1024 / 1024).toFixed(1) + 'MB',
            memoryFreed: memoryFreed.toFixed(1) + 'MB',
            cleanupResult,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error triggering memory cleanup:', error);
        res.status(500).json({
            error: 'Failed to trigger memory cleanup',
            message: error.message
        });
    }
};

/**
 * Get memory predictions for operation
 */
exports.getMemoryPrediction = async (req, res) => {
    try {
        const { operationType, searchType, searchTerm, resultCount } = req.query;
        
        const operationInfo = {
            type: operationType || 'search',
            searchType: searchType || 'none',
            searchTerm: searchTerm || '',
            resultCount: parseInt(resultCount) || 0
        };

        const currentMemory = process.memoryUsage().heapUsed / 1024 / 1024;
        const prediction = predictiveMemoryManager.predictMemoryUsage(operationInfo);
        const shouldProceed = predictiveMemoryManager.shouldProceedWithOperation(operationInfo, currentMemory);

        res.json({
            operationInfo,
            currentMemory: currentMemory.toFixed(1) + 'MB',
            prediction,
            shouldProceed,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error getting memory prediction:', error);
        res.status(500).json({
            error: 'Failed to get memory prediction',
            message: error.message
        });
    }
};

/**
 * Get active search operations
 */
exports.getActiveSearches = async (req, res) => {
    try {
        const stats = searchMemoryManager.getActiveSearchStats();
        
        res.json({
            ...stats,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error getting active searches:', error);
        res.status(500).json({
            error: 'Failed to get active searches',
            message: error.message
        });
    }
};

/**
 * Emergency memory cleanup for all operations
 */
exports.emergencyCleanup = async (req, res) => {
    try {
        console.log('🚨 [EMERGENCY] Manual emergency cleanup triggered');
        
        const beforeMemory = process.memoryUsage();
        
        searchMemoryManager.emergencyCleanupAll();
        
        const emergencyResult = emergencyMemoryProtection.emergencyCleanup();
        
        const cleanupResult = memoryCleanup.emergencyCleanup();
        
        const afterMemory = process.memoryUsage();
        const memoryFreed = (beforeMemory.heapUsed - afterMemory.heapUsed) / 1024 / 1024;

        res.json({
            success: true,
            memoryBefore: (beforeMemory.heapUsed / 1024 / 1024).toFixed(1) + 'MB',
            memoryAfter: (afterMemory.heapUsed / 1024 / 1024).toFixed(1) + 'MB',
            memoryFreed: memoryFreed.toFixed(1) + 'MB',
            emergencyResult,
            cleanupResult,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error in emergency cleanup:', error);
        res.status(500).json({
            error: 'Failed to perform emergency cleanup',
            message: error.message
        });
    }
};

/**
 * Get memory snapshots and history
 */
exports.getMemoryHistory = async (req, res) => {
    try {
        const { limit = 20 } = req.query;
        
        const report = memoryCleanup.getMemoryReport();
        const insights = predictiveMemoryManager.getMemoryInsights();
        
        res.json({
            memorySnapshots: report.memorySnapshots || [],
            cleanupHistory: report.cleanupHistory || [],
            operationHistory: insights.recentOperations || [],
            patterns: insights.topRiskyPatterns || [],
            limit: parseInt(limit),
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error getting memory history:', error);
        res.status(500).json({
            error: 'Failed to get memory history',
            message: error.message
        });
    }
};

/**
 * Configure memory management settings
 */
exports.configureMemorySettings = async (req, res) => {
    try {
        const { 
            autoCleanupThreshold, 
            autoCleanupInterval, 
            emergencyThreshold,
            enablePredictiveManagement 
        } = req.body;

        const settings = {};

        if (autoCleanupThreshold) {
            settings.autoCleanupThreshold = autoCleanupThreshold;
        }

        if (autoCleanupInterval) {
            settings.autoCleanupInterval = autoCleanupInterval;
        }

        if (emergencyThreshold) {
            settings.emergencyThreshold = emergencyThreshold;
        }

        if (enablePredictiveManagement !== undefined) {
            settings.enablePredictiveManagement = enablePredictiveManagement;
        }

        // Apply settings (this would typically be stored in a configuration system)
        console.log('🔧 [CONFIG] Memory management settings updated:', settings);

        res.json({
            success: true,
            settings,
            message: 'Memory management settings updated successfully',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error configuring memory settings:', error);
        res.status(500).json({
            error: 'Failed to configure memory settings',
            message: error.message
        });
    }
};

/**
 * Generate memory recommendations based on current state
 */
function generateMemoryRecommendations(memoryReport, searchStats, insights) {
    const recommendations = [];
    
    // Parse memory usage
    const heapUsedMB = parseFloat(memoryReport.memoryUsage.heapUsed);
    const heapUtilization = parseFloat(memoryReport.memoryUsage.heapUtilization);
    
    // High memory usage recommendations
    if (heapUsedMB > 1000) {
        recommendations.push({
            priority: 'high',
            category: 'memory_usage',
            message: 'High memory usage detected. Consider triggering cleanup.',
            action: 'trigger_cleanup'
        });
    }
    
    // High heap utilization recommendations
    if (heapUtilization > 80) {
        recommendations.push({
            priority: 'high',
            category: 'heap_utilization',
            message: 'High heap utilization. Memory pressure detected.',
            action: 'force_gc'
        });
    }
    
    // Active searches recommendations
    if (searchStats.activeSearches > 5) {
        recommendations.push({
            priority: 'medium',
            category: 'active_operations',
            message: 'Many active search operations. Monitor for memory leaks.',
            action: 'monitor_searches'
        });
    }
    
    // High-risk patterns recommendations
    if (insights.riskDistribution.high > 0 || insights.riskDistribution.critical > 0) {
        recommendations.push({
            priority: 'high',
            category: 'risk_patterns',
            message: 'High-risk memory patterns detected. Review operation patterns.',
            action: 'review_patterns'
        });
    }
    
    // Object pool recommendations
    if (memoryReport.objectPools && memoryReport.objectPools.length > 0) {
        const inefficientPools = memoryReport.objectPools.filter(pool => pool.efficiency < 50);
        if (inefficientPools.length > 0) {
            recommendations.push({
                priority: 'medium',
                category: 'object_pools',
                message: `${inefficientPools.length} object pools have low efficiency.`,
                action: 'optimize_pools'
            });
        }
    }
    
    // Streaming cleanup recommendations
    if (memoryReport.streamingCleanup && memoryReport.streamingCleanup.queueLength > 0) {
        recommendations.push({
            priority: 'low',
            category: 'streaming_cleanup',
            message: 'Streaming cleanup in progress. Monitor completion.',
            action: 'monitor_streaming'
        });
    }
    
    // Default recommendation if no issues
    if (recommendations.length === 0) {
        recommendations.push({
            priority: 'low',
            category: 'status',
            message: 'Memory usage is within normal parameters.',
            action: 'continue_monitoring'
        });
    }
    
    return recommendations;
}

/**
 * Run memory cleanup tests
 */
exports.runMemoryTests = async (req, res) => {
    try {
        console.log('🧪 [MEMORY TESTS] Starting memory cleanup tests via API');

        if (memoryTestUtils.isRunning) {
            return res.status(409).json({
                error: 'Tests already running',
                message: 'Memory tests are currently in progress. Please wait for completion.'
            });
        }

        const testPromise = memoryTestUtils.runMemoryCleanupTests();

        res.json({
            success: true,
            message: 'Memory cleanup tests started',
            status: 'running',
            timestamp: new Date().toISOString()
        });

        testPromise.then(results => {
            console.log('🧪 [MEMORY TESTS] Test results:', results.summary);
        }).catch(error => {
            console.error('🧪 [MEMORY TESTS] Test failed:', error);
        });

    } catch (error) {
        console.error('Error starting memory tests:', error);
        res.status(500).json({
            error: 'Failed to start memory tests',
            message: error.message
        });
    }
};

/**
 * Get memory test results
 */
exports.getMemoryTestResults = async (req, res) => {
    try {
        const history = memoryTestUtils.getTestHistory();
        const isRunning = memoryTestUtils.isRunning;

        res.json({
            isRunning,
            testHistory: history,
            latestResults: history.length > 0 ? history[history.length - 1] : null,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error getting memory test results:', error);
        res.status(500).json({
            error: 'Failed to get memory test results',
            message: error.message
        });
    }
};

module.exports = {
    getMemoryStatus: exports.getMemoryStatus,
    triggerMemoryCleanup: exports.triggerMemoryCleanup,
    getMemoryPrediction: exports.getMemoryPrediction,
    getActiveSearches: exports.getActiveSearches,
    emergencyCleanup: exports.emergencyCleanup,
    getMemoryHistory: exports.getMemoryHistory,
    configureMemorySettings: exports.configureMemorySettings,
    runMemoryTests: exports.runMemoryTests,
    getMemoryTestResults: exports.getMemoryTestResults
};
