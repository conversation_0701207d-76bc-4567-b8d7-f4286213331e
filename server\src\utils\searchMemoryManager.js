/**
 * Search Memory Manager
 * Specialized memory management for search operations to prevent memory leaks
 * during database queries, relevance scoring, and result processing
 */

const memoryCleanup = require('./memoryCleanup');
const { EventEmitter } = require('events');

class SearchMemoryManager extends EventEmitter {
    constructor() {
        super();
        this.activeSearches = new Map();
        this.searchMemoryThresholds = {
            broad: 600, // 600MB threshold for broad searches (appropriate for 3.7GB system)
            specific: 300, // 300MB threshold for specific searches
            none: 150 // 150MB threshold for no-search operations
        };
        this.cleanupStrategies = new Map();
        this.initializeCleanupStrategies();
    }

    /**
     * Initialize cleanup strategies for different search types
     */
    initializeCleanupStrategies() {
        this.cleanupStrategies.set('broad', {
            maxMemoryIncrease: 400, // 400MB increase allowed
            cleanupFrequency: 'immediate',
            streamingThreshold: 1000,
            gcCycles: 2,
            poolClearRatio: 0.5,
            emergencyThreshold: 800 // 800MB emergency threshold
        });

        // Specific search strategy - Optimized for 3.7GB system
        this.cleanupStrategies.set('specific', {
            maxMemoryIncrease: 200, // 200MB increase allowed
            cleanupFrequency: 'delayed',
            streamingThreshold: 5000,
            gcCycles: 1,
            poolClearRatio: 0.8,
            emergencyThreshold: 400 // 400MB emergency threshold
        });

        // No search strategy - Optimized for 3.7GB system
        this.cleanupStrategies.set('none', {
            maxMemoryIncrease: 100, // 100MB increase allowed
            cleanupFrequency: 'scheduled',
            streamingThreshold: 10000,
            gcCycles: 1,
            poolClearRatio: 0.9,
            emergencyThreshold: 200 // 200MB emergency threshold
        });
    }

    /**
     * Start monitoring a search operation
     */
    startSearchOperation(operationId, searchType, searchTerm = '') {
        const startMemory = process.memoryUsage().heapUsed / 1024 / 1024;
        const strategy = this.cleanupStrategies.get(searchType) || this.cleanupStrategies.get('broad');
        
        const operation = {
            id: operationId,
            searchType,
            searchTerm,
            startMemory,
            startTime: Date.now(),
            strategy,
            memorySnapshots: [],
            cleanupActions: [],
            isActive: true
        };

        this.activeSearches.set(operationId, operation);
        
        console.log(`🔍 [SEARCH MEMORY] Starting ${searchType} search operation: "${searchTerm}" (${startMemory.toFixed(1)}MB)`);
        
        this.takeOperationSnapshot(operationId, 'start');
        
        return operation;
    }

    /**
     * Check memory during search operation
     */
    checkSearchMemory(operationId, phase = 'processing') {
        const operation = this.activeSearches.get(operationId);
        if (!operation || !operation.isActive) {
            return { canContinue: true, reason: 'operation_not_found' };
        }

        const currentMemory = process.memoryUsage().heapUsed / 1024 / 1024;
        const memoryIncrease = currentMemory - operation.startMemory;
        const strategy = operation.strategy;

        this.takeOperationSnapshot(operationId, phase);

        if (memoryIncrease > strategy.maxMemoryIncrease) {
            console.warn(`⚠️ [SEARCH MEMORY] Operation ${operationId} exceeded memory threshold: +${memoryIncrease.toFixed(1)}MB`);
            
            this.triggerSearchCleanup(operationId, 'threshold_exceeded');
            
            if (memoryIncrease > strategy.emergencyThreshold) {
                console.error(`🚨 [SEARCH MEMORY] EMERGENCY: Operation ${operationId} exceeded emergency threshold - ABORTING`);
                this.abortSearchOperation(operationId, 'emergency_threshold');
                return { canContinue: false, reason: 'emergency_threshold', memoryIncrease };
            }
        }

        const memoryPressure = this.calculateMemoryPressure();
        if (memoryPressure > 0.85) {
            console.warn(`⚠️ [SEARCH MEMORY] High memory pressure: ${(memoryPressure * 100).toFixed(1)}%`);
            this.triggerSearchCleanup(operationId, 'memory_pressure');
        }

        return { 
            canContinue: true, 
            memoryIncrease, 
            memoryPressure,
            currentMemory: currentMemory.toFixed(1) + 'MB'
        };
    }

    /**
     * Calculate current memory pressure using absolute thresholds appropriate for 4GB heap
     */
    calculateMemoryPressure() {
        const memUsage = process.memoryUsage();
        const heapUsedMB = memUsage.heapUsed / 1024 / 1024;

        // Use absolute thresholds based on 4GB heap limit
        if (heapUsedMB > 1600) return 1.0; // Critical (80% of 4GB)
        if (heapUsedMB > 1400) return 0.9; // Very high (60% of 4GB)
        if (heapUsedMB > 1200) return 0.8; // High (40% of 4GB)
        if (heapUsedMB > 1000) return 0.6;  // Moderate (20% of 4GB)
        if (heapUsedMB > 600) return 0.4;  // Low (10% of 4GB)
        return heapUsedMB / 600; // Very low (0-0.4)
    }

    /**
     * Take memory snapshot for operation
     */
    takeOperationSnapshot(operationId, phase) {
        const operation = this.activeSearches.get(operationId);
        if (!operation) return;

        const snapshot = memoryCleanup.takeMemorySnapshot(`${operationId}_${phase}`);
        operation.memorySnapshots.push({
            phase,
            timestamp: Date.now(),
            memory: snapshot.memory,
            heap: snapshot.heap
        });
    }

    /**
     * Trigger cleanup for search operation
     */
    async triggerSearchCleanup(operationId, reason) {
        const operation = this.activeSearches.get(operationId);
        if (!operation) return;

        console.log(`🧹 [SEARCH MEMORY] Triggering cleanup for ${operationId} (reason: ${reason})`);

        const cleanupAction = {
            timestamp: Date.now(),
            reason,
            memoryBefore: process.memoryUsage().heapUsed / 1024 / 1024
        };

        const strategy = operation.strategy;

        if (strategy.cleanupFrequency === 'immediate' || reason === 'emergency_threshold') {
            const gcResult = memoryCleanup.forceGarbageCollection({
                cycles: strategy.gcCycles,
                logDetails: false
            });

            // Wait a moment for GC to complete
            await new Promise(resolve => setTimeout(resolve, 100));

            memoryCleanup.clearLRUPools();

            memoryCleanup.executeCleanupCallbacks();

            await new Promise(resolve => setTimeout(resolve, 100));
        }

        cleanupAction.memoryAfter = process.memoryUsage().heapUsed / 1024 / 1024;
        cleanupAction.memoryFreed = cleanupAction.memoryBefore - cleanupAction.memoryAfter;

        operation.cleanupActions.push(cleanupAction);

        console.log(`🧹 [SEARCH MEMORY] Cleanup completed: freed ${cleanupAction.memoryFreed.toFixed(1)}MB`);

        this.emit('searchCleanup', { operationId, cleanupAction });
    }

    /**
     * Abort search operation due to memory constraints
     */
    abortSearchOperation(operationId, reason) {
        const operation = this.activeSearches.get(operationId);
        if (!operation) return;

        operation.isActive = false;
        operation.abortReason = reason;
        operation.endTime = Date.now();
        operation.duration = operation.endTime - operation.startTime;

        console.error(`🚨 [SEARCH MEMORY] ABORTED operation ${operationId}: ${reason}`);
        
        this.triggerSearchCleanup(operationId, 'abort_cleanup');
        
        this.emit('searchAborted', { operationId, reason, operation });
    }

    /**
     * Complete search operation successfully
     */
    completeSearchOperation(operationId, results = {}) {
        const operation = this.activeSearches.get(operationId);
        if (!operation) return;

        operation.isActive = false;
        operation.endTime = Date.now();
        operation.duration = operation.endTime - operation.startTime;
        operation.results = results;

        const finalMemory = process.memoryUsage().heapUsed / 1024 / 1024;
        const totalMemoryIncrease = finalMemory - operation.startMemory;

        this.takeOperationSnapshot(operationId, 'complete');

        console.log(`✅ [SEARCH MEMORY] Completed operation ${operationId} in ${operation.duration}ms (+${totalMemoryIncrease.toFixed(1)}MB)`);

        if (operation.strategy.cleanupFrequency !== 'scheduled') {
            this.triggerSearchCleanup(operationId, 'completion_cleanup');
        }

        this.emit('searchCompleted', { operationId, operation, totalMemoryIncrease });

        setTimeout(() => {
            this.activeSearches.delete(operationId);
        }, 30000); // Keep for 30 seconds
    }

    /**
     * Clean up search data using enhanced memory cleanup
     */
    cleanupSearchData(searchData, searchType = 'broad') {
        return memoryCleanup.cleanupSearchResults(searchData, searchType);
    }

    /**
     * Get memory statistics for all active searches
     */
    getActiveSearchStats() {
        const stats = {
            activeSearches: this.activeSearches.size,
            totalMemoryUsed: 0,
            searchesByType: {},
            memoryByType: {}
        };

        const currentMemory = process.memoryUsage().heapUsed / 1024 / 1024;

        for (const [id, operation] of this.activeSearches) {
            if (operation.isActive) {
                const memoryUsed = currentMemory - operation.startMemory;
                stats.totalMemoryUsed += memoryUsed;

                if (!stats.searchesByType[operation.searchType]) {
                    stats.searchesByType[operation.searchType] = 0;
                    stats.memoryByType[operation.searchType] = 0;
                }

                stats.searchesByType[operation.searchType]++;
                stats.memoryByType[operation.searchType] += memoryUsed;
            }
        }

        return stats;
    }

    /**
     * Emergency cleanup for all active searches
     */
    emergencyCleanupAll() {
        console.log('🚨 [SEARCH MEMORY] EMERGENCY CLEANUP FOR ALL ACTIVE SEARCHES');
        
        for (const [operationId, operation] of this.activeSearches) {
            if (operation.isActive) {
                this.triggerSearchCleanup(operationId, 'emergency_cleanup_all');
            }
        }

        memoryCleanup.emergencyCleanup();
        
        this.emit('emergencyCleanup', { activeSearches: this.activeSearches.size });
    }
}

module.exports = new SearchMemoryManager();
