<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Partner API Documentation</title>
    
    <!-- Using local CSS resources to avoid CSP issues -->
    <link href="/api/config/styles/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/api/config/styles/highlight.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            padding-top: 2rem;
            padding-bottom: 2rem;
            scroll-behavior: smooth;
        }
        .container {
            max-width: 1200px;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }
        h1 {
            border-bottom: 2px solid #eaecef;
            padding-bottom: 0.3rem;
        }
        h2 {
            border-bottom: 1px solid #eaecef;
            padding-bottom: 0.3rem;
        }
        pre {
            background-color: #f8f9fa;
            border-radius: 6px;
            padding: 16px;
            overflow: auto;
        }
        code {
            font-family: SFMono-Regular, Consolas, Liberation Mono, <PERSON>lo, monospace;
            font-size: 85%;
        }
        .endpoint {
            background-color: #f8f9fa;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 2rem;
            border-left: 4px solid #007bff;
        }
        .method {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
        }
        .get {
            background-color: #28a745;
        }
        .post {
            background-color: #007bff;
        }
        .try-api {
            margin-top: 15px;
        }
        .nav-link.active {
            background-color: #007bff !important;
            color: white !important;
        }
        .tab-content {
            padding: 20px;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 0.25rem 0.25rem;
        }
        .copy-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            z-index: 10;
        }
        .response-container {
            max-height: 300px;
            overflow-y: auto;
        }
        #api-keys-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 2rem;
        }
        /* New styles for endpoint navigation */
        .list-group-item-action:hover {
            background-color: #f0f8ff;
            border-left: 3px solid #007bff;
            transition: all 0.2s ease;
        }
        .endpoints-nav {
            position: sticky;
            top: 20px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }
        html {
            scroll-behavior: smooth;
            scroll-padding-top: 20px;
        }
        .endpoint {
            scroll-margin-top: 20px;
        }
        .response-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        .refresh-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        .refresh-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .refresh-btn svg {
            transition: transform 0.5s;
        }
        .refresh-btn.refreshing svg {
            transform: rotate(360deg);
        }
        .refresh-icon {
            width: 16px;
            height: 16px;
        }

        /* Sidebar Navigation Styles */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            overflow-y: auto;
            z-index: 1000;
            transition: transform 0.3s ease;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-header h3 {
            color: white;
            margin: 0;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .sidebar-nav-item {
            display: block;
            padding: 0.75rem 1.5rem;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
        }

        .sidebar-nav-item:hover {
            background-color: rgba(255,255,255,0.1);
            color: white;
            text-decoration: none;
            border-left-color: #ffd700;
        }

        .sidebar-nav-item.active {
            background-color: rgba(255,255,255,0.15);
            color: white;
            border-left-color: #ffd700;
        }

        .sidebar-nav-item i {
            width: 20px;
            margin-right: 0.5rem;
        }

        .sidebar-nav-subitem {
            padding-left: 3rem;
            font-size: 0.9rem;
            color: rgba(255,255,255,0.8);
        }

        .main-content {
            margin-left: 280px;
            min-height: 100vh;
        }

        .sidebar-toggle {
            display: none;
            position: fixed;
            top: 1rem;
            left: 1rem;
            z-index: 1001;
            background: #667eea;
            color: white;
            border: none;
            padding: 0.5rem;
            border-radius: 4px;
            cursor: pointer;
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .sidebar-toggle {
                display: block;
            }
        }

        /* Smooth scrolling offset for fixed sidebar */
        .endpoint {
            scroll-margin-top: 20px;
        }

        /* Update container for sidebar layout */
        .container {
            max-width: none;
            padding: 2rem;
        }
    </style>
</head>
<body>
    <!-- Mobile Toggle Button -->
    <button class="sidebar-toggle" id="sidebar-toggle">
        <i class="fas fa-bars"></i> ☰
    </button>

    <!-- Sidebar Navigation -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h3>API Documentation</h3>
            <small>Partner API v1.1</small>
        </div>

        <div class="sidebar-nav">
            <a href="#api-keys-section" class="sidebar-nav-item">
                <i>🔐</i> Authentication
            </a>

            <a href="#endpoints" class="sidebar-nav-item">
                <i>📋</i> Endpoints Overview
            </a>

            <a href="#get-products" class="sidebar-nav-item sidebar-nav-subitem">
                <span class="badge bg-success me-2">GET</span> /products
            </a>

            <a href="#create-order" class="sidebar-nav-item sidebar-nav-subitem">
                <span class="badge bg-primary me-2">POST</span> /order
            </a>

            <a href="#get-order" class="sidebar-nav-item sidebar-nav-subitem">
                <span class="badge bg-success me-2">GET</span> /order/{orderId}
            </a>

            <a href="#get-usage" class="sidebar-nav-item sidebar-nav-subitem">
                <span class="badge bg-success me-2">GET</span> /usage/{orderId}
            </a>

            <a href="#get-topup-plans" class="sidebar-nav-item sidebar-nav-subitem">
                <span class="badge bg-success me-2">GET</span> /order/{orderId}/topup-plans
            </a>

            <a href="#create-topup-order" class="sidebar-nav-item sidebar-nav-subitem">
                <span class="badge bg-primary me-2">POST</span> /order/{orderId}/topup
            </a>

            <a href="#error-codes" class="sidebar-nav-item">
                <i>⚠️</i> Error Codes
            </a>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container">
        <header>
            <h1>Partner API Documentation</h1>
            <p class="lead">
                This API allows authorized partners to fetch product data, place and track orders, and view usage information.
            </p>
        </header>

        <section id="api-keys-section">
            <h2>API Authentication</h2>
            <p>All API requests require the following HTTP headers:</p>
            <ul>
                <li><code>Authorization: Bearer {your_api_key}</code></li>
                <li><code>X-Partner-ID: {your_partner_id}</code></li>
            </ul>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="auth-api-key" class="form-label">API Key</label>
                    <input type="text" class="form-control" id="auth-api-key" placeholder="Enter your API key">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="auth-partner-id" class="form-label">Partner ID</label>
                    <input type="text" class="form-control" id="auth-partner-id" placeholder="Enter your Partner ID">
                </div>
            </div>
            <div class="mb-3">
                <label for="base-url" class="form-label">Base URL</label>
                <select class="form-select" id="base-url">
                    <!-- Options will be populated dynamically based on available environments -->
                </select>
            </div>
            <div class="alert alert-info">
                <strong>Note:</strong> You can obtain your API key and Partner ID from the Partner Portal.
            </div>
        </section>

        <section id="endpoints">
            <h2>Endpoints</h2>

            <!-- Endpoints Quick Navigation -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h3 class="h5 mb-0">All Endpoints</h3>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <a href="#get-products" class="list-group-item list-group-item-action">
                            <div class="d-flex justify-content-between align-items-center flex-wrap">
                                <div class="mb-2 mb-md-0">
                                    <span class="badge bg-success me-2">GET</span>
                                    <span class="fw-bold">/products</span>
                                </div>
                                <small class="text-muted">Returns a list of all available products</small>
                            </div>
                        </a>
                        <a href="#create-order" class="list-group-item list-group-item-action">
                            <div class="d-flex justify-content-between align-items-center flex-wrap">
                                <div class="mb-2 mb-md-0">
                                    <span class="badge bg-primary me-2">POST</span>
                                    <span class="fw-bold">/order</span>
                                </div>
                                <small class="text-muted">Places a new order and returns essential summary information</small>
                            </div>
                        </a>
                        <a href="#get-order" class="list-group-item list-group-item-action">
                            <div class="d-flex justify-content-between align-items-center flex-wrap">
                                <div class="mb-2 mb-md-0">
                                    <span class="badge bg-success me-2">GET</span>
                                    <span class="fw-bold">/order/{orderId}</span>
                                </div>
                                <small class="text-muted">Retrieve order details using order ID</small>
                            </div>
                        </a>
                        <a href="#get-usage" class="list-group-item list-group-item-action">
                            <div class="d-flex justify-content-between align-items-center flex-wrap">
                                <div class="mb-2 mb-md-0">
                                    <span class="badge bg-success me-2">GET</span>
                                    <span class="fw-bold">/usage/{orderId}</span>
                                </div>
                                <small class="text-muted">Returns usage details for a specific order</small>
                            </div>
                        </a>
                        <a href="#get-topup-plans" class="list-group-item list-group-item-action">
                            <div class="d-flex justify-content-between align-items-center flex-wrap">
                                <div class="mb-2 mb-md-0">
                                    <span class="badge bg-success me-2">GET</span>
                                    <span class="fw-bold">/order/{orderId}/topup-plans</span>
                                </div>
                                <small class="text-muted">Returns available topup plans for a completed order</small>
                            </div>
                        </a>
                        <a href="#create-topup-order" class="list-group-item list-group-item-action">
                            <div class="d-flex justify-content-between align-items-center flex-wrap">
                                <div class="mb-2 mb-md-0">
                                    <span class="badge bg-primary me-2">POST</span>
                                    <span class="fw-bold">/order/{orderId}/topup</span>
                                </div>
                                <small class="text-muted">Creates a topup order for an existing completed order</small>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Get Products -->
            <div class="endpoint" id="get-products">
                <h3>
                    <span class="method get">GET</span>
                    /products
                </h3>
                <p>Returns a list of all available products.</p>
                <!-- <div class="alert alert-info">
                    <strong>Note:</strong> The <code>countries</code> field contains the specific countries supported by each individual plan, not all countries in the region. Each plan may support different countries even within the same region.
                </div> -->

                <ul class="nav nav-tabs" id="getProductsTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="getProducts-example-tab" data-bs-toggle="tab" data-bs-target="#getProducts-example" type="button" role="tab" aria-controls="getProducts-example" aria-selected="true">Example</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="getProducts-try-tab" data-bs-toggle="tab" data-bs-target="#getProducts-try" type="button" role="tab" aria-controls="getProducts-try" aria-selected="false">Try it</button>
                    </li>
                </ul>
                <div class="tab-content" id="getProductsTabContent">
                    <div class="tab-pane fade show active" id="getProducts-example" role="tabpanel" aria-labelledby="getProducts-example-tab">
                        <div class="position-relative">
                            <button class="btn btn-sm btn-outline-secondary copy-btn" data-code="getProducts-curl">Copy</button>
                            <pre><code class="language-bash" id="getProducts-curl">curl -X GET "https://partner-api.your-domain.com/api/v1/products" \
  -H "Authorization: Bearer your-api-key" \
  -H "X-Partner-ID: your-partner-id"</code></pre>
                        </div>
                        <h5>Response</h5>
                        <div class="position-relative">
                            <button class="btn btn-sm btn-outline-secondary copy-btn" data-code="getProducts-response">Copy</button>
                            <pre><code class="language-json" id="getProducts-response">{
  "success": true,
  "data": {
    "products": [
      {
        "productId": "FXTEND132075",
        "name": "Oceania 3 GB",
        "description": "Key Features:\n• This is a data-only eSIM. It does not come with a phone number.\n• Simply scan the QR code to download and use the eSIM. No other activation or registration steps needed.\n• Validity will start upon downloading the eSIM to your device and connecting to network.\n• One-time prepaid package. No auto-renewals, no contracts. The eSIM is rechargeable and can be topped up with additional data packages.\n• Full data speeds - no daily limits, no throttling. Mobile hotspot is supported.\n• Usable only with eSIM compatible phones and tablets which are not carrier locked. If in doubt, please check the FAQ section.\n• Please start using the eSIM no more than 3 months after purchase.",
        "planInfo": "HTML formatted plan information with key features and usage instructions",
        "instructions": null,
        "price": 29.99,
        "validityDays": 7,
        "countries": ["FJ", "PG", "WS", "TO"],
        "region": ["Oceania"],
        "dataAmount": 3,
        "dataUnit": "GB",
        "customPlanData": null,
        "voiceMin": null,
        "voiceMinUnit": null,
        "sms":100,
        "speed": "Unrestricted",
        "planType": "Fixed",
        "category": "esim_realtime",
        "networkType": "4G/LTE",
        "isVoiceAvailable": false,
        "isSmsAvailable": true,
        "hotspotAvailable": true,
        "topUpAvailable": false,
        "profile": "local",
        "activationPolicy": "Activation upon purchase",
        "startDateEnabled": false,
        "features": []
      }
    ],
    "total": 18663
  }
}</code></pre>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="getProducts-try" role="tabpanel" aria-labelledby="getProducts-try-tab">
                        <div class="try-it-section">
                            <button class="btn btn-primary try-api" data-endpoint="/products" data-method="GET">Send Request</button>
                            <div class="response-container mt-3">
                                <div class="response-header">
                                    <h5>Response</h5>
                                    <button class="btn btn-outline-secondary btn-sm refresh-btn" title="Refresh Response">
                                        <svg class="refresh-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M21 12a9 9 0 11-2.08-5.73" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M21 3v4h-4" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        Refresh
                                    </button>
                                </div>
                                <pre><code class="language-json" id="getProducts-try-response"></code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Create Order -->
            <div class="endpoint" id="create-order">
                <h3>
                    <span class="method post">POST</span>
                    /order
                </h3>
                <p>Places a new order for an eSIM product. Returns essential order information only. Use the Order Details endpoint with the returned orderId to retrieve complete order information including eSIM details, pricing, and fulfillment data.</p>

                <ul class="nav nav-tabs" id="createOrderTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="createOrder-example-tab" data-bs-toggle="tab" data-bs-target="#createOrder-example" type="button" role="tab" aria-controls="createOrder-example" aria-selected="true">Example</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="createOrder-try-tab" data-bs-toggle="tab" data-bs-target="#createOrder-try" type="button" role="tab" aria-controls="createOrder-try" aria-selected="false">Try it</button>
                    </li>
                </ul>
                <div class="tab-content" id="createOrderTabContent">
                    <div class="tab-pane fade show active" id="createOrder-example" role="tabpanel" aria-labelledby="createOrder-example-tab">
                        <div class="position-relative">
                            <button class="btn btn-sm btn-outline-secondary copy-btn" data-code="createOrder-curl">Copy</button>
                            <pre><code class="language-bash" id="createOrder-curl">curl -X POST "https://partner-api.your-domain.com/api/v1/order" \
  -H "Authorization: Bearer your-api-key" \
  -H "X-Partner-ID: your-partner-id" \
  -H "Content-Type: application/json" \
  -d '{
    "productId": "ABCDEF123456",
    "startDate": "2023-11-01"
  }'</code></pre>
                        </div>
                        <h5>Response</h5>
                        <div class="position-relative">
                            <button class="btn btn-sm btn-outline-secondary copy-btn" data-code="createOrder-response">Copy</button>
                            <pre><code class="language-json" id="createOrder-response">{
  "success": true,
  "orderId": "VLZ123456",
  "message": "Order created successfully"
}</code></pre>
                        </div>
                        <div class="alert alert-info mt-3">
                            <strong>Note:</strong> The order creation response contains only essential summary information. To retrieve complete order details including eSIM data, pricing, and fulfillment information, use the <strong>GET /order/{orderId}</strong> endpoint with the returned orderId.
                        </div>
                    </div>
                    <div class="tab-pane fade" id="createOrder-try" role="tabpanel" aria-labelledby="createOrder-try-tab">
                        <div class="try-it-section">
                            <div class="mb-3">
                                <label for="create-order-product-id" class="form-label">Product ID</label>
                                <input type="text" class="form-control" id="create-order-product-id" 
                                       data-param-type="body" 
                                       data-body-key="productId" 
                                       placeholder="Enter Product ID (e.g., ABCDEF123456)">
                            </div>
                            <div class="mb-3">
                                <label for="create-order-start-date" class="form-label">Start Date (Optional)</label>
                                <input type="date" class="form-control" id="create-order-start-date" 
                                       data-param-type="body" 
                                       data-body-key="startDate">
                            </div>
                            <div class="mb-3">
                                <label for="create-order-body" class="form-label">Request Body</label>
                                <textarea class="form-control" id="create-order-body" rows="5">{
  "productId": "",
  "startDate": null
}</textarea>
                            </div>
                            <button class="btn btn-primary try-api" 
                                    data-endpoint="/order" 
                                    data-method="POST" 
                                    data-body-id="create-order-body">Send Request</button>
                            <div class="response-container mt-3">
                                <div class="response-header">
                                    <h5>Response</h5>
                                    <button class="btn btn-outline-secondary btn-sm refresh-btn" title="Refresh Response">
                                        <svg class="refresh-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M21 12a9 9 0 11-2.08-5.73" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M21 3v4h-4" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        Refresh
                                    </button>
                                </div>
                                <pre><code class="language-json" id="createOrder-try-response"></code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Get Order -->
            <div class="endpoint" id="get-order">
                <h3>
                    <span class="method get">GET</span>
                    /order/{orderId}
                </h3>
                <p>Retrieve order details using order ID including eSIM data, pricing, and fulfillment information.</p>
                <ul class="nav nav-tabs" id="getOrderTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="getOrder-example-tab" data-bs-toggle="tab" data-bs-target="#getOrder-example" type="button" role="tab" aria-controls="getOrder-example" aria-selected="true">Example</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="getOrder-try-tab" data-bs-toggle="tab" data-bs-target="#getOrder-try" type="button" role="tab" aria-controls="getOrder-try" aria-selected="false">Try it</button>
                    </li>
                </ul>
                <div class="tab-content" id="getOrderTabContent">
                    <div class="tab-pane fade show active" id="getOrder-example" role="tabpanel" aria-labelledby="getOrder-example-tab">
                        <div class="position-relative">
                            <button class="btn btn-sm btn-outline-secondary copy-btn" data-code="getOrder-curl">Copy</button>
                            <pre><code class="language-bash" id="getOrder-curl">curl -X GET "https://partner-api.your-domain.com/api/v1/order/VLZ123456" \
  -H "Authorization: Bearer your-api-key" \
  -H "X-Partner-ID: your-partner-id"</code></pre>
                        </div>
                        <h5>Response</h5>
                        <div class="position-relative">
                            <button class="btn btn-sm btn-outline-secondary copy-btn" data-code="getOrder-response">Copy</button>
                            <pre><code class="language-json" id="getOrder-response">{
  "success": true,
  "data": {
    "orderId": "VLZ123456",
    "status": "completed",
    "product": {
      "productId": "ABCDEF123456",
      "name": "Europe 5GB / 30 Days"
    },
    "orderTotal": 29.99,
    "quantity": 1,
    "startDate": "2023-11-01",
    "expiryDate": "2023-12-01",
    "iccid": "8991000123456789012",
    "smdpAddress": "trl.prod.ondemandconnectivity.com",
    "accessPointName": "mbb",
    "lpaString": "LPA:1$trl.prod.ondemandconnectivity.com$AAA22",
    "activationCode": "LPA:1$smdp.example.com$123456789-abcdef-123456",
    "status": "completed",
    "top_up": "Available",
    "qrCodeUrl": "https://example.com/qr/VLZ123456.png",
    "walletAuthTransactionId": "sksgdnsdyk1234567890",
    "createdAt": "2023-10-25T14:30:45Z"
  }
}</code></pre>
                        </div>
                         <div class="alert alert-info">
                <strong>Note:</strong> Some orders may take 1–5 minutes to reflect complete data, as they start in a pending status. Once the order is marked as completed, full details will be available. Try refreshing the request to see the latest status.
                 </div>
                    </div>
                    <div class="tab-pane fade" id="getOrder-try" role="tabpanel" aria-labelledby="getOrder-try-tab">
                        <div class="try-it-section">
                            <div class="mb-3">
                                <label for="order-id" class="form-label">Order ID</label>
                                <input type="text" class="form-control" id="order-id"
                                       data-param-type="path"
                                       data-param-name="orderId"
                                       placeholder="Enter Order ID (e.g., VLZ123456)">
                            </div>
                            <button class="btn btn-primary try-api" data-endpoint="/order/{orderId}" data-method="GET">Send Request</button>
                            <div class="response-container mt-3">
                                <div class="response-header">
                                    <h5>Response</h5>
                                    <button class="btn btn-outline-secondary btn-sm refresh-btn" title="Refresh Response">
                                        <svg class="refresh-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M21 12a9 9 0 11-2.08-5.73" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M21 3v4h-4" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        Refresh
                                    </button>
                                </div>
                                <pre><code class="language-json" id="getOrder-try-response"></code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Get Usage -->
            <div class="endpoint" id="get-usage">
                <h3>
                    <span class="method get">GET</span>
                    /usage/{orderId}
                </h3>
                <p>Returns usage details for a specific order.</p>

                <ul class="nav nav-tabs" id="getUsageTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="getUsage-example-tab" data-bs-toggle="tab" data-bs-target="#getUsage-example" type="button" role="tab" aria-controls="getUsage-example" aria-selected="true">Example</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="getUsage-try-tab" data-bs-toggle="tab" data-bs-target="#getUsage-try" type="button" role="tab" aria-controls="getUsage-try" aria-selected="false">Try it</button>
                    </li>
                </ul>
                <div class="tab-content" id="getUsageTabContent">
                    <div class="tab-pane fade show active" id="getUsage-example" role="tabpanel" aria-labelledby="getUsage-example-tab">
                        <div class="position-relative">
                            <button class="btn btn-sm btn-outline-secondary copy-btn" data-code="getUsage-curl">Copy</button>
                            <pre><code class="language-bash" id="getUsage-curl">curl -X GET "https://partner-api.your-domain.com/api/v1/usage/VLZ123456" \
  -H "Authorization: Bearer your-api-key" \
  -H "X-Partner-ID: your-partner-id"</code></pre>
                        </div>
                        <h5>Response</h5>
                        <div class="position-relative">
                            <button class="btn btn-sm btn-outline-secondary copy-btn" data-code="getUsage-response">Copy</button>
                            <pre><code class="language-json" id="getUsage-response">{
  "success": true,
  "data": {
    "orderId": "VLZ123456",
    "dataUsage": 2147483648,
    "dataAllowance": 5368709120,
    "status": "Active",
    "expiryDate": "2023-12-01T00:00:00Z",
    "lastUpdated": "2023-11-15T09:45:22Z",
    "message": "Usage data retrieved successfully",
    "isRealtime": true,
    "fromCache": false
  }
}</code></pre>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="getUsage-try" role="tabpanel" aria-labelledby="getUsage-try-tab">
                        <div class="try-it-section">
                            <div class="mb-3">
                                <label for="usage-order-id" class="form-label">Order ID</label>
                                <input type="text" class="form-control" id="usage-order-id" 
                                       data-param-type="path" 
                                       data-param-name="orderId" 
                                       placeholder="Enter Order ID (e.g., VLZ123456)">
                            </div>
                            <button class="btn btn-primary try-api" data-endpoint="/usage/{orderId}" data-method="GET">Send Request</button>
                            <div class="response-container mt-3">
                                <div class="response-header">
                                    <h5>Response</h5>
                                    <button class="btn btn-outline-secondary btn-sm refresh-btn" title="Refresh Response">
                                        <svg class="refresh-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M21 12a9 9 0 11-2.08-5.73" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M21 3v4h-4" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        Refresh
                                    </button>
                                </div>
                                <pre><code class="language-json" id="getUsage-try-response"></code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Get Topup Plans -->
            <div class="endpoint" id="get-topup-plans">
                <h3>
                    <span class="method get">GET</span>
                    /order/{orderId}/topup-plans
                </h3>
                <p>Returns available topup plans for a completed order. Topup functionality is available for for all the orders that support topup.</p>

                <ul class="nav nav-tabs" id="getTopupPlansTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="getTopupPlans-example-tab" data-bs-toggle="tab" data-bs-target="#getTopupPlans-example" type="button" role="tab" aria-controls="getTopupPlans-example" aria-selected="true">Example</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="getTopupPlans-try-tab" data-bs-toggle="tab" data-bs-target="#getTopupPlans-try" type="button" role="tab" aria-controls="getTopupPlans-try" aria-selected="false">Try it</button>
                    </li>
                </ul>
                <div class="tab-content" id="getTopupPlansTabContent">
                    <div class="tab-pane fade show active" id="getTopupPlans-example" role="tabpanel" aria-labelledby="getTopupPlans-example-tab">
                        <div class="position-relative">
                            <button class="btn btn-sm btn-outline-secondary copy-btn" data-code="getTopupPlans-curl">Copy</button>
                            <pre><code class="language-bash" id="getTopupPlans-curl">curl -X GET "https://partner-api.your-domain.com/api/v1/order/VLZ123456/topup-plans" \
  -H "Authorization: Bearer your-api-key" \
  -H "X-Partner-ID: your-partner-id"</code></pre>
                        </div>
                        <h5>Response</h5>
                        <div class="position-relative">
                            <button class="btn btn-sm btn-outline-secondary copy-btn" data-code="getTopupPlans-response">Copy</button>
                            <pre><code class="language-json" id="getTopupPlans-response">{
  "success": true,
  "data": {
    "originalOrder": {
      "orderId": "VLZ123456",
      "productId": "ABCDEF123456",
      "name": "Europe 5GB / 30 Days",
      "iccid": "8991000123456789012"
    },
    "topupPlans": [
      {
        "productId": "ADDON001",
        "name": "Europe 1GB Addon",
        "description": "Additional 1GB data for Europe",
        "planInfo": "HTML formatted plan information with key features and usage instructions",
        "price": 9.99,
        "validityDays": 30,
        "dataAmount": 1,
        "dataUnit": "GB",
        "customPlanData": "1gb addon",
        "voiceMin": null,
        "voiceMinUnit": "Min",
        "sms":null,
        "speed": "Unrestricted",
        "planType": "Addon",
        "category": "esim_addon",
        "networkType": "4G/LTE",
        "countries": ["FR", "DE", "IT", "ES", "NL"],
        "region": ["Europe"],
        "features": ["5G Support"],
        "isVoiceAvailable": false,
        "isSmsAvailable": false,
        "hotspotAvailable": true,
        "profile": "roaming",
      }
    ]
  }
}</code></pre>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="getTopupPlans-try" role="tabpanel" aria-labelledby="getTopupPlans-try-tab">
                        <div class="try-it-section">
                            <div class="mb-3">
                                <label for="topup-plans-order-id" class="form-label">Order ID</label>
                                <input type="text" class="form-control" id="topup-plans-order-id"
                                       data-param-type="path"
                                       data-param-name="orderId"
                                       placeholder="Enter Order ID (e.g., VLZ123456)">
                            </div>
                            <button class="btn btn-primary try-api" data-endpoint="/order/{orderId}/topup-plans" data-method="GET">Send Request</button>
                            <div class="response-container mt-3">
                                <div class="response-header">
                                    <h5>Response</h5>
                                    <button class="btn btn-outline-secondary btn-sm refresh-btn" title="Refresh Response">
                                        <svg class="refresh-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M21 12a9 9 0 11-2.08-5.73" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M21 3v4h-4" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        Refresh
                                    </button>
                                </div>
                                <pre><code class="language-json" id="getTopupPlans-try-response"></code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Create Topup Order -->
            <div class="endpoint" id="create-topup-order">
                <h3>
                    <span class="method post">POST</span>
                    /order/{orderId}/topup
                </h3>
                <p>Creates a topup order for an existing completed order. This adds additional data/services to an existing eSIM.</p>

                <ul class="nav nav-tabs" id="createTopupOrderTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="createTopupOrder-example-tab" data-bs-toggle="tab" data-bs-target="#createTopupOrder-example" type="button" role="tab" aria-controls="createTopupOrder-example" aria-selected="true">Example</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="createTopupOrder-try-tab" data-bs-toggle="tab" data-bs-target="#createTopupOrder-try" type="button" role="tab" aria-controls="createTopupOrder-try" aria-selected="false">Try it</button>
                    </li>
                </ul>
                <div class="tab-content" id="createTopupOrderTabContent">
                    <div class="tab-pane fade show active" id="createTopupOrder-example" role="tabpanel" aria-labelledby="createTopupOrder-example-tab">
                        <div class="position-relative">
                            <button class="btn btn-sm btn-outline-secondary copy-btn" data-code="createTopupOrder-curl">Copy</button>
                            <pre><code class="language-bash" id="createTopupOrder-curl">curl -X POST "https://partner-api.your-domain.com/api/v1/order/VLZ123456/topup" \
  -H "Authorization: Bearer your-api-key" \
  -H "X-Partner-ID: your-partner-id" \
  -H "Content-Type: application/json" \
  -d '{
    "productId": "ADDON001"
  }'</code></pre>
                        </div>
                        <h5>Response</h5>
                        <div class="position-relative">
                            <button class="btn btn-sm btn-outline-secondary copy-btn" data-code="createTopupOrder-response">Copy</button>
                            <pre><code class="language-json" id="createTopupOrder-response">{
  "success": true,
  "data": {
    "topupOrderId": "VLZ789012",
    "originalOrderId": "VLZ123456",
    "status": "completed",
    "message": "Topup order created successfully"
  }
}</code></pre>
                        </div>
                        <div class="alert alert-info mt-3">
                            <strong>Note:</strong> Topup orders are processed immediately and will show a status of "completed" upon successful creation. The topup data/services are added to the existing eSIM associated with the original order.
                        </div>
                    </div>
                    <div class="tab-pane fade" id="createTopupOrder-try" role="tabpanel" aria-labelledby="createTopupOrder-try-tab">
                        <div class="try-it-section">
                            <div class="mb-3">
                                <label for="create-topup-order-id" class="form-label">Original Order ID</label>
                                <input type="text" class="form-control" id="create-topup-order-id"
                                       data-param-type="path"
                                       data-param-name="orderId"
                                       placeholder="Enter Original Order ID (e.g., VLZ123456)">
                            </div>
                            <div class="mb-3">
                                <label for="create-topup-product-id" class="form-label">Topup Product ID</label>
                                <input type="text" class="form-control" id="create-topup-product-id"
                                       data-param-type="body"
                                       data-body-key="productId"
                                       placeholder="Enter Topup Product ID (e.g., ADDON001)">
                            </div>
                            <div class="mb-3">
                                <label for="create-topup-body" class="form-label">Request Body</label>
                                <textarea class="form-control" id="create-topup-body" rows="3">{
  "productId": ""
}</textarea>
                            </div>
                            <button class="btn btn-primary try-api"
                                    data-endpoint="/order/{orderId}/topup"
                                    data-method="POST"
                                    data-body-id="create-topup-body">Send Request</button>
                            <div class="response-container mt-3">
                                <div class="response-header">
                                    <h5>Response</h5>
                                    <button class="btn btn-outline-secondary btn-sm refresh-btn" title="Refresh Response">
                                        <svg class="refresh-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M21 12a9 9 0 11-2.08-5.73" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M21 3v4h-4" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        Refresh
                                    </button>
                                </div>
                                <pre><code class="language-json" id="createTopupOrder-try-response"></code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="error-codes">
            <h2>Error Codes</h2>
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Code</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>400 Bad Request</td>
                        <td>The request was malformed or contained invalid parameters</td>
                    </tr>
                    <tr>
                        <td>400 Invalid Order ID</td>
                        <td>Order ID contains invalid characters</td>
                    </tr>
                    <tr>
                        <td>400 Order Not Completed</td>
                        <td>Topup is only available for completed orders</td>
                    </tr>
                    <tr>
                        <td>400 Topup Not Supported</td>
                        <td>This plan does not support topup</td>
                    </tr>
                    <tr>
                        <td>400 Provider Not Supported</td>
                        <td>This provider does not support topup</td
                    </tr>
                    <tr>
                        <td>400 Incompatible Provider</td>
                        <td>Topup plan must be from the same provider as the original order</td>
                    </tr>
                    <tr>
                        <td>400 Incompatible Network</td>
                        <td>Topup plan must be from the same network as the original order</td>
                    </tr>
                    <tr>
                        <td>400 Invalid Topup Plan</td>
                        <td>Only addon plans can be used for topup</td>
                    </tr>
                    <tr>
                        <td>400 Network Not Found</td>
                        <td>Cannot determine network for topup plans</td>
                    </tr>
                    <tr>
                        <td>400 ICCID Not Found</td>
                        <td>Cannot find ICCID for plan</td>
                    </tr>
                    <tr>
                        <td>400 Insufficient Balance</td>
                        <td>Insufficient wallet balance for the topup order</td>
                    </tr>
                    <tr>
                        <td>401 Unauthorized</td>
                        <td>Invalid API key or partner ID</td>
                    </tr>
                    <tr>
                        <td>403 Access Denied</td>
                        <td>You do not have access to this order</td>
                    </tr>
                    <tr>
                        <td>404 Order Not Found</td>
                        <td>Order not found</td>
                    </tr>
                    <tr>
                        <td>404 Product Not Found</td>
                        <td>Topup product not found or not available</td>
                    </tr>
                    <tr>
                        <td>404 Not Found</td>
                        <td>Resource not found</td>
                    </tr>
                    <tr>
                        <td>500 Provider Error</td>
                        <td>Failed to fetch available topup plans from provider</td>
                    </tr>
                    <tr>
                        <td>500 Topup Order Creation Failed</td>
                        <td>Failed to create topup order</td>
                    </tr>
                    <tr>
                        <td>500 Internal Server Error</td>
                        <td>General server error</td>
                    </tr>
                </tbody>
            </table>
        </section>

        <footer class="mt-5 pt-3 border-top text-muted">
            <p>&copy; 2025 eSIM Platform. All rights reserved.</p>
        </footer>
        </div>
    </div>

    <!-- Using our local proxy to avoid CSP issues -->
    <script src="/api/config/scripts/bootstrap.min.js"></script>
    <script src="/api/config/scripts/highlight.min.js"></script>
    <script src="/api/config/scripts/bash.min.js"></script>
    <script src="/api/config/scripts/json.min.js"></script>
    
    <!-- Load environment variables -->
    <script src="/api/config/env.js"></script>

    <!-- Load the main script file with all functionality -->
    <script src="/api/config/scripts/doc-main.js" defer></script>
    
    <!-- Script for enhanced endpoint navigation -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Enhance endpoint navigation links with smooth scrolling
            document.querySelectorAll('.list-group-item-action').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    const targetId = this.getAttribute('href');
                    const targetElement = document.querySelector(targetId);
                    
                    if (targetElement) {
                        // Smooth scroll to target
                        targetElement.scrollIntoView({
                            behavior: 'smooth'
                        });
                        
                        // Highlight the target section briefly
                        targetElement.classList.add('bg-light');
                        setTimeout(() => {
                            targetElement.classList.remove('bg-light');
                        }, 1500);
                        
                        // Update URL without full page reload
                        history.pushState(null, null, targetId);
                    }
                });
            });
            
            // Check if URL has a hash on page load and scroll to it
            if (location.hash) {
                const targetElement = document.querySelector(location.hash);
                if (targetElement) {
                    setTimeout(() => {
                        targetElement.scrollIntoView({
                            behavior: 'smooth'
                        });
                        
                        // Highlight the target section briefly
                        targetElement.classList.add('bg-light');
                        setTimeout(() => {
                            targetElement.classList.remove('bg-light');
                        }, 1500);
                    }, 500);
                }
            }

            // Set up refresh functionality for response containers
            document.querySelectorAll('.refresh-btn').forEach(button => {
                button.addEventListener('click', async function() {
                    const responseContainer = button.closest('.response-container');
                    const responseElement = responseContainer.querySelector('code');
                    const trySection = button.closest('.try-it-section');
                    const tryButton = trySection.querySelector('.try-api');

                    // Disable refresh button and add spinning animation
                    button.disabled = true;
                    button.classList.add('refreshing');

                    try {
                        // Get the current response data
                        const currentResponse = JSON.parse(responseElement.textContent || '{}');
                        
                        if (currentResponse?.data?.orderId) {
                            // Get the order ID from the current response
                            const orderId = currentResponse.data.orderId;

                            // Get API credentials
                            const apiKey = document.getElementById('auth-api-key').value;
                            const partnerId = document.getElementById('auth-partner-id').value;
                            const baseUrl = document.getElementById('base-url').value;

                            if (!apiKey || !partnerId) {
                                alert('Please enter your API Key and Partner ID in the Authentication section above.');
                                return;
                            }

                            // Determine which endpoint to call based on the current section
                            let endpoint;
                            let endpointType;
                            const usageSection = button.closest('#get-usage');
                            const orderSection = button.closest('#get-order');

                            if (usageSection) {
                                endpoint = `${baseUrl}/usage/${orderId}`;
                                endpointType = 'usage';
                            } else if (orderSection) {
                                endpoint = `${baseUrl}/order/${orderId}`;
                                endpointType = 'order';
                            } else {
                                // Default to order endpoint for backward compatibility
                                endpoint = `${baseUrl}/order/${orderId}`;
                                endpointType = 'order (default)';
                            }


                            // Make request to the appropriate endpoint
                            const response = await fetch(endpoint, {
                                headers: {
                                    'Authorization': `Bearer ${apiKey}`,
                                    'X-Partner-ID': partnerId
                                }
                            });

                            let responseData;
                            const contentType = response.headers.get('content-type');
                            if (contentType && contentType.includes('application/json')) {
                                responseData = await response.json();
                            } else {
                                responseData = await response.text();
                            }

                            // Update the response display
                            responseElement.textContent = JSON.stringify(responseData, null, 2);
                            hljs.highlightElement(responseElement);

                            // Add status display
                            let statusElement = responseContainer.querySelector('.response-status');
                            if (!statusElement) {
                                statusElement = document.createElement('div');
                                statusElement.className = 'response-status mb-2';
                                responseContainer.insertBefore(statusElement, responseContainer.firstChild);
                            }

                            const isSuccess = response.status >= 200 && response.status < 300;
                            const statusClass = isSuccess ? 'text-success' : 'text-danger';
                            statusElement.innerHTML = `
                                <strong class="${statusClass}">Status: ${response.status} ${response.statusText}</strong>
                            `;

                            // If response indicates we should refresh again, set up auto-refresh
                            if (responseData?.data?.shouldRefresh) {
                                const interval = responseData.data.refreshInterval || 10000;
                                const maxAttempts = responseData.data.maxRefreshAttempts || 30;
                                let attempts = 0;

                                const autoRefresh = setInterval(() => {
                                    attempts++;
                                    if (attempts >= maxAttempts) {
                                        clearInterval(autoRefresh);
                                        button.disabled = false;
                                        button.classList.remove('refreshing');
                                        return;
                                    }
                                    button.click();
                                }, interval);

                                // Store the interval ID to clear it if needed
                                button.dataset.refreshInterval = autoRefresh;
                            }
                        }
                    } catch (error) {
                        console.error('Error refreshing response:', error);
                        responseElement.textContent = JSON.stringify({
                            success: false,
                            error: {
                                message: 'Failed to refresh response',
                                details: error.message
                            }
                        }, null, 2);
                        hljs.highlightElement(responseElement);
                    } finally {
                        // Re-enable refresh button and remove spinning animation
                        button.disabled = false;
                        button.classList.remove('refreshing');
                    }
                });
            });

            // Clear auto-refresh when switching tabs
            document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
                tab.addEventListener('shown.bs.tab', function() {
                    document.querySelectorAll('.refresh-btn').forEach(button => {
                        if (button.dataset.refreshInterval) {
                            clearInterval(parseInt(button.dataset.refreshInterval));
                            delete button.dataset.refreshInterval;
                            button.disabled = false;
                            button.classList.remove('refreshing');
                        }
                    });
                });
            });

            // Sidebar functionality
            function toggleSidebar() {
                const sidebar = document.getElementById('sidebar');
                sidebar.classList.toggle('show');
            }

            function setActiveNav(element) {
                // Remove active class from all nav items
                document.querySelectorAll('.sidebar-nav-item').forEach(item => {
                    item.classList.remove('active');
                });

                // Add active class to clicked item
                element.classList.add('active');

                // Close sidebar on mobile after selection
                if (window.innerWidth <= 768) {
                    setTimeout(() => {
                        document.getElementById('sidebar').classList.remove('show');
                    }, 300);
                }
            }

            // Auto-highlight current section in sidebar
            function highlightCurrentSection() {
                const sections = document.querySelectorAll('section[id], .endpoint[id]');
                const navItems = document.querySelectorAll('.sidebar-nav-item[href^="#"]');

                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    if (window.pageYOffset >= sectionTop - 100) {
                        current = section.getAttribute('id');
                    }
                });

                navItems.forEach(item => {
                    item.classList.remove('active');
                    if (item.getAttribute('href') === '#' + current) {
                        item.classList.add('active');
                    }
                });
            }

            // Listen for scroll events to highlight current section
            window.addEventListener('scroll', highlightCurrentSection);

            // Initial highlight
            highlightCurrentSection();

            // Smooth scrolling for sidebar links
            document.querySelectorAll('.sidebar-nav-item[href^="#"]').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);

                    if (targetElement) {
                        // Smooth scroll to target with offset for fixed sidebar
                        const offsetTop = targetElement.offsetTop - 20;
                        window.scrollTo({
                            top: offsetTop,
                            behavior: 'smooth'
                        });

                        // Set active nav item
                        setActiveNav(this);

                        // Highlight the target section briefly
                        targetElement.classList.add('bg-light');
                        setTimeout(() => {
                            targetElement.classList.remove('bg-light');
                        }, 1500);
                    }
                });
            });

            // Add event listener for sidebar toggle button
            document.getElementById('sidebar-toggle').addEventListener('click', toggleSidebar);

            // Note: Request body updates are handled by doc-main.js to prevent duplicate functionality

            // Note: Try-API button functionality is handled by doc-main.js to prevent duplicate event listeners

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(e) {
                const sidebar = document.getElementById('sidebar');
                const toggle = document.querySelector('.sidebar-toggle');

                if (window.innerWidth <= 768 &&
                    !sidebar.contains(e.target) &&
                    !toggle.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            });
        });
    </script>
</body>
</html>