const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../middleware/auth');
const models = require('../models');
const { Op } = require('sequelize');
const optimizedCache = require('../utils/optimizedCacheManager');

router.get('/stats', isAuthenticated, async (req, res) => {
    try {
        const userId = req.user.id;
        const startTime = Date.now();

        // Try to get cached dashboard data first
        const cachedStats = await optimizedCache.get('dashboard', userId);
        if (cachedStats) {
            console.log(`📊 Dashboard cache HIT for user ${userId} (${Date.now() - startTime}ms)`);
            return res.json(cachedStats);
        }

        console.log(`📊 Dashboard cache MISS for user ${userId}, generating fresh data...`);

        // Get wallet balance
        const wallet = await models.Wallet.findOne({
            where: { userId }
        });

        // Get total orders
        const totalOrders = await models.Order.count({
            where: { userId }
        });

        // Get total available eSIM plans
        const totalPlans = await models.EsimPlan.count({
            where: { 
                status: 'visible',
                isActive: true
            }
        });

        // Get orders in the last 30 days
        const last30DaysOrders = await models.Order.count({
            where: {
                userId,
                createdAt: {
                    [Op.gte]: new Date(new Date() - 30 * 24 * 60 * 60 * 1000)
                }
            }
        });

        // Get all order trends by month
        const allMonthlyOrders = await models.Order.findAll({
            where: { userId },
            attributes: [
                [models.sequelize.fn('DATE_FORMAT', models.sequelize.col('createdAt'), '%Y-%m-01'), 'month'],
                [models.sequelize.fn('COUNT', '*'), 'count']
            ],
            group: [models.sequelize.fn('DATE_FORMAT', models.sequelize.col('createdAt'), '%Y-%m-01')],
            order: [[models.sequelize.fn('DATE_FORMAT', models.sequelize.col('createdAt'), '%Y-%m-01'), 'ASC']]
        });

        // Fill in all 12 months of current year with zero counts for missing months
        const monthlyTrends = [];
        const currentYear = new Date().getFullYear();

        for (let month = 0; month < 12; month++) {
            const date = new Date(currentYear, month, 1);
            const monthKey = date.toISOString().slice(0, 7) + '-01';
            const existingData = allMonthlyOrders.find(
                order => order.getDataValue('month') === monthKey
            );
            monthlyTrends.push({
                month: monthKey,
                count: existingData ? parseInt(existingData.getDataValue('count')) : 0
            });
        }

        // Get recent orders
        const recentOrders = await models.Order.findAll({
            attributes: ['id', 'parentOrderId', 'quantity', 'orderTotal', 'status', 'createdAt'],
            where: { userId },
            include: [{
                model: models.EsimPlan,
                as: 'plan',
                attributes: ['name', 'planData', 'planDataUnit', 'validityDays', 'planType','customPlanData','planCategory']
            }],
            order: [['createdAt', 'DESC']],
            limit: 5
        });

        const dashboardData = {
            walletBalance: wallet ? parseFloat(wallet.balance) : 0,
            totalOrders,
            totalPlans,
            last30DaysOrders,
            monthlyTrends,
            recentOrders: recentOrders.map(order => ({
                id: order.id,
                parentOrderId: order.parentOrderId, // Include parentOrderId for topup order identification
                planName: order.plan.name,
                planDetails: `${order.plan.planData} ${order.plan.planDataUnit} / ${order.plan.validityDays} Days`,
                quantity: order.quantity || 1,
                amount: parseFloat(order.orderTotal),
                status: order.status,
                planType: order.plan.planType,
                customPlanData: order.plan.customPlanData,
                planData: order.plan.planData,
                planDataUnit: order.plan.planDataUnit,
                validityDays: order.plan.validityDays,
                planCategory: order.plan.planCategory,
                createdAt: order.createdAt
            }))
        };

        // Cache the dashboard data for 5 minutes
        await optimizedCache.set('dashboard', userId, dashboardData);

        const responseTime = Date.now() - startTime;
        console.log(`📊 Generated fresh dashboard data in ${responseTime}ms`);

        res.json(dashboardData);
    } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        res.status(500).json({ message: 'Failed to fetch dashboard statistics' });
    }
});

module.exports = router;
