import React, { useState, useEffect, useRef } from 'react';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card'; // Import Card components
import { Search, Filter, ChevronLeft, ChevronRight, Download, Eye, RefreshCw } from 'lucide-react';
import { useToast } from "@/components/ui/use-toast";
import api from '@/lib/axios';
import { format } from 'date-fns';
import { useNavigate } from 'react-router-dom';
import { ShoppingBag } from 'lucide-react';

const getStatusColor = (status) => {
    const colors = {
        'completed': 'bg-green-100 text-green-800',
        'expired': 'bg-gray-100 text-gray-800',
        'pending': 'bg-yellow-100 text-yellow-800',
        'failed': 'bg-red-100 text-red-800',
        'cancelled': 'bg-red-100 text-red-800'
    };
    return colors[status.toLowerCase()] || 'bg-gray-100 text-gray-800';
};

export default function Orders() {
    const [orders, setOrders] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [loading, setLoading] = useState(true);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [totalCount, setTotalCount] = useState(0);
    const [limit] = useState(10);
    const [isExporting, setIsExporting] = useState(false);
    const navigate = useNavigate();
    const { toast } = useToast();
    const searchTimeout = useRef(null);
    

    useEffect(() => {
        fetchOrders();
    }, [currentPage]);

    useEffect(() => {
        // Clear existing timeout
        if (searchTimeout.current) {
            clearTimeout(searchTimeout.current);
        }

        // Set new timeout for search
        searchTimeout.current = setTimeout(() => {
            setCurrentPage(1); // Reset to first page when searching
            fetchOrders();
        }, 300); // Debounce search for 300ms

        // Cleanup timeout on unmount
        return () => {
            if (searchTimeout.current) {
                clearTimeout(searchTimeout.current);
            }
        };
    }, [searchTerm]);

    const fetchOrders = async () => {
        try {
            setLoading(true);
            const response = await api.get('/api/orders/admin/all', {
                params: {
                    page: currentPage,
                    limit,
                    search: searchTerm
                }
            });
            setOrders(response.data.orders);
            setTotalPages(response.data.totalPages);
            setTotalCount(response.data.totalCount || 0);
        } catch (error) {
            console.error('Error fetching orders:', error);
            toast({
                title: "Error",
                description: "Failed to fetch orders",
                variant: "destructive"
            });
        } finally {
            setLoading(false);
        }
    };

    const exportOrders = async () => {
        setIsExporting(true);
        try {
            // Fetch all orders for export
            const response = await api.get('/api/orders/admin/export');
            const orders = response.data.orders;

            // Prepare CSV data
            const csvData = [
                ['Order ID', 'Customer', 'Email', 'Plan', 'ICCID', 'Quantity', 'Amount', 'Status', 'Date'],
                ...orders.map(order => [
                    order.id,
                    order.customer,
                    order.customerEmail,
                    order.plan.name,
                    order.iccid,
                    order.quantity,
                    parseFloat(order.orderTotal).toFixed(2),
                    order.status,
                    format(new Date(order.createdAt), 'MMM dd, yyyy')
                ])
            ];

            // Convert to CSV string
            const csvString = csvData.map(row => row.map(cell =>
                typeof cell === 'string' && cell.includes(',') ? `"${cell}"` : cell
            ).join(',')).join('\n');

            // Create and download file
            const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `orders_${format(new Date(), 'yyyy-MM-dd')}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            toast({
                title: "Success",
                description: "Orders exported successfully",
                variant: "success"
            });
        } catch (error) {
            console.error('Error exporting orders:', error);
            toast({
                title: "Error",
                description: "Failed to export orders",
                variant: "destructive"
            });
        } finally {
            setIsExporting(false);
        }
    };

    return (
        <Card className="h-full flex flex-col">
            <CardHeader>
                <div className="flex justify-between items-center py-8 px-6 rounded-t-lg bg-gradient-to-r from-blue-800 to-blue-600">
                    <div className="flex items-center gap-2">
                        <ShoppingBag className="w-8 h-8 text-white" />
                        <div>
                            <CardTitle className="text-white">eSIM Orders</CardTitle>
                            <CardDescription className="text-white">
                                Manage and track all eSIM orders
                            </CardDescription>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={fetchOrders}
                        className="bg-white text-blue-600 border-white hover:bg-gray-100"
                        disabled={loading}
                    >
                        <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                        Refresh
                    </Button>
                    <Button
                        variant="outline"
                        className="flex items-center gap-2 border-purple-500 bg-purple-500 text-white"
                        onClick={exportOrders}
                        disabled={isExporting}
                    >
                        {isExporting ? (
                            <>
                                <span className="animate-spin">⏳</span>
                                Exporting...
                            </>
                        ) : (
                            <>
                                <Download className="w-4 h-4" />
                                Export Orders
                            </>
                        )}
                    </Button>
                    <div>
                    <Badge className="py-2 px-4 text-sm bg-white text-primary hover:bg-gray-100">
                            {totalCount} order{totalCount !== 1 ? 's' : ''}
                    </Badge>
                    </div>
                    </div>
                </div>
            </CardHeader>

            <CardContent className="flex-1 flex flex-col gap-6">
                <div className="flex flex-col sm:flex-row gap-4">
                    <div className="relative flex-1">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <Input
                            placeholder="Search by order ID, customer name, email, plan or status..."
                            className="pl-10 w-full"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm border flex-1 flex flex-col min-h-[500px]">
                    <div className="overflow-x-auto flex-1">
                        <Table>
                            <TableHeader>
                                <TableRow className="bg-gradient-to-r from-slate-100 to-gray-100">
                                    <TableHead className="w-[100px]">#Order ID</TableHead>
                                    <TableHead className="w-[200px]">Customer</TableHead>
                                    <TableHead className="w-[200px]">Plan</TableHead>
                                    <TableHead className="w-[200px]">ICCID</TableHead>
                                    <TableHead className="w-[100px]">Quantity</TableHead>
                                    <TableHead className="w-[100px]">Amount</TableHead>
                                    <TableHead className="w-[100px]">Status</TableHead>
                                    <TableHead className="w-[200px]">Date</TableHead>
                                    <TableHead className="w-[100px]">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {loading ? (
                                    <TableRow>
                                        <TableCell colSpan={9} className="text-center py-8">
                                            <div className="flex items-center justify-center gap-2">
                                                <span className="animate-spin">⏳</span>
                                                Loading orders...
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ) : orders.length === 0 ? (
                                    <TableRow>
                                        <TableCell colSpan={9} className="text-center py-8">
                                            {searchTerm ? 'No orders found matching your search' : 'No orders found'}
                                        </TableCell>
                                    </TableRow>
                                ) : (
                                    orders.map((order) => (
                                        <TableRow
                                            key={order.id}
                                            className="cursor-pointer hover:bg-gray-50"
                                        >
                                            <TableCell className="font-medium">#{order.id}</TableCell>
                                            <TableCell>
                                                <div>
                                                    <div className="font-medium">{order.customer}</div>
                                                </div>
                                            </TableCell>
                                            <TableCell>{order.plan?.name}</TableCell>
                                            <TableCell className="font-mono text-sm">{order.iccid}</TableCell>
                                            <TableCell>{order.quantity}</TableCell>
                                            <TableCell>${parseFloat(order.orderTotal).toFixed(2)}</TableCell>
                                            <TableCell>
                                                <Badge variant="secondary" className={getStatusColor(order.status)}>
                                                    {order.status}
                                                </Badge>
                                            </TableCell>
                                            <TableCell>{format(new Date(order.createdAt), 'MMM dd, yyyy')}</TableCell>
                                            <TableCell>
                                                <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => navigate(`/admin/orders/${order.id}`)}
                                                className="bg-blue-600 flex items-center gap-1 hover:bg-blue-700 hover:text-white transition-colors"
                                            >
                                                <Eye className="h-3 w-3" />
                                                View
                                            </Button>
                                            </TableCell>
                                        </TableRow>
                                    ))
                                )}
                            </TableBody>
                        </Table>
                    </div>

                    {/* Pagination */}
                    {!loading && orders.length > 0 && (
                        <div className="flex items-center justify-between px-4 py-4 border-t">
                            <div className="flex-1 text-sm text-gray-500">
                                Showing {((currentPage - 1) * limit) + 1}-{Math.min(currentPage * limit, totalCount)} of {totalCount} orders
                            </div>
                            <div className="flex items-center gap-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                                    disabled={currentPage === 1}
                                >
                                    <ChevronLeft className="h-4 w-4" />
                                    Previous
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                                    disabled={currentPage === totalPages}
                                >
                                    Next
                                    <ChevronRight className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    )}
                </div>
            </CardContent>
        </Card>
    );
}