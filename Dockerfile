FROM node:18

# Create app directory
WORKDIR /usr/src/app

# Copy dependency files
COPY server/package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy app source
COPY server/ ./

# Expose the port
EXPOSE 3000

# Start the app with conservative memory settings for 3.7GB system
# Set heap to 2GB (more conservative) and enable aggressive garbage collection
CMD ["node", "--max-old-space-size=2048", "--expose-gc", "--optimize-for-size", "--max-semi-space-size=64", "--gc-interval=100", "app.js"]
