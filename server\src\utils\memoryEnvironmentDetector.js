/**
 * Memory Environment Detector
 * Automatically detects system memory constraints and adjusts thresholds accordingly
 */

const v8 = require('v8');
const os = require('os');
const fs = require('fs');

class MemoryEnvironmentDetector {
    constructor() {
        this.environmentInfo = null;
        this.detectedLimits = null;
        this.initialize();
    }

    /**
     * Initialize environment detection
     */
    initialize() {
        this.environmentInfo = this.detectEnvironment();
        this.detectedLimits = this.calculateOptimalLimits();
        
        console.log('🔍 [MEMORY ENV] Environment detected:', this.environmentInfo.type);
        console.log('📊 [MEMORY ENV] Optimal limits calculated:', this.detectedLimits);
    }

    /**
     * Detect the runtime environment
     */
    detectEnvironment() {
        const heapStats = v8.getHeapStatistics();
        const systemMemory = os.totalmem();
        const freeMemory = os.freemem();
        
        // Check for containerization
        const isContainer = this.isRunningInContainer();
        
        // Check for memory constraints
        const heapLimitGB = heapStats.heap_size_limit / 1024 / 1024 / 1024;
        const systemMemoryGB = systemMemory / 1024 / 1024 / 1024;
        
        let environmentType = 'unknown';
        let memoryConstraint = 'none';
        
        if (isContainer) {
            environmentType = 'container';
            if (systemMemoryGB < 4) {
                memoryConstraint = 'high';
            } else if (systemMemoryGB < 8) {
                memoryConstraint = 'medium';
            } else {
                memoryConstraint = 'low';
            }
        } else {
            environmentType = 'native';
            if (systemMemoryGB < 8) {
                memoryConstraint = 'medium';
            } else {
                memoryConstraint = 'low';
            }
        }

        return {
            type: environmentType,
            isContainer,
            memoryConstraint,
            systemMemoryGB: systemMemoryGB.toFixed(1),
            heapLimitGB: heapLimitGB.toFixed(1),
            freeMemoryGB: (freeMemory / 1024 / 1024 / 1024).toFixed(1),
            platform: process.platform,
            nodeVersion: process.version
        };
    }

    /**
     * Check if running in a container
     */
    isRunningInContainer() {
        // Check for Docker
        if (fs.existsSync('/.dockerenv')) return true;
        
        // Check for Kubernetes
        if (process.env.KUBERNETES_SERVICE_HOST) return true;
        
        // Check for other container indicators
        if (process.env.DOCKER_CONTAINER) return true;
        
        // Check cgroup (Linux containers)
        try {
            if (fs.existsSync('/proc/1/cgroup')) {
                const cgroup = fs.readFileSync('/proc/1/cgroup', 'utf8');
                if (cgroup.includes('docker') || cgroup.includes('kubepods')) return true;
            }
        } catch (error) {
        }
        
        return false;
    }

    /**
     * Calculate optimal memory limits based on environment
     */
    calculateOptimalLimits() {
        const heapStats = v8.getHeapStatistics();
        const heapLimitMB = heapStats.heap_size_limit / 1024 / 1024;
        const systemMemoryMB = os.totalmem() / 1024 / 1024;
        
        let limits = {};
        
        switch (this.environmentInfo.memoryConstraint) {
            case 'high': // < 4GB system memory
                limits = {
                    searchThresholds: {
                        broad: Math.min(400, heapLimitMB * 0.15),
                        specific: Math.min(200, heapLimitMB * 0.08),
                        none: Math.min(100, heapLimitMB * 0.04)
                    },
                    emergencyThresholds: {
                        broad: Math.min(600, heapLimitMB * 0.25),
                        specific: Math.min(300, heapLimitMB * 0.12),
                        none: Math.min(150, heapLimitMB * 0.06)
                    },
                    cleanupThresholds: {
                        light: Math.min(600, heapLimitMB * 0.25),
                        aggressive: Math.min(1000, heapLimitMB * 0.40)
                    },
                    memoryPressure: {
                        critical: Math.min(1200, heapLimitMB * 0.50),
                        veryHigh: Math.min(1000, heapLimitMB * 0.40),
                        high: Math.min(800, heapLimitMB * 0.30),
                        moderate: Math.min(600, heapLimitMB * 0.25)
                    }
                };
                break;
                
            case 'medium': // 4-8GB system memory
                limits = {
                    searchThresholds: {
                        broad: Math.min(600, heapLimitMB * 0.20),
                        specific: Math.min(300, heapLimitMB * 0.10),
                        none: Math.min(150, heapLimitMB * 0.05)
                    },
                    emergencyThresholds: {
                        broad: Math.min(800, heapLimitMB * 0.30),
                        specific: Math.min(400, heapLimitMB * 0.15),
                        none: Math.min(200, heapLimitMB * 0.08)
                    },
                    cleanupThresholds: {
                        light: Math.min(1000, heapLimitMB * 0.30),
                        aggressive: Math.min(1600, heapLimitMB * 0.50)
                    },
                    memoryPressure: {
                        critical: Math.min(2000, heapLimitMB * 0.60),
                        veryHigh: Math.min(1600, heapLimitMB * 0.50),
                        high: Math.min(1200, heapLimitMB * 0.40),
                        moderate: Math.min(800, heapLimitMB * 0.30)
                    }
                };
                break;
                
            default: // > 8GB system memory
                limits = {
                    searchThresholds: {
                        broad: Math.min(800, heapLimitMB * 0.25),
                        specific: Math.min(400, heapLimitMB * 0.12),
                        none: Math.min(200, heapLimitMB * 0.06)
                    },
                    emergencyThresholds: {
                        broad: Math.min(1200, heapLimitMB * 0.35),
                        specific: Math.min(600, heapLimitMB * 0.18),
                        none: Math.min(300, heapLimitMB * 0.09)
                    },
                    cleanupThresholds: {
                        light: Math.min(1500, heapLimitMB * 0.40),
                        aggressive: Math.min(2500, heapLimitMB * 0.65)
                    },
                    memoryPressure: {
                        critical: Math.min(3300, heapLimitMB * 0.80),
                        veryHigh: Math.min(2500, heapLimitMB * 0.65),
                        high: Math.min(1800, heapLimitMB * 0.50),
                        moderate: Math.min(1200, heapLimitMB * 0.35)
                    }
                };
        }
        
        this.roundLimits(limits);
        
        return limits;
    }

    /**
     * Round all limit values to integers
     */
    roundLimits(limits) {
        for (const category in limits) {
            for (const key in limits[category]) {
                limits[category][key] = Math.round(limits[category][key]);
            }
        }
    }

    /**
     * Get environment information
     */
    getEnvironmentInfo() {
        return this.environmentInfo;
    }

    /**
     * Get optimal limits for current environment
     */
    getOptimalLimits() {
        return this.detectedLimits;
    }

    /**
     * Get recommendations for current environment
     */
    getRecommendations() {
        const recommendations = [];
        
        if (this.environmentInfo.memoryConstraint === 'high') {
            recommendations.push('Consider increasing system memory or using memory-optimized container');
            recommendations.push('Enable aggressive garbage collection');
            recommendations.push('Use streaming data processing for large datasets');
            recommendations.push('Implement request throttling during peak usage');
        }
        
        if (this.environmentInfo.isContainer) {
            recommendations.push('Monitor container memory limits');
            recommendations.push('Consider horizontal scaling instead of vertical scaling');
        }
        
        if (parseFloat(this.environmentInfo.freeMemoryGB) < 1) {
            recommendations.push('WARNING: Very low free memory - consider immediate optimization');
        }
        
        return recommendations;
    }

    /**
     * Log environment summary
     */
    logEnvironmentSummary() {
        console.log('\n🔍 Memory Environment Summary');
        console.log('============================');
        console.log('Environment Type:', this.environmentInfo.type);
        console.log('Memory Constraint:', this.environmentInfo.memoryConstraint);
        console.log('System Memory:', this.environmentInfo.systemMemoryGB, 'GB');
        console.log('Heap Limit:', this.environmentInfo.heapLimitGB, 'GB');
        console.log('Free Memory:', this.environmentInfo.freeMemoryGB, 'GB');
        console.log('Container:', this.environmentInfo.isContainer ? 'Yes' : 'No');
        
        console.log('\n📊 Optimal Thresholds:');
        console.log('Search Thresholds:', this.detectedLimits.searchThresholds);
        console.log('Emergency Thresholds:', this.detectedLimits.emergencyThresholds);
        console.log('Cleanup Thresholds:', this.detectedLimits.cleanupThresholds);
        
        const recommendations = this.getRecommendations();
        if (recommendations.length > 0) {
            console.log('\n💡 Recommendations:');
            recommendations.forEach(rec => console.log('  -', rec));
        }
    }
}

module.exports = new MemoryEnvironmentDetector();
