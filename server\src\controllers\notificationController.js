const models = require('../models');
const { Op } = require('sequelize');
const notificationProcessor = require('../services/notificationProcessor.service');
const { processStoredNotification } = require('./orderController');

/**
 * Get notification processing statistics
 */
exports.getNotificationStats = async (req, res) => {
    try {
        const stats = await notificationProcessor.getProcessingStats();
        
        // Get additional metrics
        const recentFailures = await models.NotificationMessage.findAll({
            where: {
                status: 'failed',
                lastProcessingAttempt: {
                    [Op.gte]: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
                }
            },
            order: [['lastProcessingAttempt', 'DESC']],
            limit: 10,
            attributes: ['id', 'provider', 'notificationType', 'orderId', 'processingError', 'processingAttempts', 'lastProcessingAttempt']
        });

        const oldestPending = await models.NotificationMessage.findOne({
            where: { status: 'pending' },
            order: [['receivedAt', 'ASC']],
            attributes: ['id', 'receivedAt', 'provider', 'notificationType']
        });

        res.json({
            success: true,
            data: {
                stats,
                recentFailures,
                oldestPending,
                lastUpdated: new Date().toISOString()
            }
        });
    } catch (error) {
        console.error('Error getting notification stats:', error);
        res.status(500).json({
            success: false,
            message: 'Error retrieving notification statistics'
        });
    }
};

/**
 * Get notification list with filtering and pagination
 */
exports.getNotifications = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 20,
            status,
            provider,
            notificationType,
            orderId,
            startDate,
            endDate
        } = req.query;

        const offset = (page - 1) * limit;
        const where = {};

        // Apply filters
        if (status) where.status = status;
        if (provider) where.provider = provider;
        if (notificationType) where.notificationType = notificationType;
        if (orderId) where.orderId = { [Op.like]: `%${orderId}%` };

        if (startDate || endDate) {
            where.receivedAt = {};
            if (startDate) where.receivedAt[Op.gte] = new Date(startDate);
            if (endDate) where.receivedAt[Op.lte] = new Date(endDate);
        }

        const { count, rows } = await models.NotificationMessage.findAndCountAll({
            where,
            include: [{
                model: models.Order,
                as: 'order',
                attributes: ['id', 'externalOrderId', 'status'],
                required: false
            }],
            order: [['receivedAt', 'DESC']],
            limit: parseInt(limit),
            offset: parseInt(offset)
        });

        res.json({
            success: true,
            data: {
                notifications: rows,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total: count,
                    pages: Math.ceil(count / limit)
                }
            }
        });
    } catch (error) {
        console.error('Error getting notifications:', error);
        res.status(500).json({
            success: false,
            message: 'Error retrieving notifications'
        });
    }
};

/**
 * Get a specific notification by ID
 */
exports.getNotificationById = async (req, res) => {
    try {
        const { id } = req.params;

        const notification = await models.NotificationMessage.findByPk(id, {
            include: [{
                model: models.Order,
                as: 'order',
                include: [
                    { model: models.User, as: 'user', attributes: ['id', 'email', 'firstName', 'lastName'] },
                    { model: models.EsimPlan, as: 'plan', attributes: ['id', 'name', 'data', 'validity'] }
                ]
            }]
        });

        if (!notification) {
            return res.status(404).json({
                success: false,
                message: 'Notification not found'
            });
        }

        res.json({
            success: true,
            data: notification
        });
    } catch (error) {
        console.error('Error getting notification:', error);
        res.status(500).json({
            success: false,
            message: 'Error retrieving notification'
        });
    }
};

/**
 * Manually retry a failed notification
 */
exports.retryNotification = async (req, res) => {
    try {
        const { id } = req.params;

        const notification = await models.NotificationMessage.findByPk(id);

        if (!notification) {
            return res.status(404).json({
                success: false,
                message: 'Notification not found'
            });
        }

        if (notification.status === 'completed') {
            return res.status(400).json({
                success: false,
                message: 'Notification is already completed'
            });
        }

        // Reset status to pending for retry
        await notification.update({
            status: 'pending'
        });

        // Process the notification
        try {
            await processStoredNotification(notification.id);
            res.json({
                success: true,
                message: 'Notification retry initiated successfully'
            });
        } catch (processingError) {
            res.status(500).json({
                success: false,
                message: 'Error processing notification',
                error: processingError.message
            });
        }
    } catch (error) {
        console.error('Error retrying notification:', error);
        res.status(500).json({
            success: false,
            message: 'Error retrying notification'
        });
    }
};

/**
 * Mark a notification as ignored
 */
exports.ignoreNotification = async (req, res) => {
    try {
        const { id } = req.params;
        const { reason } = req.body;

        const notification = await models.NotificationMessage.findByPk(id);

        if (!notification) {
            return res.status(404).json({
                success: false,
                message: 'Notification not found'
            });
        }

        await notification.update({
            status: 'ignored',
            processingResult: {
                reason: reason || 'Manually ignored by admin',
                ignoredBy: req.user.id,
                ignoredAt: new Date().toISOString()
            },
            processedAt: new Date()
        });

        res.json({
            success: true,
            message: 'Notification marked as ignored'
        });
    } catch (error) {
        console.error('Error ignoring notification:', error);
        res.status(500).json({
            success: false,
            message: 'Error ignoring notification'
        });
    }
};

/**
 * Clean up old notifications
 */
exports.cleanupNotifications = async (req, res) => {
    try {
        const { daysOld = 30 } = req.body;

        await notificationProcessor.cleanupOldNotifications(daysOld);

        res.json({
            success: true,
            message: `Cleanup initiated for notifications older than ${daysOld} days`
        });
    } catch (error) {
        console.error('Error cleaning up notifications:', error);
        res.status(500).json({
            success: false,
            message: 'Error cleaning up notifications'
        });
    }
};

/**
 * Force process all pending notifications
 */
exports.processPendingNotifications = async (req, res) => {
    try {
        // Trigger the processor manually
        notificationProcessor.processPendingNotifications();

        res.json({
            success: true,
            message: 'Pending notification processing initiated'
        });
    } catch (error) {
        console.error('Error processing pending notifications:', error);
        res.status(500).json({
            success: false,
            message: 'Error processing pending notifications'
        });
    }
};

/**
 * Manually trigger retry of all eligible failed notifications (Admin only)
 */
exports.triggerRetryBatch = async (req, res) => {
    try {
        const notificationProcessor = require('../services/notificationProcessor.service');

        // Get status before retry
        console.log('Triggering manual retry batch...');
        await notificationProcessor.getFailedNotificationStatus();

        // Trigger retry
        await notificationProcessor.retryFailedNotifications();

        res.json({
            success: true,
            message: 'Retry batch triggered successfully. Check server logs for details.'
        });
    } catch (error) {
        console.error('Error triggering retry batch:', error);
        res.status(500).json({
            success: false,
            message: 'Error triggering retry batch',
            error: error.message
        });
    }
};
