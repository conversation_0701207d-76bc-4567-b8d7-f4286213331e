/**
 * Simple memory debugging script
 * Run this to check current memory status and test cleanup
 */

const memoryCleanup = require('./src/utils/memoryCleanup');
const searchMemoryManager = require('./src/utils/searchMemoryManager');

function formatMemory(bytes) {
    return (bytes / 1024 / 1024).toFixed(1) + 'MB';
}

function getMemoryStatus() {
    const memUsage = process.memoryUsage();
    const heapUtilization = (memUsage.heapUsed / memUsage.heapTotal) * 100;
    
    return {
        heapUsed: formatMemory(memUsage.heapUsed),
        heapTotal: formatMemory(memUsage.heapTotal),
        rss: formatMemory(memUsage.rss),
        external: formatMemory(memUsage.external),
        heapUtilization: heapUtilization.toFixed(1) + '%'
    };
}

console.log('🔍 Memory Debugging Tool');
console.log('========================');

console.log('\n📊 Current Memory Status:');
const initialStatus = getMemoryStatus();
console.log(JSON.stringify(initialStatus, null, 2));

console.log('\n🧪 Testing Memory Pressure Calculation:');

const memUsage = process.memoryUsage();
const heapUsedMB = memUsage.heapUsed / 1024 / 1024;

console.log(`Heap Used: ${heapUsedMB.toFixed(1)}MB`);

const searchPressure = searchMemoryManager.calculateMemoryPressure();
console.log(`Search Memory Pressure: ${(searchPressure * 100).toFixed(1)}%`);

console.log('\n🧹 Testing Memory Cleanup:');
const beforeCleanup = process.memoryUsage();
console.log(`Before cleanup: ${formatMemory(beforeCleanup.heapUsed)}`);

memoryCleanup.forceGarbageCollection({ cycles: 1, logDetails: true });

setTimeout(() => {
    const afterCleanup = process.memoryUsage();
    console.log(`After cleanup: ${formatMemory(afterCleanup.heapUsed)}`);
    
    const freed = beforeCleanup.heapUsed - afterCleanup.heapUsed;
    console.log(`Memory freed: ${formatMemory(freed)}`);
    
    console.log('\n✅ Memory debugging completed');
    process.exit(0);
}, 1000);
