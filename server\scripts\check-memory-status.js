#!/usr/bin/env node

/**
 * Quick memory status checker
 * Use this to check current memory usage and cache status
 */

const path = require('path');

// Add the src directory to the require path
require('module').globalPaths.push(path.join(__dirname, '../src'));

const memoryMonitor = require('../src/utils/memoryMonitor');
const { getCacheStats } = require('../src/utils/cacheManager');

class MemoryStatusChecker {
    constructor() {
        this.colors = {
            reset: '\x1b[0m',
            bright: '\x1b[1m',
            red: '\x1b[31m',
            green: '\x1b[32m',
            yellow: '\x1b[33m',
            blue: '\x1b[34m',
            magenta: '\x1b[35m',
            cyan: '\x1b[36m'
        };
    }

    /**
     * Colorize text based on status
     */
    colorize(text, color) {
        return `${this.colors[color]}${text}${this.colors.reset}`;
    }

    /**
     * Get memory status color based on usage
     */
    getMemoryStatusColor(percentage) {
        if (percentage > 90) return 'red';
        if (percentage > 75) return 'yellow';
        if (percentage > 50) return 'blue';
        return 'green';
    }

    /**
     * Format bytes to human readable
     */
    formatBytes(bytes) {
        const mb = bytes / 1024 / 1024;
        return `${mb.toFixed(1)}MB`;
    }

    /**
     * Display current memory status
     */
    displayMemoryStatus() {
        console.log(this.colorize('\n🧠 Memory Status', 'bright'));
        console.log('=' .repeat(50));

        try {
            const usage = memoryMonitor.getMemoryUsage();
            const maxHeapMB = Math.round(memoryMonitor.maxHeapUsage / 1024 / 1024);
            
            const statusColor = this.getMemoryStatusColor(usage.heapUsagePercent);
            
            console.log(`Heap Used:      ${this.colorize(this.formatBytes(usage.heapUsed), statusColor)} (${this.colorize(usage.heapUsagePercent.toFixed(1) + '%', statusColor)})`);
            console.log(`Heap Total:     ${this.formatBytes(usage.heapTotal)}`);
            console.log(`Heap Limit:     ${maxHeapMB}MB`);
            console.log(`External:       ${this.formatBytes(usage.external)}`);
            console.log(`RSS:            ${this.formatBytes(usage.rss)}`);
            
            // Memory status indicators
            console.log('\nStatus Indicators:');
            if (usage.heapUsagePercent > 90) {
                console.log(`  ${this.colorize('🚨 CRITICAL', 'red')} - Memory usage very high`);
            } else if (usage.heapUsagePercent > 75) {
                console.log(`  ${this.colorize('⚠️  WARNING', 'yellow')} - Memory usage high`);
            } else if (usage.heapUsagePercent > 50) {
                console.log(`  ${this.colorize('ℹ️  MODERATE', 'blue')} - Memory usage moderate`);
            } else {
                console.log(`  ${this.colorize('✅ GOOD', 'green')} - Memory usage normal`);
            }
            
            // Safety checks
            const isSafe = memoryMonitor.isSafeToProcess();
            const isWarning = memoryMonitor.isMemoryWarning();
            const isCritical = memoryMonitor.isMemoryCritical();
            
            console.log(`  Safe to process: ${isSafe ? this.colorize('Yes', 'green') : this.colorize('No', 'red')}`);
            console.log(`  Warning level:   ${isWarning ? this.colorize('Yes', 'yellow') : this.colorize('No', 'green')}`);
            console.log(`  Critical level:  ${isCritical ? this.colorize('Yes', 'red') : this.colorize('No', 'green')}`);

        } catch (error) {
            console.log(this.colorize(`Error getting memory status: ${error.message}`, 'red'));
        }
    }

    /**
     * Display cache status
     */
    displayCacheStatus() {
        console.log(this.colorize('\n💾 Cache Status', 'bright'));
        console.log('=' .repeat(50));

        try {
            const cacheStats = getCacheStats();
            
            console.log(`Total Entries:    ${cacheStats.totalEntries}`);
            console.log(`Valid Entries:    ${this.colorize(cacheStats.validEntries, 'green')}`);
            console.log(`Expired Entries:  ${cacheStats.expiredEntries > 0 ? this.colorize(cacheStats.expiredEntries, 'yellow') : cacheStats.expiredEntries}`);
            console.log(`Hit Rate:         ${cacheStats.hitRate ? (cacheStats.hitRate * 100).toFixed(1) + '%' : 'N/A'}`);
            
            // Cache health indicators
            console.log('\nCache Health:');
            if (cacheStats.totalEntries > 800) {
                console.log(`  ${this.colorize('⚠️  HIGH', 'yellow')} - Cache size is high (${cacheStats.totalEntries} entries)`);
            } else if (cacheStats.totalEntries > 500) {
                console.log(`  ${this.colorize('ℹ️  MODERATE', 'blue')} - Cache size is moderate (${cacheStats.totalEntries} entries)`);
            } else {
                console.log(`  ${this.colorize('✅ GOOD', 'green')} - Cache size is healthy (${cacheStats.totalEntries} entries)`);
            }
            
            if (cacheStats.expiredEntries > cacheStats.validEntries * 0.3) {
                console.log(`  ${this.colorize('⚠️  CLEANUP NEEDED', 'yellow')} - Many expired entries (${cacheStats.expiredEntries})`);
            }

        } catch (error) {
            console.log(this.colorize(`Error getting cache status: ${error.message}`, 'red'));
        }
    }

    /**
     * Display system information
     */
    displaySystemInfo() {
        console.log(this.colorize('\n🖥️  System Information', 'bright'));
        console.log('=' .repeat(50));

        const nodeVersion = process.version;
        const platform = process.platform;
        const arch = process.arch;
        const uptime = process.uptime();
        
        console.log(`Node.js Version:  ${nodeVersion}`);
        console.log(`Platform:         ${platform} (${arch})`);
        console.log(`Process Uptime:   ${Math.floor(uptime / 3600)}h ${Math.floor((uptime % 3600) / 60)}m ${Math.floor(uptime % 60)}s`);
        console.log(`Process PID:      ${process.pid}`);
        
        // Environment info
        const env = process.env.NODE_ENV || 'development';
        console.log(`Environment:      ${env}`);
        
        // Memory limits
        const v8 = require('v8');
        const heapStats = v8.getHeapStatistics();
        console.log(`Heap Size Limit:  ${this.formatBytes(heapStats.heap_size_limit)}`);
        console.log(`Total Heap Size:  ${this.formatBytes(heapStats.total_heap_size)}`);
        console.log(`Used Heap Size:   ${this.formatBytes(heapStats.used_heap_size)}`);
    }

    /**
     * Check for memory leaks
     */
    checkMemoryLeaks() {
        console.log(this.colorize('\n🔍 Memory Leak Detection', 'bright'));
        console.log('=' .repeat(50));

        try {
            const leakCheck = memoryMonitor.checkForMemoryLeaks();
            
            if (leakCheck.detected) {
                console.log(`${this.colorize('🚨 MEMORY LEAK DETECTED', 'red')}`);
                console.log(`Leak Score: ${this.colorize(leakCheck.score + '/4', 'red')}`);
                console.log('Indicators:');
                Object.entries(leakCheck.indicators).forEach(([key, value]) => {
                    const status = value ? this.colorize('YES', 'red') : this.colorize('NO', 'green');
                    console.log(`  ${key}: ${status}`);
                });
            } else {
                console.log(`${this.colorize('✅ No memory leak detected', 'green')}`);
                console.log(`Leak Score: ${leakCheck.score}/4`);
            }

        } catch (error) {
            console.log(this.colorize(`Error checking for memory leaks: ${error.message}`, 'red'));
        }
    }

    /**
     * Provide recommendations
     */
    provideRecommendations() {
        console.log(this.colorize('\n💡 Recommendations', 'bright'));
        console.log('=' .repeat(50));

        try {
            const usage = memoryMonitor.getMemoryUsage();
            const cacheStats = getCacheStats();
            const leakCheck = memoryMonitor.checkForMemoryLeaks();

            const recommendations = [];

            if (usage.heapUsagePercent > 90) {
                recommendations.push('🚨 URGENT: Memory usage is critical - consider restarting the application');
                recommendations.push('🗑️  Clear caches immediately');
                recommendations.push('🔄 Reduce application load');
            } else if (usage.heapUsagePercent > 75) {
                recommendations.push('⚠️  Monitor memory usage closely');
                recommendations.push('🗑️  Consider clearing caches');
            }

            if (cacheStats.totalEntries > 800) {
                recommendations.push('💾 Cache size is high - consider reducing cache TTL');
            }

            if (cacheStats.expiredEntries > cacheStats.validEntries * 0.3) {
                recommendations.push('🧹 Run cache cleanup to remove expired entries');
            }

            if (leakCheck.detected) {
                recommendations.push('🔍 Memory leak detected - investigate and fix the root cause');
                if (leakCheck.score >= 3) {
                    recommendations.push('🚨 High leak score - restart application soon');
                }
            }

            if (recommendations.length === 0) {
                console.log(this.colorize('✅ System is running well - no immediate actions needed', 'green'));
            } else {
                recommendations.forEach(rec => console.log(`  ${rec}`));
            }

        } catch (error) {
            console.log(this.colorize(`Error generating recommendations: ${error.message}`, 'red'));
        }
    }

    /**
     * Run complete status check
     */
    run() {
        console.log(this.colorize('Memory Status Checker', 'bright'));
        console.log(this.colorize('='.repeat(60), 'bright'));
        console.log(`Timestamp: ${new Date().toISOString()}`);

        this.displayMemoryStatus();
        this.displayCacheStatus();
        this.displaySystemInfo();
        this.checkMemoryLeaks();
        this.provideRecommendations();

        console.log(this.colorize('\n✅ Status check completed', 'green'));
    }
}

// Main execution
if (require.main === module) {
    const checker = new MemoryStatusChecker();
    
    const args = process.argv.slice(2);
    if (args.includes('--help')) {
        console.log('Usage: node scripts/check-memory-status.js [options]');
        console.log('Options:');
        console.log('  --help     Show this help message');
        console.log('  --watch    Continuously monitor (every 10 seconds)');
        process.exit(0);
    }
    
    if (args.includes('--watch')) {
        console.log('Starting continuous monitoring (Ctrl+C to stop)...\n');
        
        const runCheck = () => {
            console.clear();
            checker.run();
        };
        
        runCheck();
        setInterval(runCheck, 10000);
        
        process.on('SIGINT', () => {
            console.log('\n\nMonitoring stopped.');
            process.exit(0);
        });
    } else {
        checker.run();
    }
}

module.exports = MemoryStatusChecker;
