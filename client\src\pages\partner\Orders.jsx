import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import api from '@/lib/axios';
import { useToast } from '@/components/ui/use-toast';
import {
    <PERSON>,
    CardHeader,
    CardTitle,
    CardContent,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Loader2, Eye, ShoppingBag, AlertCircle, RefreshCw } from "lucide-react";

const Orders = () => {
    const navigate = useNavigate();
    const { toast } = useToast();
    const [orders, setOrders] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        fetchOrders();
    }, []);

    const fetchOrders = async () => {
        try {
            setLoading(true);
            setError(null);
            const response = await api.get('/api/orders');
            setOrders(response.data);
        } catch (error) {
            // console.error('Error fetching orders:', error);
            setError(error.response?.data?.message || "Failed to fetch orders");
            toast({
                variant: "destructive",
                title: "Error",
                description: error.response?.data?.message || "Failed to fetch orders"
            });
        } finally {
            setLoading(false);
        }
    };

    const getStatusVariant = (status) => {
        switch (status) {
            case 'completed':
                return 'success';
            case 'pending':
                return 'warning';
            default:
                return 'destructive';
        }
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center h-[calc(100vh-4rem)]">
                <div className="text-center space-y-4">
                    <Loader2 className="w-12 h-12 animate-spin mx-auto text-primary" />
                    <p className="text-gray-600 text-lg font-medium">Loading your orders...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
                <Card className="w-full max-w-3xl mx-auto shadow-md border-0 overflow-hidden">
                    <CardHeader className="border-b bg-red-50 py-8">
                        <CardTitle className="text-3xl font-bold flex items-center justify-center space-x-3 text-red-600">
                            <AlertCircle className="w-8 h-8" />
                            <span>Error Loading Orders</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="p-10">
                        <div className="flex flex-col items-center justify-center space-y-8">
                            <p className="text-xl text-gray-700 font-medium text-center max-w-md">
                                {error}
                            </p>
                            <Button
                                variant="outline"
                                className="px-10 py-6 text-lg font-semibold rounded-full"
                                size="lg"
                                onClick={fetchOrders}
                            >
                                Try Again
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        );
    }

    if (!orders.length) {
        return (
            <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
                <Card className="w-full max-w-3xl mx-auto shadow-md border-0 overflow-hidden">
                    <CardHeader className="border-b bg-gradient-to-r from-blue-800 to-blue-600 py-8">
                        <CardTitle className="text-3xl font-bold flex items-center justify-center space-x-3 text-white">
                            <ShoppingBag className="w-8 h-8" />
                            <span>No Orders Yet</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="p-10">
                        <div className="flex flex-col items-center justify-center space-y-8">
                            <div className="rounded-full bg-gray-100 p-8">
                                <ShoppingBag className="w-20 h-20 text-gray-400" />
                            </div>
                            <p className="text-xl text-gray-700 font-medium text-center max-w-md">
                                You haven't placed any orders yet. Browse our collection to find the perfect eSIM plan for your needs.
                            </p>
                            <Button
                                className="px-10 py-6 text-lg font-semibold rounded-full bg-blue-600 text-white"
                                size="lg"
                                onClick={() => navigate('/dashboard/plans')}
                            >
                                Browse Plans
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 sm:px-6 lg:px-4">
            <Card className="w-full max-w-8xl mx-auto h-100vh shadow-md border-0 overflow-hidden">
                <CardHeader className="border-b bg-gradient-to-r from-blue-800 to-blue-600 py-6">
                    <div className="flex items-center justify-between">
                        <CardTitle className="text-3xl font-bold flex items-center space-x-3 text-white">
                            <ShoppingBag className="w-8 h-8" />
                            <span>My Orders</span>
                        </CardTitle>
                        <div className="flex items-center gap-3">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={fetchOrders}
                                className="bg-white text-blue-600 border-white hover:bg-gray-100"
                                disabled={loading}
                            >
                                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                                Refresh
                            </Button>
                            <Badge className="py-2 px-4 text-sm bg-white text-primary hover:bg-gray-100">
                                {orders.length} order{orders.length > 1 ? 's' : ''}
                            </Badge>
                        </div>
                    </div>
                </CardHeader>

                <CardContent className="p-6">
                    <div className="overflow-x-auto max-h-[600px]">
                        <table className="w-full border-collapse">
                            <thead className="bg-gray-800 sticky top-0">
                                <tr className="bg-gray-200 border-b">
                                    <th className="px-4 py-3  text-sm font-semibold text-gray-600">Order ID</th>
                                    <th className="px-4 py-3  text-sm font-semibold text-gray-600">Plan Name</th>
                                    <th className="px-4 py-3  text-sm font-semibold text-gray-600">Quantity</th>
                                    <th className="px-4 py-3  text-sm font-semibold text-gray-600">Total</th>
                                    <th className="px-4 py-3  text-sm font-semibold text-gray-600">Data</th>
                                    <th className="px-4 py-3  text-sm font-semibold text-gray-600">Validity</th>
                                    <th className="px-4 py-3  text-sm font-semibold text-gray-600">Status</th>
                                    <th className="px-4 py-3 text-sm font-semibold text-gray-600">Order Date</th>
                                    <th className="px-4 py-3 text-sm font-semibold text-gray-600">Actions</th>

                                </tr>
                            </thead>
                            <tbody className="divide-y divide-gray-200">
                                {orders.map((order) => (
                                    <tr 
                                        key={order.id}
                                        className="hover:bg-gray-50 transition-colors duration-200"
                                    >
                                        <td className="px-4 py-4 text-center text-sm text-gray-900 font-medium">
                                            #{order.id}
                                        </td>
                                        <td className="px-4 py-4 text-xs text-center font-medium text-gray-900">
                                            {order.plan.name}
                                        </td>
                                        <td className="px-4 py-4 text-center text-xs text-gray-700 font-medium">
                                            {order.quantity}
                                        </td>
                                        <td className="px-4 py-4 text-center text-xs text-gray-900 font-medium">
                                            ${parseFloat(order.orderTotal).toFixed(2)}
                                        </td>
                                        <td className="px-4 py-4 text-center text-xs text-gray-900 font-medium">
                                        {order.plan.planType === 'Unlimited' ? (
                                            <span className="font-medium text-blue-600">Unlimited</span>
                                        ) : order.plan.planType === 'Custom' ? (
                                            <span className="font-medium text-purple-600">
                                                {order.plan.customPlanData || 'Custom Plan'}
                                            </span>
                                        ) : order.plan.planData && order.plan.planDataUnit ? (
                                            `${order.plan.planData} ${order.plan.planDataUnit}`
                                        ) : (
                                            '-'
                                        )}
                                        </td>

                                        <td className="px-4 py-4 text-center text-xs text-gray-900 font-medium">
                                            {order.plan.validityDays} Day{order.plan.validityDays > 1 ? 's' : ''}
                                        </td>
                                        <td className="px-4 py-4 text-center">
                                            <Badge 
                                                variant={getStatusVariant(order.status)}
                                                className="capitalize"
                                            >
                                                {order.status}
                                            </Badge>
                                        </td>
                                        <td className="px-4 py-4 text-center text-xs text-gray-900 font-medium">
                                            {format(new Date(order.createdAt), 'MMM dd, yyyy')}
                                        </td>
                                        <td className="flex items-center gap-2 px-4 py-4">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => navigate(`/dashboard/orders/${order.id}`)}
                                                className="bg-blue-600 flex items-center gap-1 hover:bg-blue-700 hover:text-white transition-colors"
                                            >
                                                <Eye className="h-3 w-3" />
                                                View
                                            </Button>
                                            {/* Hide Order Again button for topup orders */}
                                            {!order.parentOrderId && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="flex items-center gap-1 hover:bg-primary hover:text-white transition-colors"
                                                    onClick={() => navigate(`/dashboard/plans/${order.plan.id}`)}
                                                >
                                                    Order Again
                                                </Button>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
};

export default Orders;