/**
 * Memory Configuration
 * Automatically adapts memory thresholds based on system environment
 */

const os = require('os');
const v8 = require('v8');

class MemoryConfig {
    constructor() {
        this.systemMemoryGB = os.totalmem() / 1024 / 1024 / 1024;
        this.heapLimitMB = v8.getHeapStatistics().heap_size_limit / 1024 / 1024;
        this.config = this.generateConfig();
    }

    /**
     * Generate memory configuration based on system resources
     */
    generateConfig() {
        // Detect environment type
        const isLowMemory = this.systemMemoryGB < 4;
        const isMediumMemory = this.systemMemoryGB >= 4 && this.systemMemoryGB < 8;
        const isHighMemory = this.systemMemoryGB >= 8;

        let config;

        if (isLowMemory) {
            // Conservative settings for < 4GB systems 
            config = {
                environment: 'low-memory',
                searchThresholds: {
                    broad: 400,
                    specific: 200,
                    none: 100
                },
                emergencyThresholds: {
                    broad: 600,
                    specific: 300,
                    none: 150
                },
                cleanupThresholds: {
                    light: 600,
                    aggressive: 1000
                },
                memoryPressure: {
                    critical: 1600,  
                    veryHigh: 1400,  
                    high: 1200,      
                    moderate: 1000,  
                    low: 600         
                }
            };
        } else if (isMediumMemory) {
            // Balanced settings for 4-8GB systems
            config = {
                environment: 'medium-memory',
                searchThresholds: {
                    broad: 600,
                    specific: 300,
                    none: 150
                },
                emergencyThresholds: {
                    broad: 800,
                    specific: 400,
                    none: 200
                },
                cleanupThresholds: {
                    light: 1000,
                    aggressive: 1600
                },
                memoryPressure: {
                    critical: 2000,
                    veryHigh: 1600,
                    high: 1200,
                    moderate: 800,
                    low: 400
                }
            };
        } else {
            // Generous settings for > 8GB systems
            config = {
                environment: 'high-memory',
                searchThresholds: {
                    broad: 800,
                    specific: 400,
                    none: 200
                },
                emergencyThresholds: {
                    broad: 1200,
                    specific: 600,
                    none: 300
                },
                cleanupThresholds: {
                    light: 1500,
                    aggressive: 2500
                },
                memoryPressure: {
                    critical: 3300,
                    veryHigh: 2500,
                    high: 1800,
                    moderate: 1200,
                    low: 600
                }
            };
        }

        config.systemInfo = {
            totalMemoryGB: this.systemMemoryGB.toFixed(1),
            heapLimitMB: Math.round(this.heapLimitMB),
            platform: process.platform,
            nodeVersion: process.version
        };

        return config;
    }

    /**
     * Get search memory thresholds
     */
    getSearchThresholds() {
        return this.config.searchThresholds;
    }

    /**
     * Get emergency thresholds
     */
    getEmergencyThresholds() {
        return this.config.emergencyThresholds;
    }

    /**
     * Get cleanup thresholds
     */
    getCleanupThresholds() {
        return this.config.cleanupThresholds;
    }

    /**
     * Get memory pressure thresholds
     */
    getMemoryPressureThresholds() {
        return this.config.memoryPressure;
    }

    /**
     * Get full configuration
     */
    getConfig() {
        return this.config;
    }

    /**
     * Check if current memory usage exceeds threshold
     */
    checkMemoryPressure(currentMemoryMB) {
        const thresholds = this.config.memoryPressure;
        
        if (currentMemoryMB > thresholds.critical) return 'critical';
        if (currentMemoryMB > thresholds.veryHigh) return 'very-high';
        if (currentMemoryMB > thresholds.high) return 'high';
        if (currentMemoryMB > thresholds.moderate) return 'moderate';
        if (currentMemoryMB > thresholds.low) return 'low';
        return 'normal';
    }

    /**
     * Get normalized memory pressure (0-1)
     */
    getNormalizedMemoryPressure(currentMemoryMB) {
        const thresholds = this.config.memoryPressure;
        
        if (currentMemoryMB > thresholds.critical) return 1.0;
        if (currentMemoryMB > thresholds.veryHigh) return 0.9;
        if (currentMemoryMB > thresholds.high) return 0.8;
        if (currentMemoryMB > thresholds.moderate) return 0.6;
        if (currentMemoryMB > thresholds.low) return 0.4;
        return currentMemoryMB / thresholds.low; // 0-0.4 range
    }

    /**
     * Log configuration summary
     */
    logConfig() {
        console.log('\n🔧 Memory Configuration');
        console.log('=======================');
        console.log('Environment:', this.config.environment);
        console.log('System Memory:', this.config.systemInfo.totalMemoryGB, 'GB');
        console.log('Heap Limit:', this.config.systemInfo.heapLimitMB, 'MB');
        console.log('Platform:', this.config.systemInfo.platform);
        
        console.log('\n📊 Thresholds:');
        console.log('Search:', this.config.searchThresholds);
        console.log('Emergency:', this.config.emergencyThresholds);
        console.log('Cleanup:', this.config.cleanupThresholds);
        console.log('Memory Pressure:', this.config.memoryPressure);
    }
}

module.exports = new MemoryConfig();
