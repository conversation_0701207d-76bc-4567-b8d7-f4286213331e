const { Order, User, EsimPlan, Provider } = require('./src/models');
const { v4: uuidv4 } = require('uuid');

async function createTestOrder() {
    try {
        // Find or create test user
        let user = await User.findOne({ where: { email: '<EMAIL>' } });
        if (!user) {
            user = await User.create({
                id: uuidv4(),
                firstName: 'Test',
                lastName: 'User',
                email: '<EMAIL>',
                password: 'testpassword',
                role: 'partner',
                markupPercentage: 0,
                isActive: true,
                loginAttempts: 0
            });
        }

        // Find BillionConnect provider
        const provider = await Provider.findOne({
            where: { name: 'billionconnect' }
        });

        if (!provider) {
            throw new Error('BillionConnect provider not found');
        }

        // Find or create test plan
        let plan = await EsimPlan.findOne({
            where: { providerId: provider.id }
        });

        if (!plan) {
            plan = await EsimPlan.create({
                id: uuidv4(),
                name: 'Test Plan',
                description: 'Test Plan Description',
                planInfo: 'Test Plan Info',
                additionalInfo: 'Test Additional Info',
                networkName: 'Test Network',
                networkType: '4G/5G',
                region: 'Global',
                supportedRegions: JSON.stringify(['Global']),
                planData: 1000,
                planDataUnit: 'MB',
                validityDays: 30,
                providerId: provider.id,
                externalProductId: '1090',
                externalSkuId: '1090',
                planType: 'Data',
                category: 'Standard',
                planCategory: 'Data Only',
                status: 'active',
                isActive: true,
                startDateEnabled: false,
                hotspot: true,
                activationPolicy: 'Activation upon purchase',
                speed: 'Unrestricted',
                stockThreshold: 10
            });
        }

        // Create test order
        const order = await Order.create({
            id: `VLZ2030`, // Generate a unique ID
            userId: user.id,
            esimPlanId: plan.id,
            quantity: 1,
            orderTotal: 10,
            startDate: new Date(),
            status: 'pending',
            providerMetadata: {},
            providerOrderStatus: 'PENDING',
            externalOrderId: '2750936766287561'
        });

        console.log('Test order created:', {
            orderId: order.id,
            externalOrderId: order.externalOrderId,
            userId: order.userId,
            planId: order.esimPlanId
        });

    } catch (error) {
        console.error('Error creating test order:', error);
    }
}

createTestOrder(); 