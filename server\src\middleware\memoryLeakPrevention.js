/**
 * Memory Leak Prevention Middleware
 * Prevents memory leaks in API endpoints by monitoring and cleaning up memory usage
 */

const searchMemoryManager = require('../utils/searchMemoryManager');
const memoryCleanup = require('../utils/memoryCleanup');
const { v4: uuidv4 } = require('uuid');

/**
 * Memory leak prevention middleware for search operations
 */
const memoryLeakPrevention = (options = {}) => {
    const {
        searchTypeDetector = detectSearchType,
        memoryThreshold = 100, // 100MB threshold
        enableProfiling = false,
        abortOnThreshold = true
    } = options;

    return async (req, res, next) => {
        const operationId = uuidv4();
        const startTime = Date.now();
        const startMemory = process.memoryUsage().heapUsed / 1024 / 1024;

        // Detect search type and term
        const searchType = searchTypeDetector(req);
        const searchTerm = req.query.search || '';

        // Start memory monitoring
        const operation = searchMemoryManager.startSearchOperation(operationId, searchType, searchTerm);

        // Add memory utilities to request object
        req.memoryManager = {
            operationId,
            searchType,
            startMemory,
            checkMemory: (phase) => searchMemoryManager.checkSearchMemory(operationId, phase),
            cleanupData: (data) => searchMemoryManager.cleanupSearchData(data, searchType),
            takeSnapshot: (label) => memoryCleanup.takeMemorySnapshot(`${operationId}_${label}`)
        };

        // Override res.json to ensure cleanup
        const originalJson = res.json.bind(res);
        res.json = function(data) {
            try {
                searchMemoryManager.completeSearchOperation(operationId, {
                    responseSize: JSON.stringify(data).length,
                    statusCode: res.statusCode
                });

                return originalJson(data);
            } catch (error) {
                console.error('Error in memory cleanup during response:', error);
                return originalJson(data);
            }
        };

        // Handle errors and aborts
        const cleanup = (reason) => {
            if (operation.isActive) {
                searchMemoryManager.abortSearchOperation(operationId, reason);
            }
        };

        // Set up cleanup on response finish
        res.on('finish', () => {
            const endTime = Date.now();
            const endMemory = process.memoryUsage().heapUsed / 1024 / 1024;
            const memoryIncrease = endMemory - startMemory;
            const duration = endTime - startTime;

            console.log(`📊 [MEMORY MIDDLEWARE] ${req.method} ${req.path} completed in ${duration}ms (+${memoryIncrease.toFixed(1)}MB)`);

            // Trigger cleanup if memory increase is significant
            if (memoryIncrease > memoryThreshold * 0.5) {
                setTimeout(() => {
                    memoryCleanup.proactiveCleanup();
                }, 1000); // Cleanup after 1 second delay
            }
        });

        res.on('close', () => {
            cleanup('connection_closed');
        });

        res.on('error', (error) => {
            console.error('Response error:', error);
            cleanup('response_error');
        });

        next();
    };
};

/**
 * Detect search type based on request parameters
 */
function detectSearchType(req) {
    const { search, countryId, region } = req.query;
    
    if (!search) {
        return 'none';
    }

    const searchLower = search.toLowerCase().trim();
    const broadSearchTerms = ['global', 'globa', 'premium', 'business', 'unlimited', 'world', 'international'];
    
    // Check if it's a broad search
    const isBroadSearch = broadSearchTerms.some(term => 
        searchLower === term || 
        searchLower.startsWith(term) || 
        (searchLower.length <= 5 && term.startsWith(searchLower))
    );

    if (isBroadSearch) {
        return 'broad';
    }

    if (countryId || region || searchLower.length > 10) {
        return 'specific';
    }

    return searchLower.length <= 3 ? 'broad' : 'specific';
}

/**
 * Memory monitoring middleware for general endpoints
 */
const memoryMonitoring = (options = {}) => {
    const {
        threshold = 50, // 50MB threshold
        logMemoryUsage = true
    } = options;

    return (req, res, next) => {
        const startMemory = process.memoryUsage().heapUsed / 1024 / 1024;
        const startTime = Date.now();

        if (logMemoryUsage) {
            console.log(`📊 [MEMORY] ${req.method} ${req.path} started (${startMemory.toFixed(1)}MB)`);
        }

        // Override res.end to log memory usage
        const originalEnd = res.end.bind(res);
        res.end = function(...args) {
            const endMemory = process.memoryUsage().heapUsed / 1024 / 1024;
            const memoryIncrease = endMemory - startMemory;
            const duration = Date.now() - startTime;

            if (logMemoryUsage || memoryIncrease > threshold) {
                const level = memoryIncrease > threshold ? '⚠️' : '📊';
                console.log(`${level} [MEMORY] ${req.method} ${req.path} completed in ${duration}ms (+${memoryIncrease.toFixed(1)}MB)`);
            }

            // Trigger cleanup if memory increase is significant
            if (memoryIncrease > threshold) {
                setImmediate(() => {
                    memoryCleanup.forceGarbageCollection({ cycles: 1, logDetails: false });
                });
            }

            return originalEnd(...args);
        };

        next();
    };
};

/**
 * Emergency memory protection middleware
 */
const emergencyMemoryProtection = (options = {}) => {
    const {
        criticalThreshold = 1500, // 1.5GB critical threshold
        warningThreshold = 1200   // 1.2GB warning threshold
    } = options;

    return (req, res, next) => {
        const currentMemory = process.memoryUsage().heapUsed / 1024 / 1024;

        // Check for critical memory levels
        if (currentMemory > criticalThreshold) {
            console.error(`🚨 [EMERGENCY] Critical memory level: ${currentMemory.toFixed(1)}MB - REJECTING REQUEST`);
            
            // Trigger emergency cleanup
            searchMemoryManager.emergencyCleanupAll();
            
            return res.status(503).json({
                error: 'Service temporarily unavailable',
                message: 'System memory is critically high. Please try again in a few minutes.',
                code: 'MEMORY_CRITICAL',
                currentMemory: `${currentMemory.toFixed(1)}MB`
            });
        }

        // Check for warning levels
        if (currentMemory > warningThreshold) {
            console.warn(`⚠️ [MEMORY WARNING] High memory usage: ${currentMemory.toFixed(1)}MB`);
            
            // Trigger proactive cleanup
            setImmediate(() => {
                memoryCleanup.proactiveCleanup();
            });
        }

        next();
    };
};

/**
 * Memory cleanup after response middleware
 */
const memoryCleanupAfterResponse = (options = {}) => {
    const {
        delay = 1000, // 1 second delay
        forceGC = true
    } = options;

    return (req, res, next) => {
        // Set up cleanup after response is sent
        res.on('finish', () => {
            setTimeout(() => {
                if (forceGC) {
                    memoryCleanup.forceGarbageCollection({ cycles: 1, logDetails: false });
                }
                
                memoryCleanup.cleanupWeakReferences();
                
                memoryCleanup.processStreamingCleanupQueue();
            }, delay);
        });

        next();
    };
};

/**
 * Get memory statistics middleware (for debugging)
 */
const memoryStats = (req, res, next) => {
    req.getMemoryStats = () => {
        const memoryReport = memoryCleanup.getMemoryReport();
        const searchStats = searchMemoryManager.getActiveSearchStats();
        
        return {
            ...memoryReport,
            activeSearches: searchStats,
            timestamp: new Date().toISOString()
        };
    };

    next();
};

module.exports = {
    memoryLeakPrevention,
    memoryMonitoring,
    emergencyMemoryProtection,
    memoryCleanupAfterResponse,
    memoryStats,
    detectSearchType
};
