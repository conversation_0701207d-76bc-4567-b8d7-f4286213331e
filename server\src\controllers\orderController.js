const models = require('../models');
const sequelize = require('../config/database');
const { v4: uuidv4 } = require('uuid');
const QRCode = require('qrcode');
const emailService = require('../utils/emailService');
const { Op } = require('sequelize');
const providerFactory = require('../services/provider.factory');
const mobimatterService = require('../services/mobimatter.service');
const billionconnectService = require('../services/billionconnect.service');
const memoryMonitor = require('../utils/memoryMonitor');
const { getCachedPlan, setCachePlan, invalidatePlanCache } = require('../utils/cacheManager');

// Helper function to generate QR code as data URL
const generateQRCode = async (text) => {
    try {
        return await QRCode.toDataURL(text, {
            errorCorrectionLevel: 'H',
            type: 'image/png',
            margin: 2,
            width: 400,
            color: {
                dark: '#000000',
                light: '#ffffff'
            },
            rendererOpts: {
                quality: 1
            }
        });
    } catch (err) {
        console.error('Error generating QR code:', err);
        return null;
    }
};

// Helper function to parse LPA string and extract components
const parseLpaString = (lpaString) => {
    try {
        if (!lpaString || !lpaString.startsWith('LPA:')) {
            return null;
        }

        // LPA format: LPA:1$<SMDP_ADDRESS>$<ACTIVATION_CODE>
        const parts = lpaString.split('$');
        if (parts.length >= 3) {
            return {
                smdpAddress: parts[1],
                activationCode: parts[2],
                lpaString: lpaString
            };
        }

        return null;
    } catch (error) {
        console.error('Error parsing LPA string:', error);
        return null;
    }
};

// Helper function to check stock and notify admins
const checkStockAndNotify = async (planId, t) => {
    try {
        // Get plan details with current stock count and provider info
        const plan = await models.EsimPlan.findByPk(planId, {
            include: [
                {
                    model: models.EsimStock,
                    as: 'stocks',
                    where: { status: 'available' },
                    required: false
                },
                {
                    model: models.Provider,
                    as: 'provider',
                    attributes: ['id', 'name', 'type', 'country']
                }
            ],
            transaction: t
        });

        if (!plan) {
            return;
        }

        const currentStock = plan.stocks?.length || 0;
        
        // Skip low stock notification for API providers (Mobimatter, Billionconnect)
        if (plan.provider?.name === 'Mobimatter' ||
            plan.provider?.name === 'Billionconnect' ||
            plan.provider?.type === 'API') {
            console.log(`Skipping low stock notification for ${plan.provider?.name} plan (API provider)`);
            return;
        }
        
        // Check if stock is below threshold for non-Mobimatter plans
        if (currentStock < plan.stockThreshold) {
            // Get all admin users
            const admins = await models.User.findAll({
                where: { role: 'admin', isActive: true },
                attributes: ['email']
            });

            const adminEmails = admins.map(admin => admin.email);
            
            // Send notification
            await emailService.sendLowStockEmail(adminEmails, plan, currentStock);
        }
    } catch (error) {
        console.error('Error in stock check and notification:', error);
        // Don't throw error as this is a notification service
    }
};

// Create a new order from cart items
exports.createOrder = async (req, res) => {
    const t = await sequelize.transaction();
    try {
        console.log('Starting order creation process...');
        const userId = req.user.id;
        const { items } = req.body;
        console.log('User ID:', userId);
        console.log('Items received:', items);

        // Get partner for markup calculation
        const partner = await models.User.findByPk(userId, { transaction: t });
        if (!partner) {
            console.log('Partner not found for userId:', userId);
            await t.rollback();
            return res.status(404).json({ message: 'Partner not found' });
        }
        console.log('Partner found:', partner.id);

        // Get user details for the order
        const user = await models.User.findByPk(userId);
        if (!user) {
            throw new Error('User not found');
        }

        // Get cart items with full plan details
        const cartItems = await models.Cart.findAll({
            where: { userId },
            include: [{
                model: models.EsimPlan,
                as: 'EsimPlan',
                include: [{
                    model: models.Provider,
                    as: 'provider'
                }]
            }],
            transaction: t
        });

        console.log('Cart items found:', cartItems.length);
        if (!cartItems.length) {
            console.log('Cart is empty for userId:', userId);
            await t.rollback();
            return res.status(400).json({ message: 'Cart is empty' });
        }

        // Calculate total order amount and validate prices
        let totalAmount = 0;
        const processedItems = cartItems.map(item => {
            const cartItem = item.toJSON();
            let sellingPrice = parseFloat(cartItem.EsimPlan.sellingPrice);
            
            if (!sellingPrice && partner.markupPercentage) {
                const markup = 1 + (partner.markupPercentage / 100);
                sellingPrice = Number((cartItem.EsimPlan.buyingPrice * markup).toFixed(2));
            }

            if (!sellingPrice || sellingPrice <= 0) {
                throw new Error(`Invalid price for plan: ${cartItem.EsimPlan.name} (ID: ${cartItem.EsimPlan.id})`);
            }

            totalAmount += sellingPrice;
            return { ...cartItem, calculatedPrice: sellingPrice };
        });
        console.log('Total order amount calculated:', totalAmount);

        // Check wallet balance
        const wallet = await models.Wallet.findOne({
            where: { userId },
            transaction: t,
            lock: true
        });

        if (!wallet || parseFloat(wallet.balance) < totalAmount) {
            console.log('Insufficient balance. Required:', totalAmount, 'Available:', wallet?.balance);
            await t.rollback();
            return res.status(400).json({ 
                message: 'Insufficient wallet balance',
                required: totalAmount,
                available: parseFloat(wallet?.balance || 0)
            });
        }

        // Process orders
        const orders = [];
        const usedStockIds = [];
        
        for (const cartItem of processedItems) {
            const plan = cartItem.EsimPlan;
            const provider = plan.provider;

            try {
                let orderData = null;
                let stockData = null;

                // Handle external provider orders (API-based providers)
                if (provider && provider.type === 'API') {
                    const providerService = providerFactory.getProvider(provider.name);
                    
                    console.log('Creating external order for plan:', {
                        planId: plan.id,
                        externalProductId: plan.externalProductId,
                        externalSkuId: plan.externalSkuId
                    });

                    // Create initial order record with pending status
                    orderData = await models.Order.create({
                        userId,
                        esimPlanId: plan.id,
                        esimStockId: null,
                        quantity: cartItem.quantity,
                        orderTotal: cartItem.calculatedPrice,
                        startDate: items.find(i => i.id === cartItem.id)?.startDate || null,
                        status: 'pending',
                        walletAuthTransactionId: uuidv4(),
                        parentOrderId: cartItem.isTopUp ? cartItem.parentOrderId : null
                    }, { transaction: t });

                    // Step 1: Create external order
                    const orderPayload = {
                        productId: plan.externalProductId,
                        skuId: plan.externalSkuId,
                        quantity: cartItem.quantity,
                        customerReference: `order_${userId}_${Date.now()}`,
                        email: user.email,
                        totalAmount: cartItem.calculatedPrice
                    };

                    // For top-up orders, include parent order identifier from lineItemDetails
                    if (cartItem.isTopUp && cartItem.parentOrderId) {
                        // Find the parent order to get the identifier from its lineItemDetails
                        const parentOrder = await models.Order.findOne({
                            where: {
                                id: cartItem.parentOrderId,
                                userId: userId
                            }
                        });

                        if (parentOrder && parentOrder.externalOrderId) {
                            // For Mobimatter top-ups, addOnOrderIdentifier should be the external order ID
                            orderPayload.parentOrderId = parentOrder.externalOrderId;
                            console.log('Using parent external order ID for top-up:', parentOrder.externalOrderId);

                            // But the original order must have an identifier in its providerMetadata
                            // This is what Mobimatter checks to validate the top-up request
                            console.log('Parent order providerMetadata:', JSON.stringify(parentOrder.providerMetadata, null, 2));

                            if (!parentOrder.providerMetadata || !parentOrder.providerMetadata.identifier) {
                                console.warn('Parent order missing identifier in providerMetadata - top-up may fail');
                            }
                        } else {
                            console.warn('Parent order not found or missing external order ID:', cartItem.parentOrderId);
                        }
                    }

                    // Different flow for BillionConnect vs Mobimatter
                    let externalOrder = null;
                    let externalOrderId = null;

                    if (provider.name === 'Billionconnect') {
                        // Check if this is a top-up order
                        if (cartItem.isTopUp && cartItem.parentOrderId) {
                            // For BillionConnect top-up orders, skip F040 and use F007 API directly
                            // For BillionConnect top-up orders, use F007 API instead of regular order creation
                            const parentOrder = await models.Order.findOne({
                                where: {
                                    id: cartItem.parentOrderId,
                                    userId: userId
                                },
                                include: [{
                                    model: models.EsimStock,
                                    as: 'stock',
                                    attributes: ['iccid']
                                }]
                            });

                            if (!parentOrder || !parentOrder.stock?.iccid) {
                                throw new Error('Parent order not found or missing ICCID for Billionconnect top-up');
                            }

                            // Use F007 API for recharge order with unique channel order ID
                            // Append _topup suffix to avoid "Channel order ID already exists" error
                            const rechargeChannelOrderId = `${orderPayload.customerReference}_topup`;

                            // Determine the correct number of copies for recharge order from database metadata
                            let rechargeCopies = 1; // Default to 1 copy
                            let rechargeSkuId = plan.externalSkuId;

                            // Check if this plan has variant metadata stored in database
                            if (plan.providerMetadata && plan.providerMetadata.copies) {
                                // This is a variant plan with copies metadata from database
                                rechargeCopies = plan.providerMetadata.copies;
                                console.log(`Using database variant metadata for recharge: baseSkuId=${rechargeSkuId}, copies=${rechargeCopies}, actualDays=${plan.providerMetadata.actualDays || plan.validityDays}`);
                            } else {
                                // Regular plan without variant metadata
                                console.log(`Using regular plan for recharge: skuId=${rechargeSkuId}, copies=1`);
                            }

                            const rechargeOrder = await billionconnectService.createRechargeOrder({
                                channelOrderId: rechargeChannelOrderId,
                                iccidArray: [parentOrder.stock.iccid],
                                skuId: rechargeSkuId,
                                copies: rechargeCopies,
                                totalAmount: cartItem.calculatedPrice.toString(),
                                comment: `Top-up for order ${cartItem.parentOrderId}`
                            });

                            // Set external order ID for topup order
                            externalOrderId = rechargeOrder.orderId;

                            await orderData.update({
                                externalOrderId: rechargeOrder.orderId,
                                providerResponse: rechargeOrder.providerResponse,
                                providerMetadata: {
                                    iccid: parentOrder.stock.iccid,
                                    rechargeOrder: true
                                },
                                status: 'completed', // Set status to completed for topup orders
                                providerOrderStatus: 'ACTIVE', // Set to ACTIVE since recharge is immediate
                                lastProviderCheck: new Date()
                            }, { transaction: t });

                            console.log('BillionConnect top-up order created and completed:', {
                                orderId: orderData.id,
                                externalOrderId: rechargeOrder.orderId,
                                iccid: parentOrder.stock.iccid,
                                status: 'completed'
                            });

                            // Send email notifications for BillionConnect topup order
                            try {
                                console.log('Sending email notifications for BillionConnect topup order:', orderData.id);

                                // Get partner details
                                const partner = await models.User.findByPk(userId);
                                if (!partner) {
                                    throw new Error('Partner not found for email notification');
                                }

                                // Prepare order details for emails
                                const orderDetails = {
                                    order: {
                                        id: orderData.id,
                                        externalOrderId: rechargeOrder.orderId,
                                        parentOrderId: cartItem.parentOrderId
                                    },
                                    plan: plan,
                                    orderTotal: cartItem.calculatedPrice,
                                    startDate: orderData.startDate,
                                    quantity: cartItem.quantity,
                                    walletAuthTransactionId: orderData.walletAuthTransactionId,
                                    isTopUp: true,
                                    partner: {
                                        id: userId,
                                        firstName: partner.firstName,
                                        lastName: partner.lastName,
                                        email: partner.email
                                    },
                                    stock: {
                                        iccid: parentOrder.stock.iccid
                                    }
                                };

                                // Check if this is an API order by looking at the channelOrderId pattern
                                const isApiOrder = orderPayload.customerReference && orderPayload.customerReference.startsWith('api_');

                                // For topup orders, send regular emails to both partner and admin (not confirmation emails)
                                if (isApiOrder) {
                                    // Send API-specific emails with "(Via API)" in subject
                                    await emailService.sendApiPartnerOrderEmail(partner.email, orderDetails);
                                    console.log('BillionConnect API topup order email sent to partner:', partner.email);

                                    // Send admin notifications for API orders
                                    const admins = await models.User.findAll({
                                        where: {
                                            role: 'admin',
                                            isActive: true
                                        },
                                        attributes: ['email']
                                    });

                                    if (admins && admins.length > 0) {
                                        const adminEmails = admins.map(admin => admin.email);
                                        await emailService.sendApiAdminOrderEmail(adminEmails, orderDetails);
                                        console.log('BillionConnect API topup order admin notifications sent');
                                    }
                                } else {
                                    // Send regular emails for web orders
                                    await emailService.sendPartnerOrderEmail(partner.email, orderDetails);
                                    console.log('BillionConnect topup order email sent to partner:', partner.email);

                                    // Send admin notifications
                                    const admins = await models.User.findAll({
                                        where: {
                                            role: 'admin',
                                            isActive: true
                                        },
                                        attributes: ['email']
                                    });

                                    if (admins && admins.length > 0) {
                                        const adminEmails = admins.map(admin => admin.email);
                                        await emailService.sendAdminOrderEmail(adminEmails, orderDetails);
                                        console.log('BillionConnect topup order admin notifications sent');
                                    }
                                }
                            } catch (emailError) {
                                console.error('Error sending BillionConnect topup order emails:', emailError);
                                // Don't fail the order creation due to email errors
                            }

                        } else {
                            // For regular BillionConnect orders, create F040 order and wait for N009 webhook
                            // Pass plan data for variant information extraction
                            externalOrder = await providerService.createOrder(orderPayload, plan);

                            if (!externalOrder || !externalOrder.orderId) {
                                throw new Error('Invalid response from provider: Missing order ID');
                            }

                            // Set external order ID for regular order
                            externalOrderId = externalOrder.orderId;

                            await orderData.update({
                                externalOrderId: externalOrder.orderId,
                                providerResponse: externalOrder.providerResponse,
                                providerMetadata: {},
                                providerOrderStatus: 'PENDING',
                                lastProviderCheck: new Date()
                            }, { transaction: t });

                            console.log('BillionConnect order created, waiting for webhook:', {
                                orderId: orderData.id,
                                externalOrderId: externalOrderId
                            });
                        }

                        // Get plan details for response (needed for both regular and topup orders)
                        const planDetails = await models.EsimPlan.findByPk(orderData.esimPlanId, {
                            include: [{
                                model: models.Provider,
                                as: 'provider',
                                attributes: ['id', 'name', 'type', 'country']
                            }]
                        });

                        // Send initial order confirmation email for BillionConnect (skip for topup orders)
                        if (!cartItem.isTopUp) {

                            const initialOrderDetails = {
                                order: {
                                    id: orderData.id,
                                    externalOrderId: externalOrderId
                                },
                                plan: planDetails.get({ plain: true }),
                                orderTotal: orderData.orderTotal,
                                startDate: orderData.startDate,
                                quantity: orderData.quantity,
                                walletAuthTransactionId: orderData.walletAuthTransactionId,
                                partner: {
                                    id: partner.id,
                                    firstName: partner.firstName,
                                    lastName: partner.lastName,
                                    email: partner.email
                                }
                            };

                            try {
                                await emailService.sendBillionConnectInitialOrderEmail(partner.email, initialOrderDetails);
                                console.log('BillionConnect initial order email sent to:', partner.email);
                            } catch (error) {
                                console.error('Failed to send BillionConnect initial email:', error);
                                // Don't fail the order creation due to email error
                            }
                        } else {
                            console.log('Skipping initial confirmation email for BillionConnect topup order:', orderData.id);
                        }

                        // Note: For BillionConnect, we don't create the stock record here
                        // Stock record will be created when we receive the N009 webhook
                        stockData = null;

                        // For BillionConnect, we need to process wallet transaction before returning
                        // Process wallet transaction for BillionConnect orders
                        try {
                            const newBalance = parseFloat(wallet.balance) - totalAmount;
                            await models.WalletTransaction.create({
                                id: uuidv4(),
                                walletId: wallet.id,
                                type: 'debit',
                                amount: totalAmount,
                                balance: newBalance,
                                description: `Payment for order: ${orderData.id}`,
                                status: 'completed',
                                referenceType: 'order',
                                referenceId: orderData.id,
                                metadata: {
                                    orders: [orderData.id],
                                    totalAmount,
                                    provider: 'Billionconnect'
                                }
                            }, { transaction: t });

                            // Update wallet balance
                            await wallet.update({
                                balance: newBalance,
                                updatedAt: new Date()
                            }, { transaction: t });

                            console.log('BillionConnect wallet transaction processed');
                        } catch (walletError) {
                            console.error('Error processing BillionConnect wallet transaction:', walletError);

                            // Try to cancel the external order that was created (only for regular orders, not topups)
                            if (externalOrder && externalOrderId) {
                                try {
                                    const providerService = providerFactory.getProvider(provider.name);
                                    await providerService.cancelOrder(externalOrderId);
                                } catch (cancelError) {
                                    console.error(`Error cancelling BillionConnect order ${externalOrderId}:`, cancelError);
                                }
                            }

                            await t.rollback();
                            return res.status(500).json({
                                message: 'Error processing wallet transaction for BillionConnect order',
                                error: walletError.message
                            });
                        }

                        // Clear cart for BillionConnect orders
                        await models.Cart.destroy({
                            where: { userId },
                            transaction: t
                        });

                        // Skip regular email flow for BillionConnect orders
                        await t.commit();
                        console.log('BillionConnect order created successfully, waiting for webhook');

                        // Trigger notification processor to handle any pending notifications
                        // This helps with race conditions where webhook arrives before order creation
                        const notificationProcessor = require('../services/notificationProcessor.service');
                        notificationProcessor.triggerProcessing(1000); // Small delay to ensure order is fully committed

                        return res.status(201).json({
                            message: 'Orders created successfully',
                            orders: [orderData.id],
                            orderDetails: [{
                                id: orderData.id,
                                status: orderData.status,
                                orderTotal: orderData.orderTotal,
                                externalOrderId: externalOrderId,
                                plan: planDetails.get({ plain: true })
                            }]
                        });
                    } else if (provider.name.toLowerCase() === 'mobimatter') {
                        // For Mobimatter, create external order first
                        externalOrder = await providerService.createOrder(orderPayload);

                        if (!externalOrder || !externalOrder.orderId) {
                            throw new Error('Invalid response from provider: Missing order ID');
                        }

                        externalOrderId = externalOrder.orderId;

                        // For Mobimatter, continue with existing flow
                        const orderInfo = await providerService.getOrderStatus(externalOrder.orderId);
                        
                        if (!orderInfo) {
                            throw new Error('Failed to fetch order status');
                        }

                        if (orderInfo.status === 'FAILED' || orderInfo.orderState === 'Failed') {
                            throw new Error(`External order failed: ${orderInfo.errorMessage || 'Unknown error'}`);
                        }

                        // Update order with external order details (initial status)
                        await orderData.update({
                            externalOrderId: externalOrder.orderId,
                            providerResponse: orderInfo,
                            providerMetadata: {
                                activationCode: orderInfo.activationCode,
                                qrCodeUrl: orderInfo.qrCodeUrl,
                                iccid: orderInfo.iccid,
                                smdpAddress: orderInfo.smdpAddress,
                                lpaString: orderInfo.qrCodeUrl,
                                apn: orderInfo.apn,
                                providerStatus: orderInfo.orderState
                            },
                            providerOrderStatus: orderInfo.orderState,
                            lastProviderCheck: new Date()
                        }, { transaction: t });

                        // Complete the Mobimatter order
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        const completedOrder = await providerService.completeOrder(externalOrder.orderId);
                        
                        if (!completedOrder) {
                            throw new Error('No response received from order completion');
                        }

                        // Extract identifier from completed order lineItemDetails for top-up support
                        const completedLineItems = completedOrder.orderLineItem?.lineItemDetails || [];
                        const identifier = completedLineItems.find(item =>
                            item.name === 'IDENTIFIER' ||
                            item.name === 'ORDER_IDENTIFIER' ||
                            item.name === 'ICCID'
                        )?.value;

                        console.log('Completed order lineItemDetails:', completedLineItems);
                        console.log('Extracted identifier for top-up support:', identifier);

                        // Update order with completed status and identifier
                        await orderData.update({
                            status: 'completed',
                            providerResponse: completedOrder,
                            providerOrderStatus: completedOrder.orderState,
                            lastProviderCheck: new Date(),
                            // Update providerMetadata to include identifier
                            providerMetadata: {
                                ...orderData.providerMetadata,
                                identifier: identifier, // Store identifier for top-up support
                                providerStatus: completedOrder.orderState
                            }
                        }, { transaction: t });

                        // Extract eSIM details from the completed order response
                        const lineItems = completedOrder.orderLineItem?.lineItemDetails || [];
                        const getLineItemValue = (name) => lineItems.find(item => item.name === name)?.value || '';

                        // Create stock record for Mobimatter (but NOT for top-up orders)
                        if (!cartItem.isTopUp) {
                            stockData = {
                                id: uuidv4(),
                                esimPlanId: plan.id,
                                orderId: orderData.id,
                                iccid: getLineItemValue('ICCID'),
                                smdpAddress: getLineItemValue('SMDP_ADDRESS'),
                                lpaString: getLineItemValue('LOCAL_PROFILE_ASSISTANT'),
                                accessPointName: getLineItemValue('ACCESS_POINT_NAME'),
                                activationCode: getLineItemValue('ACTIVATION_CODE'),
                                phoneNumber: getLineItemValue('PHONE_NUMBER'),
                                qrCodeUrl: getLineItemValue('QR_CODE'),
                                confCode: getLineItemValue('CONF_CODE'),
                                status: 'assigned',
                                externalStockId: completedOrder.orderId,
                                externalIccid: getLineItemValue('ICCID'),
                                providerStatus: completedOrder.orderState,
                                providerMetadata: completedOrder,
                                walletAuthTransactionId: orderData.walletAuthTransactionId
                            };
                        } else {
                            console.log('Skipping stock creation for top-up order');
                            stockData = null;
                        }
                    } else {
                        // For other providers, create external order
                        externalOrder = await providerService.createOrder(orderPayload);

                        if (!externalOrder || !externalOrder.orderId) {
                            throw new Error('Invalid response from provider: Missing order ID');
                        }

                        externalOrderId = externalOrder.orderId;

                        // Update order with external order details
                        await orderData.update({
                            externalOrderId: externalOrder.orderId,
                            providerResponse: externalOrder,
                            providerOrderStatus: 'PENDING',
                            lastProviderCheck: new Date()
                        }, { transaction: t });

                        // For now, throw error as we don't have specific handling for other providers
                        throw new Error(`Provider ${provider.name} not fully implemented yet`);
                    }

                    // Save stock record if we have it
                    if (stockData) {
                        const stock = await models.EsimStock.create(stockData, { transaction: t });

                        // Create stock history - check if one already exists first
                        const existingStockHistory = await models.EsimPlanStockHistory.findOne({
                            where: { orderId: orderData.id },
                            transaction: t
                        });

                        if (!existingStockHistory) {
                            try {
                                await models.EsimPlanStockHistory.create({
                                    id: uuidv4(),
                                    esimPlanId: stock.esimPlanId,
                                    esimStockId: stock.id,
                                    iccid: stock.iccid,
                                    smdpAddress: stock.smdpAddress,
                                    lpaString: stock.lpaString,
                                    accessPointName: stock.accessPointName,
                                    activationCode: stock.activationCode,
                                    phoneNumber: stock.phoneNumber,
                                    orderId: orderData.id,
                                    orderDate: new Date(),
                                    quantity: 1,
                                    status: 'assigned',
                                    reason: 'External provider order',
                                    createdBy: userId,
                                    createdAt: new Date(),
                                    updatedAt: new Date()
                                }, { transaction: t });
                            } catch (error) {
                                if (error.name === 'SequelizeUniqueConstraintError') {
                                    console.log(`Stock history already exists for order ${orderData.id}, skipping creation`);
                                } else {
                                    throw error;
                                }
                            }
                        }
                    }

                    // Get plan details with provider info
                    const planDetails = await models.EsimPlan.findByPk(orderData.esimPlanId, {
                        include: [{
                            model: models.Provider,
                            as: 'provider',
                            attributes: ['id', 'name', 'type', 'country']
                        }]
                    });

                    if (!planDetails) {
                        throw new Error('Plan details not found');
                    }

                    // For top-up orders, get ICCID from parent order
                    let topUpIccid = null;
                    if (cartItem.isTopUp && cartItem.parentOrderId) {
                        const parentOrder = await models.Order.findOne({
                            where: { id: cartItem.parentOrderId },
                            include: [
                                {
                                    model: models.EsimStock,
                                    as: 'stock',
                                    attributes: ['iccid']
                                },
                                {
                                    model: models.EsimPlanStockHistory,
                                    as: 'stockHistory',
                                    attributes: ['iccid']
                                }
                            ]
                        });

                        if (parentOrder) {
                            // Get ICCID from stock, stockHistory, or providerMetadata
                            topUpIccid = parentOrder.stock?.iccid ||
                                        parentOrder.stockHistory?.iccid ||
                                        parentOrder.providerMetadata?.iccid;
                        }
                    }

                    // Prepare order details in the format expected by email service
                    const orderDetails = {
                        order: {
                            id: orderData.id,
                            externalOrderId: stockData?.externalStockId,
                            parentOrderId: orderData.parentOrderId // Include parent order ID for top-up orders
                        },
                        plan: planDetails.get({ plain: true }),
                        orderTotal: orderData.orderTotal,
                        startDate: orderData.startDate,
                        quantity: orderData.quantity,
                        walletAuthTransactionId: orderData.walletAuthTransactionId,
                        qrCode: stockData?.qrCodeUrl,
                        isTopUp: cartItem.isTopUp || false, // Flag for top-up orders
                        partner: {
                            id: partner.id,
                            firstName: partner.firstName,
                            lastName: partner.lastName,
                            email: partner.email
                        },
                        stock: {
                            iccid: cartItem.isTopUp ? topUpIccid : stockData?.iccid,
                            smdpAddress: stockData?.smdpAddress,
                            lpaString: stockData?.lpaString,
                            accessPointName: stockData?.accessPointName,
                            activationCode: stockData?.activationCode,
                            phoneNumber: stockData?.phoneNumber,
                            qrCodeUrl: stockData?.qrCodeUrl,
                            confCode: stockData?.confCode
                        }
                    };

                    // Send partner notification (skip for BillionConnect as they already received initial email)
                    if (planDetails.provider?.name !== 'Billionconnect') {
                        console.log('Sending partner notification to:', partner.email);
                        try {
                            await emailService.sendPartnerOrderEmail(partner.email, orderDetails);
                            console.log('Partner notification sent successfully');
                        } catch (error) {
                            console.error('Failed to send partner notification:', error);
                            // Update order metadata with email error
                            await orderData.update({
                                providerMetadata: {
                                    ...orderData.providerMetadata,
                                    emailError: {
                                        message: error.message,
                                        timestamp: new Date().toISOString()
                                    }
                                }
                            }, { transaction: t });
                            throw new Error('Failed to send order confirmation email');
                        }
                    } else {
                        console.log('Skipping partner notification for BillionConnect order (initial email already sent)');
                    }

                    // Send admin notifications (skip for BillionConnect as they will be sent when webhook completes)
                    if (planDetails.provider?.name !== 'Billionconnect') {
                        const admins = await models.User.findAll({
                            where: {
                                role: 'admin',
                                isActive: true
                            },
                            attributes: ['email'],
                            transaction: t
                        });

                        if (admins && admins.length > 0) {
                            const adminEmails = admins.map(admin => admin.email);
                            console.log('Sending admin notifications to:', adminEmails);

                            try {
                                await emailService.sendAdminOrderEmail(adminEmails, orderDetails);
                                console.log('Admin notifications sent successfully');
                            } catch (error) {
                                console.error('Failed to send admin notifications:', error);
                                // Update order metadata with admin email error
                                await orderData.update({
                                    providerMetadata: {
                                        ...orderData.providerMetadata,
                                        adminEmailError: {
                                            message: error.message,
                                            timestamp: new Date().toISOString()
                                        }
                                    }
                                }, { transaction: t });
                                // Don't throw here, as partner notification was successful
                            }
                        }
                    } else {
                        console.log('Skipping admin notifications for BillionConnect order (will be sent when webhook completes)');
                    }

                    console.log('Email notifications sent successfully for order:', orderData.id);
                } else {
                    // Handle local stock order - FIFO (First In, First Out)
                    const stock = await models.EsimStock.findOne({
                        where: {
                            esimPlanId: plan.id,
                            status: 'available'
                        },
                        order: [['createdAt', 'ASC']], // FIFO: oldest stock first
                        transaction: t,
                        lock: true
                    });

                    if (!stock) {
                        throw new Error(`No stock available for plan: ${plan.name}`);
                    }

                    // Create order record
                    orderData = await models.Order.create({
                        userId,
                        esimPlanId: plan.id,
                        esimStockId: stock.id,
                        quantity: cartItem.quantity,
                        orderTotal: cartItem.calculatedPrice,
                        startDate: items.find(i => i.id === cartItem.id)?.startDate || null,
                        status: 'completed',
                        walletAuthTransactionId: uuidv4(),
                        providerResponse: stock.providerMetadata || null,
                        providerMetadata: {
                            iccid: stock.iccid,
                            smdpAddress: stock.smdpAddress,
                            lpaString: stock.lpaString,
                            accessPointName: stock.accessPointName,
                            activationCode: stock.activationCode,
                            phoneNumber: stock.phoneNumber,
                            qrCodeUrl: stock.qrCodeUrl,
                            providerStatus: stock.providerStatus
                        }
                    }, { transaction: t });

                    // Create stock history - check if one already exists first
                    const existingStockHistory = await models.EsimPlanStockHistory.findOne({
                        where: { orderId: orderData.id },
                        transaction: t
                    });

                    if (!existingStockHistory) {
                        try {
                            await models.EsimPlanStockHistory.create({
                                id: uuidv4(),
                                esimPlanId: stock.esimPlanId,
                                esimStockId: stock.id,
                                iccid: stock.iccid,
                                smdpAddress: stock.smdpAddress,
                                lpaString: stock.lpaString,
                                accessPointName: stock.accessPointName,
                                activationCode: stock.activationCode,
                                phoneNumber: stock.phoneNumber,
                                orderId: orderData.id,
                                orderDate: new Date(),
                                quantity: 1,
                                status: 'assigned',
                                reason: 'Local stock order',
                                createdBy: userId,
                                createdAt: new Date(),
                                updatedAt: new Date()
                            }, { transaction: t });
                        } catch (error) {
                            if (error.name === 'SequelizeUniqueConstraintError') {
                                console.log(`Stock history already exists for order ${orderData.id}, skipping creation`);
                            } else {
                                throw error;
                            }
                        }
                    }

                    // Delete used stock
                    //    await models.EsimStock.destroy({
                    //     where: { id: stock.id },
                    //     transaction: t
                    // });

                    // Update stock status instead of deleting
                    await models.EsimStock.update(
                        {
                            status: 'assigned',
                            orderId: orderData.id,
                            orderDate: new Date(),
                            updatedAt: new Date()
                        },
                        {
                            where: { id: stock.id },
                            transaction: t
                        }
                    );

                    usedStockIds.push(stock.id);
                }

                orders.push(orderData.id);
            } catch (error) {
                console.error('Error processing order item:', error);
                throw error;
            }
        }

        // Process wallet transaction
        try {
            const newBalance = parseFloat(wallet.balance) - totalAmount;
            await models.WalletTransaction.create({
                id: uuidv4(),
                walletId: wallet.id,
                type: 'debit',
                amount: totalAmount,
                balance: newBalance,
                description: `Payment for order: ${orders.join(', ')}`,
                status: 'completed',
                referenceType: 'order',
                referenceId: orders[0],
                metadata: {
                    orders,
                    totalAmount,
                    usedStockIds
                }
            }, { transaction: t });

            // Update wallet balance
            await wallet.update({
                balance: newBalance,
                updatedAt: new Date()
            }, { transaction: t });

            console.log('Wallet transaction processed');
        } catch (error) {
            console.error('Error processing wallet transaction:', error);
            
            // Try to cancel any external orders that were created
            for (const orderId of orders) {
                try {
                    const order = await models.Order.findByPk(orderId, {
                        include: [{
                            model: models.EsimPlan,
                            as: 'plan',
                            include: [{
                                model: models.Provider,
                                as: 'provider'
                            }]
                        }]
                    });

                    if (order?.plan?.provider?.type === 'API' && order.externalOrderId) {
                        const providerService = providerFactory.getProvider(order.plan.provider.name);
                        await providerService.cancelOrder(order.externalOrderId);
                    }
                } catch (cancelError) {
                    console.error(`Error cancelling order ${orderId}:`, cancelError);
                }
            }
            
            await t.rollback();
            return res.status(500).json({ 
                message: 'Error processing wallet transaction',
                error: error.message 
            });
        }

        // Clear cart
        await models.Cart.destroy({
            where: { userId },
            transaction: t
        });

        console.log('Cart cleared');

        // Check stock levels
        for (const cartItem of processedItems) {
            await checkStockAndNotify(cartItem.esimPlanId, t);
        }

        console.log('Committing transaction...');
        // Commit transaction only after all operations are successful
        await t.commit();
        console.log('Transaction committed successfully');

        // Get all admin users for notification
        const admins = await models.User.findAll({
            where: { role: 'admin', isActive: true },
            attributes: ['email']
        });

        // Send notifications for each order
        for (const orderId of orders) {
            try {
                const order = await models.Order.findOne({
                    where: { id: orderId },
                    include: [
                        {
                            model: models.EsimPlan,
                            as: 'plan',
                            include: [
                                {
                                    model: models.Provider,
                                    as: 'provider',
                                    attributes: ['id', 'name', 'type', 'country']
                                }
                            ]
                        },
                        {
                            model: models.EsimPlanStockHistory,
                            as: 'stockHistory',
                            attributes: ['id', 'iccid', 'smdpAddress', 'lpaString', 'accessPointName', 'activationCode', 'phoneNumber', 'status', 'orderDate', 'quantity', 'reason']
                        }
                    ]
                });

                if (order) {
                    console.log('Processing order for notifications:', orderId);
                    console.log('Provider type:', order.plan?.provider?.name);
                    
                    // Skip Mobimatter orders as they've already been notified
                    if (order.plan?.provider?.name === 'Mobimatter') {
                        console.log('Skipping notification for Mobimatter order as it was already sent');
                        continue;
                    }

                    console.log('Provider metadata:', order.providerMetadata);

                    // Get eSIM details based on provider type
                    let esimDetails = {};
                    let qrCode = null;

                    // Process local stock order details
                    console.log('Processing local stock order details');
                    esimDetails = {
                        iccid: order.stockHistory?.iccid,
                        phoneNumber: order.stockHistory?.phoneNumber,
                        lpaString: order.stockHistory?.lpaString,
                        smdpAddress: order.stockHistory?.smdpAddress,
                        accessPointName: order.stockHistory?.accessPointName,
                        activationCode: order.stockHistory?.activationCode
                    };
                    if (esimDetails.lpaString) {
                        // Generate QR code from the LPA string if available
                        try {
                            qrCode = await generateQRCode(esimDetails.lpaString);
                            console.log('QR code generated successfully from LPA string');
                        } catch (error) {
                            console.error('Error generating QR code:', error);
                            // Try using QR_CODE value as fallback
                            const qrCodeValue = esimDetails.qrCodeUrl;
                            if (qrCodeValue) {
                                try {
                                    qrCode = await generateQRCode(qrCodeValue);
                                    console.log('QR code generated successfully from QR_CODE value');
                                } catch (fallbackError) {
                                    console.error('Error generating QR code from fallback:', fallbackError);
                                }
                            }
                        }
                    }
                    console.log('Local stock eSIM details:', esimDetails);

                    // Prepare order details for emails
                    const orderDetails = {
                        order: {
                            id: orderId,
                            externalOrderId: order.externalOrderId,
                            parentOrderId: order.parentOrderId // Include parent order ID for top-up orders
                        },
                        plan: order.plan,
                        orderTotal: order.orderTotal,
                        startDate: order.startDate,
                        quantity: order.quantity,
                        walletAuthTransactionId: order.walletAuthTransactionId,
                        qrCode,
                        isTopUp: order.parentOrderId ? true : false, // Flag for top-up orders
                        partner: {
                            id: userId,
                            firstName: partner.firstName,
                            lastName: partner.lastName,
                            email: partner.email
                        },
                        stock: esimDetails
                    };

                    console.log('Sending email notifications for local stock order:', orderId);
                    console.log('Partner email:', partner.email);

                    try {
                        // Send partner email with QR code
                        await emailService.sendPartnerOrderEmail(partner.email, orderDetails);
                        console.log('Partner email sent successfully');

                        // Send admin notification for all orders
                        if (admins.length > 0) {
                            const adminEmails = admins.map(admin => admin.email);
                            console.log('Sending admin notifications to:', adminEmails);
                            await emailService.sendAdminOrderEmail(adminEmails, orderDetails);
                            console.log('Admin emails sent successfully');
                        }
                    } catch (error) {
                        console.error('Error sending order notifications:', error);
                        // Log more details about the error
                        console.error('Order details:', JSON.stringify(orderDetails, null, 2));
                        console.error('Error details:', error.response?.data || error.message);
                        // Don't throw error as this is a notification service
                    }
                } else {
                    console.error('Order not found:', orderId);
                }
            } catch (error) {
                console.error(`Error sending notifications for order ${orderId}:`, error);
                // Don't throw error as this is a notification service
            }
        }

        // Return success response with order IDs and details
        return res.status(201).json({
            message: 'Orders created successfully',
            orders: orders,
            orderDetails: await Promise.all(orders.map(async (orderId) => {
                const order = await models.Order.findByPk(orderId, {
                    include: [
                        {
                            model: models.EsimPlan,
                            as: 'plan'
                        },
                        {
                            model: models.EsimPlanStockHistory,
                            as: 'stockHistory',
                            attributes: ['id', 'iccid', 'smdpAddress', 'lpaString', 'accessPointName', 'activationCode', 'phoneNumber', 'status', 'orderDate', 'quantity', 'reason']
                        }
                    ]
                });
                return order;
            }))
        });

    } catch (error) {
        console.error('Error in order creation:', error);
        await t.rollback();
        return res.status(500).json({ 
            message: 'Error creating order',
            error: error.message 
        });
    }
};

// Get all orders for the current user
exports.getUserOrders = async (req, res) => {
    try {
        const userId = req.user.id;
        const orders = await models.Order.findAll({
            where: { userId },
            include: [
                {
                model: models.EsimPlan,
                as: 'plan',
                    include: [{
                        model: models.Provider,
                        as: 'provider',
                        attributes: ['id', 'name', 'type', 'country']
                    }]
                },
                {
                    model: models.EsimStock,
                    as: 'stock',
                    attributes: ['id', 'iccid', 'smdpAddress', 'lpaString', 'accessPointName', 'activationCode', 'phoneNumber', 'status', 'providerMetadata']
                },
                {
                    model: models.EsimPlanStockHistory,
                    as: 'stockHistory',
                    attributes: ['id', 'iccid', 'smdpAddress', 'lpaString', 'accessPointName', 'activationCode', 'phoneNumber', 'status', 'orderDate', 'quantity', 'reason']
                }
            ],
            order: [['createdAt', 'DESC']]
        });

        // Format the response with QR codes
        const formattedOrders = await Promise.all(orders.map(async (order) => {
            const orderJson = order.toJSON();
            
            // Prepare eSIM details based on order type
            const esimDetails = {
                // For MobiMatter orders, use providerMetadata
                ...(orderJson.providerMetadata && {
                    iccid: orderJson.providerMetadata.iccid,
                    activationCode: orderJson.providerMetadata.activationCode,
                    qrCodeUrl: orderJson.providerMetadata.qrCodeUrl,
                    smdpAddress: orderJson.providerMetadata.smdpAddress,
                    lpaString: orderJson.providerMetadata.lpaString || orderJson.providerMetadata.qrCodeUrl,
                    accessPointName: orderJson.providerMetadata.apn,
                    phoneNumber: orderJson.providerMetadata.phoneNumber,
                    matchingId: orderJson.providerMetadata.matchingId,
                    profileState: orderJson.providerMetadata.profileState,
                    providerStatus: orderJson.providerMetadata.providerStatus
                }),
                // For local stock orders, use stock or stockHistory
                ...(orderJson.stock && {
                    iccid: orderJson.stock.iccid,
                    activationCode: orderJson.stock.activationCode,
                    smdpAddress: orderJson.stock.smdpAddress,
                    lpaString: orderJson.stock.lpaString,
                    accessPointName: orderJson.stock.accessPointName,
                    phoneNumber: orderJson.stock.phoneNumber,
                    providerMetadata: orderJson.stock.providerMetadata
                }),
                // Fallback to stockHistory if stock is not available
                ...(!orderJson.stock && orderJson.stockHistory && {
                    iccid: orderJson.stockHistory.iccid,
                    activationCode: orderJson.stockHistory.activationCode,
                    smdpAddress: orderJson.stockHistory.smdpAddress,
                    lpaString: orderJson.stockHistory.lpaString,
                    accessPointName: orderJson.stockHistory.accessPointName,
                    phoneNumber: orderJson.stockHistory.phoneNumber
                })
            };

            // Generate QR code if we have lpaString
            let qrCode = null;
            const lpaString = esimDetails.lpaString || esimDetails.qrCodeUrl;
            if (lpaString) {
                qrCode = await generateQRCode(lpaString);
            }

            return {
                id: orderJson.id,
                parentOrderId: orderJson.parentOrderId, // Include parentOrderId for topup order identification
                plan: orderJson.plan,
                orderTotal: orderJson.orderTotal,
                startDate: orderJson.startDate,
                validTime: orderJson.validTime,
                status: orderJson.status,
                createdAt: orderJson.createdAt,
                quantity: orderJson.quantity,
                // Include usage data fields
                dataUsage: orderJson.dataUsage,
                dataAllowance: orderJson.dataAllowance,
                usageStatus: orderJson.usageStatus,
                expiryDate: orderJson.expiryDate,
                usageMessage: orderJson.usageMessage,
                lastUsageCheck: orderJson.lastUsageCheck,
                usageData: orderJson.usageData,
                providerResponse: orderJson.providerResponse,
                providerMetadata: orderJson.providerMetadata,
                esimDetails,
                qrCode
            };
        }));

        return res.json(formattedOrders);
    } catch (error) {
        console.error('Error fetching orders:', error);
        return res.status(500).json({ 
            message: 'Failed to fetch orders',
            error: error.message 
        });
    }
};

// Get a specific order by ID
exports.getOrderById = async (req, res) => {
    try {
        const id = req.params.id;
        
        if (!id || typeof id !== 'string' || !id.startsWith('VLZ')) {
            return res.status(400).json({ 
                message: 'Invalid order ID format',
                details: 'Order ID must be in the format VLZxxxx'
            });
        }

        const order = await models.Order.findOne({
            where: { id },
            include: [
                {
                    model: models.EsimPlan,
                    as: 'plan',
                    include: [{
                        model: models.Provider,
                        as: 'provider',
                        attributes: ['id', 'name', 'type', 'country']
                    }]
                },
                {
                    model: models.EsimStock,
                    as: 'stock',
                    attributes: [
                        'id', 'iccid', 'smdpAddress', 'lpaString', 
                        'accessPointName', 'activationCode', 'phoneNumber', 
                        'status', 'providerMetadata'
                    ]
                },
                {
                    model: models.EsimPlanStockHistory,
                    as: 'stockHistory',
                    attributes: [
                        'id', 'iccid', 'smdpAddress', 'lpaString', 
                        'accessPointName', 'activationCode', 'phoneNumber',
                        'status', 'orderDate', 'quantity', 'reason'
                    ]
                }
            ]
        });

        if (!order) {
            return res.status(404).json({
                message: 'Order not found'
            });
        }

        // Extract eSIM details from either Mobimatter response or local stock
        let esimDetails = {};
        
        if (order.plan?.provider?.name === 'Mobimatter' && order.providerResponse?.orderLineItem?.lineItemDetails) {
            const lineItems = order.providerResponse.orderLineItem.lineItemDetails;
            esimDetails = {
                iccid: lineItems.find(item => item.name === 'ICCID')?.value,
                smdpAddress: lineItems.find(item => item.name === 'SMDP_ADDRESS')?.value,
                lpaString: lineItems.find(item => item.name === 'LOCAL_PROFILE_ASSISTANT')?.value,
                accessPointName: lineItems.find(item => item.name === 'ACCESS_POINT_NAME')?.value,
                activationCode: lineItems.find(item => item.name === 'ACTIVATION_CODE')?.value,
                phoneNumber: lineItems.find(item => item.name === 'PHONE_NUMBER')?.value,
                qrCodeUrl: lineItems.find(item => item.name === 'QR_CODE')?.value,
                confCode: lineItems.find(item => item.name === 'CONF_CODE')?.value,
                walletAuthTransactionId: lineItems.find(item => item.name === 'WALLET_AUTH_TRANSACTION_ID')?.value
            };
        } else {
            // For local orders and BillionConnect orders, use stock, stockHistory, or providerMetadata data
            esimDetails = {
                iccid: order.stock?.iccid || order.stockHistory?.iccid || order.providerMetadata?.iccid,
                smdpAddress: order.stock?.smdpAddress || order.stockHistory?.smdpAddress || order.providerMetadata?.smdpAddress,
                lpaString: order.stock?.lpaString || order.stockHistory?.lpaString || order.providerMetadata?.lpaString,
                accessPointName: order.stock?.accessPointName || order.stockHistory?.accessPointName || order.providerMetadata?.accessPointName,
                activationCode: order.stock?.activationCode || order.stockHistory?.activationCode || order.providerMetadata?.activationCode,
                phoneNumber: order.stock?.phoneNumber || order.stockHistory?.phoneNumber || order.providerMetadata?.phoneNumber,
                qrCodeUrl: null
            };
        }

        // Generate QR code if we have lpaString but no qrCodeUrl
        if (!esimDetails.qrCodeUrl && esimDetails.lpaString) {
            try {
                esimDetails.qrCodeUrl = await generateQRCode(esimDetails.lpaString);
            } catch (error) {
                console.error('Error generating QR code:', error);
            }
        }

        // Format the response
        const formattedOrder = {
            id: order.id,
            externalOrderId: order.externalOrderId,
            parentOrderId: order.parentOrderId, // Include parent order ID for top-up orders
            status: order.status,
            orderTotal: order.orderTotal,
            quantity: order.quantity,
            startDate: order.startDate,
            validTime: order.validTime,
            createdAt: order.createdAt,
            updatedAt: order.updatedAt,
            // Include usage data fields
            dataUsage: order.dataUsage,
            dataAllowance: order.dataAllowance,
            usageStatus: order.usageStatus,
            expiryDate: order.expiryDate,
            usageMessage: order.usageMessage,
            lastUsageCheck: order.lastUsageCheck,
            usageData: order.usageData,
            plan: order.plan ? {
                id: order.plan.id,
                name: order.plan.name,
                activationPolicy: order.plan.activationPolicy,
                top_up: order.plan.top_up, // Include top-up availability
                provider: order.plan.provider ? {
                    id: order.plan.provider.id,
                    name: order.plan.provider.name,
                    type: order.plan.provider.type
            } : null
            } : null,
            stock: {
                ...esimDetails,
                status: order.stock?.status || order.stockHistory?.status
            },
            providerDetails: {
                orderStatus: order.providerOrderStatus,
                errorMessage: order.providerErrorMessage,
                lastCheck: order.lastProviderCheck,
                response: order.providerResponse,
                metadata: order.providerMetadata
            }
        };

        return res.json(formattedOrder);

    } catch (error) {
        console.error('Error fetching order:', error);
        return res.status(500).json({ 
            message: 'Failed to fetch order',
            error: error.message 
        });
    }
};

// Get all orders (admin only)
exports.getAllOrders = async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;
        const search = req.query.search?.toLowerCase() || '';

        // Build search conditions
        const searchConditions = search ? {
            [Op.or]: [
                sequelize.where(
                    sequelize.fn('LOWER', sequelize.col('user.firstName')),
                    { [Op.like]: `%${search}%` }
                ),
                sequelize.where(
                    sequelize.fn('LOWER', sequelize.col('user.lastName')),
                    { [Op.like]: `%${search}%` }
                ),
                sequelize.where(
                    sequelize.fn('LOWER', sequelize.col('user.email')),
                    { [Op.like]: `%${search}%` }
                ),
                sequelize.where(
                    sequelize.fn('LOWER', sequelize.col('plan.name')),
                    { [Op.like]: `%${search}%` }
                ),
                sequelize.where(
                    sequelize.fn('LOWER', sequelize.col('Order.status')),
                    { [Op.like]: `%${search}%` }
                ),
                {
                    id: { [Op.like]: `%${search}%` }
                }
            ]
        } : {};

        // Get orders with count
        const { count, rows: orders } = await models.Order.findAndCountAll({
            where: searchConditions,
            include: [
                {
                    model: models.User,
                    as: 'user',
                    attributes: ['id', 'firstName', 'lastName', 'email']
                },
                {
                    model: models.EsimPlan,
                    as: 'plan',
                    attributes: ['name']
                },
                {
                    model: models.EsimPlanStockHistory,
                    as: 'stockHistory',
                    attributes: ['iccid']
                }
            ],
            order: [['createdAt', 'DESC']],
            limit,
            offset,
            distinct: true
        });

        // Format orders for response
        const formattedOrders = orders.map(order => {
            const json = order.toJSON();
            return {
                id: json.id,
                customer: `${json.user?.firstName} ${json.user?.lastName}`.trim() || 'N/A',
                customerEmail: json.user?.email || 'N/A',
                plan: json.plan,
                iccid: json.stockHistory?.iccid || json.providerMetadata?.iccid || 'N/A',
                quantity: 1,
                orderTotal: json.orderTotal,
                status: json.status,
                createdAt: json.createdAt
            };
        });

        res.json({
            orders: formattedOrders,
            totalPages: Math.ceil(count / limit),
            currentPage: page,
            totalCount: count
        });
    } catch (error) {
        console.error('Error fetching orders:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};

// Get orders by user ID (admin only)
exports.getOrdersByUserId = async (req, res) => {
    try {
        const { userId } = req.params;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;

        const { count, rows: orders } = await models.Order.findAndCountAll({
            where: { userId },
            include: [{
                model: models.EsimPlan,
                as: 'plan',
                attributes: ['id', 'name', 'description', 'planData', 'planType', 'planDataUnit', 'validityDays', 'category', 'planCategory', 'customPlanData', 'activationPolicy']
            }, {
                model: models.User,
                as: 'user',
                attributes: ['id', 'firstName', 'lastName', 'email']
            }],
            order: [['createdAt', 'DESC']],
            limit,
            offset
        });

        // Format the response
        const formattedOrders = await Promise.all(orders.map(async (order) => {
            // Get stock details from history
            const stockHistory = await models.EsimPlanStockHistory.findOne({
                where: { orderId: order.id }
            });

            return {
                id: order.id,
                customer: `${order.user.firstName} ${order.user.lastName}`,
                customerEmail: order.user.email,
                plan: {
                    id: order.plan.id,
                    name: order.plan.name,
                    description: order.plan.description,
                    planData: order.plan.planData,
                    planType: order.plan.planType,
                    planDataUnit: order.plan.planDataUnit,
                    validityDays: order.plan.validityDays,
                    category: order.plan.category,
                    customPlanData: order.plan.customPlanData,
                    planCategory: order.plan.planCategory,
                    activationPolicy: order.plan.activationPolicy
                },
                orderTotal: order.orderTotal,
                startDate: order.startDate,
                status: order.status,
                createdAt: order.createdAt,
                quantity: order.quantity,
                externalOrderId: order.externalOrderId,
                // Stock details with fallback to providerMetadata for API orders
                iccid: stockHistory?.iccid || order.providerMetadata?.iccid || null,
                smdpAddress: stockHistory?.smdpAddress || order.providerMetadata?.smdpAddress || null,
                lpaString: stockHistory?.lpaString || order.providerMetadata?.lpaString || null,
                accessPointName: stockHistory?.accessPointName || order.providerMetadata?.accessPointName || null,
                activationCode: stockHistory?.activationCode || order.providerMetadata?.activationCode || null,
                phoneNumber: stockHistory?.phoneNumber || order.providerMetadata?.phoneNumber || null
            };
        }));

        return res.json({
            orders: formattedOrders,
            totalPages: Math.ceil(count / limit),
            currentPage: page,
            totalItems: count
        });
    } catch (error) {
        console.error('Error fetching user orders:', error);
        return res.status(500).json({ message: 'Failed to fetch orders' });
    }
};

// Get order by ID (Admin Only) - OPTIMIZED WITH CACHING
exports.getAllOrderById = async (req, res) => {
    const startTime = Date.now();

    try {
        const orderId = req.params.id;

        // Create cache key for this order
        const cacheKey = `admin_order_${orderId}`;

        // Check cache first
        const cachedOrder = getCachedPlan(cacheKey);
        if (cachedOrder) {
            const responseTime = Date.now() - startTime;
            console.log(`🚀 Cache HIT for admin order ${orderId}: ${responseTime}ms`);
            return res.json(cachedOrder);
        }

        // Log memory status before heavy query
        memoryMonitor.logMemoryStatus();

        const order = await models.Order.findByPk(orderId, {
            include: [
                {
                    model: models.EsimPlan,
                    as: 'plan',
                    attributes: ['id', 'name', 'description', 'planData', 'planType', 'planDataUnit', 'validityDays', 'category', 'planCategory', 'customPlanData', 'activationPolicy'],
                    include: [{
                        model: models.Provider,
                        as: 'provider',
                        attributes: ['id', 'name', 'type', 'country']
                    }]
                },
                {
                    model: models.User,
                    as: 'user',
                    attributes: ['id', 'firstName', 'lastName', 'email']
                }
            ],
        });

        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }

        // Get stock details from history
        const stockHistory = await models.EsimPlanStockHistory.findOne({
            where: { orderId: order.id }
        });

        // Generate QR code if we have lpaString (from stockHistory or providerMetadata)
        let qrCode = null;
        const lpaString = stockHistory?.lpaString || order.providerMetadata?.lpaString;
        
        if (lpaString) {
            try {
                qrCode = await generateQRCode(lpaString);
                // Remove the 'data:image/png;base64,' prefix
                qrCode = qrCode.split(',')[1];
            } catch (error) {
                console.error('Error generating QR code:', error);
            }
        } else if (order.providerMetadata?.qrCodeUrl) {
            // Use existing QR code if available
            qrCode = order.providerMetadata.qrCodeUrl;
        }

        // Format the response
        const formattedOrder = {
            id: order.id,
            customer: `${order.user.firstName} ${order.user.lastName}`,
            customerEmail: order.user.email,
            plan: {
                id: order.plan.id,
                name: order.plan.name,
                description: order.plan.description,
                planData: order.plan.planData,
                planType: order.plan.planType,
                planDataUnit: order.plan.planDataUnit,
                validityDays: order.plan.validityDays,
                category: order.plan.category,
                customPlanData: order.plan.customPlanData,
                planCategory: order.plan.planCategory,
                activationPolicy: order.plan.activationPolicy,
                provider: order.plan.provider ? {
                    id: order.plan.provider.id,
                    name: order.plan.provider.name,
                    type: order.plan.provider.type,
                    country: order.plan.provider.country
                } : null
            },
            orderTotal: order.orderTotal,
            startDate: order.startDate,
            validTime: order.validTime,
            status: order.status,
            createdAt: order.createdAt,
            quantity: order.quantity,
            externalOrderId: order.externalOrderId,
            parentOrderId: order.parentOrderId, // Include parent order ID for top-up orders
            // Include usage data fields
            dataUsage: order.dataUsage,
            dataAllowance: order.dataAllowance,
            usageStatus: order.usageStatus,
            expiryDate: order.expiryDate,
            usageMessage: order.usageMessage,
            lastUsageCheck: order.lastUsageCheck,
            usageData: order.usageData,
            // Stock details with fallback to providerMetadata for API orders
            iccid: stockHistory?.iccid || order.providerMetadata?.iccid || null,
            smdpAddress: stockHistory?.smdpAddress || order.providerMetadata?.smdpAddress || null,
            lpaString: stockHistory?.lpaString || order.providerMetadata?.lpaString || null,
            accessPointName: stockHistory?.accessPointName || order.providerMetadata?.accessPointName || null,
            activationCode: stockHistory?.activationCode || order.providerMetadata?.activationCode || null,
            phoneNumber: stockHistory?.phoneNumber || order.providerMetadata?.phoneNumber || null,
            qrCode: qrCode || (order.providerMetadata?.qrCodeUrl ? order.providerMetadata.qrCodeUrl : null)
        };

        // Cache the response for 2 minutes (orders don't change frequently)
        setCachePlan(cacheKey, formattedOrder, 120 * 1000);

        const responseTime = Date.now() - startTime;
        console.log(`✅ Generated fresh admin order data for ${orderId} in ${responseTime}ms`);
        memoryMonitor.logMemoryStatus();

        return res.json(formattedOrder);
    } catch (error) {
        console.error('Error fetching order:', error);
        return res.status(500).json({ message: 'Failed to fetch order details' });
    }
};

// Export all orders (admin only)
exports.exportOrders = async (req, res) => {
    try {
        // Get all orders with related data
        const orders = await models.Order.findAll({
            include: [
                {
                    model: models.User,
                    as: 'user',
                    attributes: ['id', 'firstName', 'lastName', 'email']
                },
                {
                    model: models.EsimPlan,
                    as: 'plan',
                    attributes: ['name']
                },
                {
                    model: models.EsimPlanStockHistory,
                    as: 'stockHistory',
                    attributes: ['iccid']
                }
            ],
            order: [['createdAt', 'DESC']]
        });

        // Format orders for export
        const formattedOrders = orders.map(order => {
            const json = order.toJSON();
            return {
                id: json.id,
                customer: `${json.user?.firstName} ${json.user?.lastName}` || 'N/A',
                customerEmail: json.user?.email || 'N/A',
                plan: json.plan,
                iccid: json.stockHistory?.iccid || 'N/A',
                quantity: 1, 
                orderTotal: json.orderTotal,
                status: json.status,
                createdAt: json.createdAt
            };
        });

        res.json({ orders: formattedOrders });
    } catch (error) {
        console.error('Error exporting orders:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};

// Get usage data for an order
exports.getOrderUsage = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user.id;

        console.log('Fetching usage data for order:', id);

        // Find the order with plan and provider details
        const order = await models.Order.findOne({
            where: { id },
            include: [{
                model: models.EsimPlan,
                as: 'plan',
                include: [{
                    model: models.Provider,
                    as: 'provider'
                }]
            }]
        });

        console.log('Found order:', {
            id: order?.id,
            provider: order?.plan?.provider?.name,
            externalOrderId: order?.externalOrderId,
            providerResponse: order?.providerResponse
        });

        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }

        // Check if user has access to this order
        if (order.userId !== userId && req.user.role !== 'admin') {
            return res.status(403).json({ message: 'Access denied' });
        }

        // Check if the order is from a supported provider
        const providerName = order.plan?.provider?.name;
        if (providerName !== 'Mobimatter' && providerName !== 'Billionconnect') {
            return res.status(400).json({ message: 'Usage data is not available for this provider' });
        }

        // Check if the order is completed - usage data is only available for completed orders
        if (order.status === 'pending') {
            return res.status(400).json({
                message: 'Usage data is not available for pending orders. Please wait for the order to be completed.',
                status: 'pending'
            });
        }
        
        // Get the external order ID
        const externalOrderId = order.externalOrderId || order.providerResponse?.id;
        if (!externalOrderId) {
            console.error('Order details:', {
                id: order.id,
                externalOrderId: order.externalOrderId,
                providerResponse: order.providerResponse,
                providerMetadata: order.providerMetadata
            });
            return res.status(400).json({ message: 'External order ID not found' });
        }

        let usageData;

        if (providerName === 'Mobimatter') {
            console.log('Fetching usage data from Mobimatter for external order:', externalOrderId);
            usageData = await mobimatterService.getUsageInfo(externalOrderId);
        } else if (providerName === 'Billionconnect') {
            console.log('Fetching usage data from BillionConnect for external order:', externalOrderId);

            // For BillionConnect, we need the ICCID as well
            const iccid = order.stock?.iccid ||
                         order.providerMetadata?.iccid ||
                         order.providerResponse?.tradeData?.iccid;

            if (!iccid) {
                console.error('ICCID not found for BillionConnect order:', {
                    orderId: order.id,
                    externalOrderId: externalOrderId,
                    stockIccid: order.stock?.iccid,
                    metadataIccid: order.providerMetadata?.iccid,
                    responseIccid: order.providerResponse?.tradeData?.iccid
                });
                return res.status(400).json({ message: 'ICCID not found for BillionConnect usage query' });
            }

            // Get channel order ID if available
            const channelOrderId = order.id; // Our internal order ID is used as channel order ID

            usageData = await billionconnectService.getUsageInfo(externalOrderId, channelOrderId, iccid);
        }

        console.log(`${providerName} usage data response:`, usageData);

        // Store the usage data in the database
        try {
            await order.update({
                usageData: usageData,
                dataUsage: usageData.dataUsage || null,
                dataAllowance: usageData.dataAllowance || null,
                usageStatus: usageData.status || 'Unknown',
                expiryDate: usageData.expiryDate || null,
                usageMessage: usageData.message || usageData.usageMessage || null,
                lastUsageCheck: new Date()
            });
            console.log('Usage data stored in database for order:', id);
        } catch (updateError) {
            console.error('Error storing usage data in database:', updateError);
            // Continue processing - don't fail the request if storage fails
        }

        res.json(usageData);
    } catch (error) {
        console.error('Error getting order usage:', error);
        console.error('Error stack:', error.stack);
        res.status(500).json({ message: 'Failed to get usage data', error: error.message });
    }
};

/**
 * Handle BillionConnect webhook notifications - Store first, respond quickly
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.handleBillionConnectWebhook = async (req, res) => {
    try {
        const { tradeType, tradeTime, tradeData } = req.body;

        console.log('Received BillionConnect webhook:', {
            tradeType,
            tradeTime,
            orderId: tradeData?.orderId,
            channelOrderId: tradeData?.channelOrderId,
            timestamp: new Date().toISOString()
        });

        // Store the notification immediately
        const notificationData = {
            provider: 'billionconnect',
            notificationType: tradeType,
            rawPayload: req.body,
            orderId: tradeData?.orderId,
            channelOrderId: tradeData?.channelOrderId,
            receivedAt: new Date(),
            metadata: {
                userAgent: req.get('User-Agent'),
                ip: req.ip,
                headers: req.headers
            }
        };

        // Try to find internal order ID for reference
        if (tradeData?.orderId) {
            try {
                const order = await models.Order.findOne({
                    where: { externalOrderId: tradeData.orderId },
                    attributes: ['id']
                });
                if (order) {
                    notificationData.internalOrderId = order.id;
                }
            } catch (error) {
                console.warn('Could not find internal order for notification storage:', error.message);
            }
        }

        // Store notification in database
        const notification = await models.NotificationMessage.create(notificationData);

        console.log('Notification stored successfully:', {
            notificationId: notification.id,
            tradeType,
            orderId: tradeData?.orderId
        });

        // Respond immediately to BC
        res.json({
            tradeCode: '1000',
            tradeMsg: 'Notification received and stored'
        });

        // Process the notification asynchronously (don't await)
        setImmediate(() => {
            processStoredNotification(notification.id).catch(error => {
                console.error('Error in async notification processing:', error);

                // If immediate processing fails, schedule a retry for race conditions
                const notificationProcessor = require('../services/notificationProcessor.service');
                notificationProcessor.scheduleRetry(2000); // Retry in 2 seconds for race conditions
            });
        });

    } catch (error) {
        console.error('Error storing BillionConnect webhook:', error);

        // Still respond with success to BC to avoid retries
        // The notification will be lost but we log the error
        res.json({
            tradeCode: '1000',
            tradeMsg: 'Notification acknowledged'
        });
    }
};

/**
 * Process a stored notification message asynchronously
 * @param {string} notificationId - ID of the notification to process
 */
async function processStoredNotification(notificationId) {
    const t = await sequelize.transaction();
    try {
        // Get the notification
        const notification = await models.NotificationMessage.findByPk(notificationId, {
            include: [{
                model: models.Order,
                as: 'order',
                include: [
                    { model: models.User, as: 'user' },
                    {
                        model: models.EsimPlan,
                        as: 'plan',
                        include: [{
                            model: models.Provider,
                            as: 'provider',
                            attributes: ['id', 'name', 'type', 'country']
                        }]
                    }
                ]
            }],
            transaction: t
        });

        if (!notification) {
            console.error('Notification not found:', notificationId);
            return;
        }

        // Update notification status to processing
        await notification.update({
            status: 'processing',
            processingAttempts: notification.processingAttempts + 1,
            lastProcessingAttempt: new Date()
        }, { transaction: t });

        const { tradeType, tradeData } = notification.rawPayload;

        console.log('Processing stored notification:', {
            notificationId: notification.id,
            tradeType,
            orderId: tradeData?.orderId,
            attempt: notification.processingAttempts + 1
        });

        // Only handle N009 notifications (ESIM QR code info)
        if (tradeType !== 'N009') {
            console.log('Ignoring non-N009 notification:', tradeType);
            await notification.update({
                status: 'ignored',
                processingResult: { reason: 'Non-N009 notification type' },
                processedAt: new Date()
            }, { transaction: t });
            await t.commit();
            return;
        }

        const { orderId, channelOrderId, subOrderList } = tradeData;

        // Find the order in our database
        let order = notification.order;
        if (!order && orderId) {
            order = await models.Order.findOne({
                where: { externalOrderId: orderId },
                include: [
                    { model: models.User, as: 'user' },
                    {
                        model: models.EsimPlan,
                        as: 'plan',
                        include: [{
                            model: models.Provider,
                            as: 'provider',
                            attributes: ['id', 'name', 'type', 'country']
                        }]
                    }
                ],
                transaction: t
            });
        }

        if (!order) {
            // Handle race condition: webhook might arrive before order creation is committed
            // Mark as failed with race condition indicator for faster retry
            const isRaceCondition = notification.processingAttempts <= 2;
            const errorMessage = isRaceCondition
                ? `Order not found for orderId: ${orderId} (attempt ${notification.processingAttempts} - likely race condition)`
                : `Order not found for orderId: ${orderId} after ${notification.processingAttempts} attempts`;

            console.log(`Order not found for BillionConnect orderId: ${orderId}, attempt ${notification.processingAttempts}. ${isRaceCondition ? 'This might be a race condition - will retry faster.' : 'Giving up after multiple attempts.'}`);

            await notification.update({
                status: 'failed',
                processingError: errorMessage,
                processedAt: new Date()
            }, { transaction: t });
            await t.commit();
            return;
        }

        // Check if this is a topup/recharge order
        if (order.providerMetadata && order.providerMetadata.rechargeOrder === true) {
            console.log('Skipping N009 processing for topup order:', {
                orderId: order.id,
                externalOrderId: orderId,
                reason: 'Topup orders do not generate new ICCID and QR code'
            });

            await notification.update({
                status: 'ignored',
                processingResult: { reason: 'Topup order - no new ICCID/QR code' },
                processedAt: new Date()
            }, { transaction: t });
            await t.commit();
            return;
        }

        console.log('Processing BillionConnect notification for order:', {
            orderId: order.id,
            externalOrderId: orderId,
            status: order.status,
            userId: order.userId,
            planId: order.esimPlanId
        });

        // Process each sub-order (usually just one)
        for (const subOrder of subOrderList) {
            const {
                iccid,
                qrCodeContent,
                apn,
                validTime
            } = subOrder;

            // Parse LPA string to extract smdpAddress, activationCode, and lpaString
            const lpaData = parseLpaString(qrCodeContent);

            if (!lpaData) {
                console.error('Failed to parse LPA string from qrCodeContent:', qrCodeContent);
                throw new Error('Invalid LPA format in webhook data');
            }

            const { smdpAddress, activationCode, lpaString } = lpaData;

            console.log('Processing eSIM details:', {
                iccid,
                smdpAddress,
                apn,
                hasQrCode: !!qrCodeContent,
                hasActivationCode: !!activationCode,
                lpaString: lpaString
            });

            // Check if a stock record with this ICCID already exists
            let stock = await models.EsimStock.findOne({
                where: { iccid: iccid },
                transaction: t,
                lock: true
            });

            const stockData = {
                esimPlanId: order.esimPlanId,
                orderId: order.id,
                iccid: iccid,
                smdpAddress: smdpAddress,
                lpaString: lpaString,
                accessPointName: apn,
                activationCode: activationCode,
                qrCodeUrl: qrCodeContent,
                status: 'assigned',
                externalStockId: orderId,
                externalIccid: iccid,
                providerStatus: 'ACTIVE',
                providerMetadata: subOrder,
                walletAuthTransactionId: order.walletAuthTransactionId
            };

            if (stock) {
                console.log('Updating existing stock record:', stock.id);
                await stock.update(stockData, { transaction: t });
            } else {
                console.log('Creating new stock record');
                stock = await models.EsimStock.create({
                    id: uuidv4(),
                    ...stockData
                }, { transaction: t });
            }

            console.log('Creating stock history record');
            // Create stock history record - check if one already exists first
            const existingStockHistory = await models.EsimPlanStockHistory.findOne({
                where: { orderId: order.id },
                transaction: t
            });

            if (!existingStockHistory) {
                try {
                    await models.EsimPlanStockHistory.create({
                        id: uuidv4(),
                        esimPlanId: order.esimPlanId,
                        esimStockId: stock.id,
                        iccid: iccid,
                        smdpAddress: smdpAddress,
                        lpaString: lpaString,
                        accessPointName: apn,
                        activationCode: activationCode,
                        orderId: order.id,
                        orderDate: new Date(),
                        quantity: 1,
                        status: 'assigned',
                        reason: 'BillionConnect webhook activation',
                        createdBy: order.userId
                    }, { transaction: t });
                } catch (error) {
                    if (error.name === 'SequelizeUniqueConstraintError') {
                        console.log(`Stock history already exists for order ${order.id}, skipping creation`);
                    } else {
                        throw error;
                    }
                }
            } else {
                console.log(`Stock history already exists for order ${order.id}, skipping creation`);
            }

            console.log('Updating order status to completed');
            // Update order with stock reference and completed status
            await order.update({
                status: 'completed',
                esimStockId: stock.id,
                providerOrderStatus: 'ACTIVE',
                validTime: validTime ? new Date(validTime) : null,
                providerMetadata: {
                    iccid: iccid,
                    smdpAddress: smdpAddress,
                    lpaString: lpaString,
                    accessPointName: apn,
                    activationCode: activationCode,
                    qrCodeUrl: qrCodeContent,
                    validTime: validTime
                }
            }, { transaction: t });

            // Send email notifications for completed BillionConnect order
            try {
                console.log('Sending email notifications for BillionConnect order:', order.id);

                const user = order.user;
                const plan = order.plan;

                // Generate QR code for email
                let qrCode = null;
                if (lpaString) {
                    try {
                        qrCode = await generateQRCode(lpaString);
                        console.log('QR code generated for email');
                    } catch (error) {
                        console.error('Error generating QR code for email:', error);
                    }
                }

                const orderDetails = {
                    orderId: order.id,
                    externalOrderId: order.externalOrderId,
                    planName: plan.name,
                    planData: plan.data,
                    planValidity: plan.validity,
                    planPrice: order.totalAmount || order.orderTotal || plan.price,
                    customerName: `${user.firstName} ${user.lastName}`,
                    customerEmail: user.email,
                    orderDate: order.createdAt,
                    iccid: iccid,
                    qrCodeUrl: qrCodeContent,
                    activationCode: activationCode,
                    apn: apn,
                    smdpAddress: smdpAddress,
                    lpaString: lpaString,
                    validTime: validTime,
                    countries: plan.countries || [],
                    provider: plan.provider?.name || 'BillionConnect',
                    qrCode: qrCode, // Add the generated QR code
                    // Additional fields for email compatibility
                    order: {
                        id: order.id,
                        externalOrderId: order.externalOrderId,
                        status: 'completed',
                        totalAmount: order.totalAmount,
                        createdAt: order.createdAt,
                        parentOrderId: order.parentOrderId
                    },
                    plan: plan,
                    orderTotal: order.totalAmount || order.orderTotal || plan.price,
                    expiryDate: validTime ? new Date(validTime) : null,
                    quantity: order.quantity || 1,
                    walletAuthTransactionId: order.walletAuthTransactionId,
                    isTopUp: order.parentOrderId ? true : false,
                    partner: {
                        id: user.id,
                        firstName: user.firstName,
                        lastName: user.lastName,
                        email: user.email
                    },
                    stock: {
                        iccid: iccid,
                        smdpAddress: smdpAddress,
                        lpaString: lpaString,
                        accessPointName: apn,
                        activationCode: activationCode,
                        phoneNumber: null
                    }
                };

                // Check if this is an API order (multiple detection methods)
                const isApiOrder = (order.metadata && order.metadata.apiKey) ||
                                 (notification.rawPayload.tradeData?.channelOrderId &&
                                  notification.rawPayload.tradeData.channelOrderId.startsWith('api_'));

                // Check if user has alternate email and wants to use it
                const emailToUse = (user.alternateEmail && user.useAlternateEmailForOrders)
                    ? user.alternateEmail
                    : user.email;

                if (isApiOrder) {
                    // Send API-specific emails with "(Via API)" in subject and QR code
                    console.log('Sending API-specific emails for BC order:', order.id);

                    if (user.role === 'admin') {
                        // Send admin-specific API emails
                        await emailService.sendApiAdminOrderEmail([emailToUse], orderDetails);
                        console.log('BillionConnect API order email sent to admin:', emailToUse);
                    } else {
                        // Send API partner email with QR code
                        await emailService.sendApiPartnerOrderEmail(user.email, orderDetails);
                        console.log('BillionConnect API order email sent to partner:', user.email);

                        // Send admin notifications for API orders
                        const admins = await models.User.findAll({
                            where: {
                                role: 'admin',
                                isActive: true
                            },
                            attributes: ['email']
                        });

                        if (admins && admins.length > 0) {
                            const adminEmails = admins.map(admin => admin.email);
                            await emailService.sendApiAdminOrderEmail(adminEmails, orderDetails);
                            console.log('BillionConnect API order admin notifications sent');
                        }
                    }
                } else {
                    // Send regular web order emails
                    console.log('Sending regular web order emails for BC order:', order.id);

                    if (user.role === 'admin') {
                        // Send admin-specific emails
                        await emailService.sendAdminOrderEmail([emailToUse], orderDetails);
                        console.log('BillionConnect order email sent to admin:', emailToUse);
                    } else {
                        // Send regular emails for web orders with QR code
                        await emailService.sendPartnerOrderEmail(user.email, orderDetails);
                        console.log('BillionConnect order email sent to partner:', user.email);

                        // Send admin notifications
                        const admins = await models.User.findAll({
                            where: {
                                role: 'admin',
                                isActive: true
                            },
                            attributes: ['email']
                        });

                        if (admins && admins.length > 0) {
                            const adminEmails = admins.map(admin => admin.email);
                            await emailService.sendAdminOrderEmail(adminEmails, orderDetails);
                            console.log('BillionConnect order admin notifications sent');
                        }
                    }
                }
            } catch (emailError) {
                console.error('Error sending BillionConnect order emails:', emailError);
                // Don't fail the notification processing due to email errors
            }
        }

        // Mark notification as completed
        await notification.update({
            status: 'completed',
            processingResult: {
                orderId: order.id,
                externalOrderId: orderId,
                processedSubOrders: subOrderList.length
            },
            processedAt: new Date()
        }, { transaction: t });

        await t.commit();
        console.log('Notification processing completed successfully:', notificationId);

    } catch (error) {
        await t.rollback();
        console.error('Error processing stored notification:', notificationId, error);

        // Update notification with error status - preserve processingAttempts count
        try {
            // Get the current notification to preserve the processingAttempts count
            const currentNotification = await models.NotificationMessage.findByPk(notificationId);
            if (currentNotification) {
                await models.NotificationMessage.update({
                    status: 'failed',
                    processingError: error.message,
                    lastProcessingAttempt: new Date(),
                    // Preserve the processingAttempts count that was incremented before the transaction
                    processingAttempts: currentNotification.processingAttempts + 1
                }, {
                    where: { id: notificationId }
                });
            }
        } catch (updateError) {
            console.error('Error updating notification status:', updateError);
        }
    }
}

/**
 * Get top-up plans for a specific order
 */
exports.getTopUpPlans = async (req, res) => {
    try {
        const { id: orderId } = req.params;
        const userId = req.user.id;

        // Get partner information for markup calculation
        const partner = await models.User.findByPk(userId);
        if (!partner) {
            return res.status(404).json({
                success: false,
                message: 'Partner not found'
            });
        }

        // First, get the original order to verify ownership and get network info
        const originalOrder = await models.Order.findOne({
            where: {
                id: orderId,
                userId: userId
            },
            include: [
                {
                    model: models.EsimPlan,
                    as: 'plan',
                    include: [{
                        model: models.Provider,
                        as: 'provider'
                    }]
                },
                {
                    model: models.EsimStock,
                    as: 'stock',
                    required: false // Left join - stock might not exist for pending orders
                }
            ]
        });

        console.log('Original order for top-up:', {
            id: originalOrder?.id,
            externalOrderId: originalOrder?.externalOrderId,
            provider: originalOrder?.plan?.provider?.name,
            status: originalOrder?.status,
            hasStock: !!originalOrder?.stock,
            stockIccid: originalOrder?.stock?.iccid,
            providerMetadataIccid: originalOrder?.providerMetadata?.iccid,
            providerResponseIccid: originalOrder?.providerResponse?.tradeData?.iccid
        });

        if (!originalOrder) {
            return res.status(404).json({
                success: false,
                message: 'Order not found or access denied'
            });
        }

        // Check if the order is from a supported provider
        const providerName = originalOrder.plan.provider.name;
        if (providerName !== 'Mobimatter' && providerName !== 'Billionconnect') {
            return res.status(400).json({
                success: false,
                message: 'Top-up is only available for Mobimatter and BillionConnect orders'
            });
        }

        let topUpPlans = [];

        if (providerName === 'Mobimatter') {
            // Get the network from the original plan
            const originalNetwork = originalOrder.plan.networkName;

            if (!originalNetwork) {
                return res.status(400).json({
                    success: false,
                    message: 'Cannot determine network for top-up plans'
                });
            }

            // Get all Mobimatter addon plans with the same network
            const mobimatterPlans = await models.EsimPlan.findAll({
                where: {
                    status: 'visible',
                    isActive: true,
                    category: 'esim_addon',
                    networkName: originalNetwork
                },
                include: [
                    {
                        model: models.Provider,
                        as: 'provider',
                        where: { name: 'Mobimatter' }
                    },
                    {
                        model: models.Country,
                        as: 'countries',
                        through: { attributes: [] }
                    }
                ],
                order: [['createdAt', 'DESC']]
            });

            // Apply markup-based pricing for all plans
            topUpPlans = mobimatterPlans.map(plan => {
                const planData = plan.toJSON ? plan.toJSON() : plan;

                // Calculate display price based on markup
                if (!planData.sellingPrice) {
                    const markup = partner.markupPercentage || 0;
                    const basePrice = parseFloat(planData.buyingPrice);
                    const markupAmount = (basePrice * markup) / 100;
                    planData.displayPrice = (basePrice + markupAmount).toFixed(2);
                } else {
                    planData.displayPrice = planData.sellingPrice;
                }

                return planData;
            });

        } else if (providerName === 'Billionconnect') {
            // For BillionConnect, we need to query available recharge SKUs using F052
            // Try to get ICCID from multiple sources
            const iccid = originalOrder.stock?.iccid ||
                         originalOrder.providerMetadata?.iccid ||
                         originalOrder.providerResponse?.tradeData?.iccid;

            if (!iccid) {
                return res.status(400).json({
                    success: false,
                    message: 'Cannot find ICCID for Billionconnect top-up. Order may not be completed yet.'
                });
            }

            try {
                // Query available recharge SKUs
                const availableSkuIds = await billionconnectService.queryRechargeCommodities(iccid);

                if (!availableSkuIds || availableSkuIds.length === 0) {
                    return res.json({
                        success: true,
                        data: {
                            originalOrder: {
                                id: originalOrder.id,
                                planName: originalOrder.plan.name,
                                iccid: iccid
                            },
                            topUpPlans: []
                        }
                    });
                }

                // Get Billionconnect plans that match the available SKUs
                const billionconnectPlans = await models.EsimPlan.findAll({
                    where: {
                        status: 'visible',
                        isActive: true,
                        externalSkuId: {
                            [Op.in]: availableSkuIds
                        }
                    },
                    include: [
                        {
                            model: models.Provider,
                            as: 'provider',
                            where: { name: 'Billionconnect' }
                        },
                        {
                            model: models.Country,
                            as: 'countries',
                            through: { attributes: [] }
                        }
                    ],
                    order: [['createdAt', 'DESC']]
                });

                // Apply markup-based pricing for Billionconnect plans
                topUpPlans = billionconnectPlans.map(plan => {
                    const planData = plan.toJSON ? plan.toJSON() : plan;

                    // Calculate display price based on markup
                    if (!planData.sellingPrice) {
                        const markup = partner.markupPercentage || 0;
                        const basePrice = parseFloat(planData.buyingPrice);
                        const markupAmount = (basePrice * markup) / 100;
                        planData.displayPrice = (basePrice + markupAmount).toFixed(2);
                    } else {
                        planData.displayPrice = planData.sellingPrice;
                    }

                    return planData;
                });

            } catch (error) {
                console.error('Error querying Billionconnect recharge commodities:', error);
                return res.status(500).json({
                    success: false,
                    message: 'Failed to fetch available top-up plans from Billionconnect'
                });
            }
        }

        res.json({
            success: true,
            data: {
                originalOrder: {
                    id: originalOrder.id,
                    planName: originalOrder.plan.name,
                    network: providerName === 'Mobimatter' ? originalOrder.plan.networkName : null,
                    iccid: providerName === 'Billionconnect' ? originalOrder.stock?.iccid : null,
                    provider: providerName
                },
                topUpPlans: topUpPlans
            }
        });

    } catch (error) {
        console.error('Error fetching top-up plans:', error);
        res.status(500).json({
            success: false,
            message: 'Error retrieving top-up plans'
        });
    }
};

// Export the processing function for use by background processors
exports.processStoredNotification = processStoredNotification;

module.exports = exports;
