const Redis = require('ioredis');
const NodeCache = require('node-cache');
const memoryMonitor = require('../utils/memoryMonitor');

class HybridCacheService {
    constructor() {
        this.redisClient = null;
        this.fallbackCache = new NodeCache({ 
            stdTTL: 1800, // 30 minutes default
            checkperiod: 300, // 5 minutes cleanup
            maxKeys: 1000, // Limit memory usage
            useClones: false // Better performance, less memory
        });
        
        this.isRedisConnected = false;
        this.retryAttempts = 0;
        this.maxRetries = 3;
        this.retryDelay = 5000; // 5 seconds
        
        // Performance metrics
        this.stats = {
            redisHits: 0,
            redisMisses: 0,
            fallbackHits: 0,
            fallbackMisses: 0,
            errors: 0
        };
        
        this.initializeRedis();
    }

    async initializeRedis() {
        try {
            // Only initialize Redis if configuration is available
            if (!process.env.REDIS_URL && !process.env.REDIS_HOST) {
                console.log('📦 Redis not configured, using in-memory cache only');
                return;
            }

            const redisConfig = {
                host: process.env.REDIS_HOST || 'localhost',
                port: parseInt(process.env.REDIS_PORT) || 6379,
                password: process.env.REDIS_PASSWORD || undefined,
                db: parseInt(process.env.REDIS_DB) || 0,

                // Production optimizations
                retryDelayOnFailover: 100,
                maxRetriesPerRequest: parseInt(process.env.REDIS_MAX_RETRIES) || 3,
                lazyConnect: true,
                keepAlive: 30000,

                // Connection settings
                family: 4,
                connectTimeout: parseInt(process.env.REDIS_CONNECT_TIMEOUT) || 10000,
                commandTimeout: parseInt(process.env.REDIS_COMMAND_TIMEOUT) || 5000,

                // TLS support for production
                tls: process.env.REDIS_TLS === 'true' ? {} : undefined,

                // Connection pool for high traffic
                enableAutoPipelining: true,
                maxLoadingTimeout: 5000,

                // Memory optimization
                compression: process.env.NODE_ENV === 'production' ? 'gzip' : undefined
            };

            this.redisClient = new Redis(redisConfig);

            this.redisClient.on('connect', () => {
                console.log('✅ Redis connected successfully');
                this.isRedisConnected = true;
                this.retryAttempts = 0;
            });

            this.redisClient.on('error', (error) => {
                console.warn('⚠️ Redis connection error, falling back to in-memory cache:', error.message);
                this.isRedisConnected = false;
                this.stats.errors++;
                
                // Don't spam logs with connection errors
                if (this.retryAttempts < this.maxRetries) {
                    this.scheduleReconnect();
                }
            });

            this.redisClient.on('close', () => {
                console.log('📦 Redis connection closed, using in-memory cache');
                this.isRedisConnected = false;
            });

            // Test connection
            await this.redisClient.ping();
            
        } catch (error) {
            console.warn('⚠️ Failed to initialize Redis, using in-memory cache only:', error.message);
            this.isRedisConnected = false;
        }
    }

    scheduleReconnect() {
        if (this.retryAttempts >= this.maxRetries) {
            console.log('🚫 Max Redis retry attempts reached, staying with in-memory cache');
            return;
        }

        this.retryAttempts++;
        setTimeout(() => {
            console.log(`🔄 Attempting Redis reconnection (${this.retryAttempts}/${this.maxRetries})`);
            this.initializeRedis();
        }, this.retryDelay * this.retryAttempts);
    }

    async get(key) {
        try {
            // Try Redis first if connected
            if (this.isRedisConnected && this.redisClient) {
                try {
                    const value = await this.redisClient.get(key);
                    if (value !== null) {
                        this.stats.redisHits++;
                        return JSON.parse(value);
                    }
                    this.stats.redisMisses++;
                } catch (redisError) {
                    console.warn('Redis get error, falling back:', redisError.message);
                    this.isRedisConnected = false;
                }
            }

            // Fallback to in-memory cache
            const fallbackValue = this.fallbackCache.get(key);
            if (fallbackValue !== undefined) {
                this.stats.fallbackHits++;
                return fallbackValue;
            }
            
            this.stats.fallbackMisses++;
            return null;
            
        } catch (error) {
            console.error('Cache get error:', error);
            this.stats.errors++;
            return null;
        }
    }

    async set(key, value, ttlSeconds = 1800) {
        try {
            // Check memory pressure before caching
            if (memoryMonitor.isMemoryCritical()) {
                console.warn('⚠️ Memory critical - skipping cache to prevent memory leak');
                return false;
            }

            const serializedValue = JSON.stringify(value);
            
            // Try Redis first if connected
            if (this.isRedisConnected && this.redisClient) {
                try {
                    await this.redisClient.setex(key, ttlSeconds, serializedValue);
                    
                    // Also cache in memory for faster access (smaller TTL)
                    const memoryTtl = Math.min(ttlSeconds, 300); // Max 5 minutes in memory
                    this.fallbackCache.set(key, value, memoryTtl);
                    
                    return true;
                } catch (redisError) {
                    console.warn('Redis set error, using fallback:', redisError.message);
                    this.isRedisConnected = false;
                }
            }

            // Fallback to in-memory cache only
            this.fallbackCache.set(key, value, ttlSeconds);
            return true;
            
        } catch (error) {
            console.error('Cache set error:', error);
            this.stats.errors++;
            return false;
        }
    }

    async del(key) {
        try {
            // Delete from both caches
            if (this.isRedisConnected && this.redisClient) {
                try {
                    await this.redisClient.del(key);
                } catch (redisError) {
                    console.warn('Redis delete error:', redisError.message);
                }
            }
            
            this.fallbackCache.del(key);
            return true;
            
        } catch (error) {
            console.error('Cache delete error:', error);
            return false;
        }
    }

    async flush() {
        try {
            if (this.isRedisConnected && this.redisClient) {
                try {
                    await this.redisClient.flushdb();
                } catch (redisError) {
                    console.warn('Redis flush error:', redisError.message);
                }
            }
            
            this.fallbackCache.flushAll();
            return true;
            
        } catch (error) {
            console.error('Cache flush error:', error);
            return false;
        }
    }

    async has(key) {
        try {
            if (this.isRedisConnected && this.redisClient) {
                try {
                    const exists = await this.redisClient.exists(key);
                    if (exists) return true;
                } catch (redisError) {
                    console.warn('Redis exists error:', redisError.message);
                }
            }
            
            return this.fallbackCache.has(key);
            
        } catch (error) {
            console.error('Cache has error:', error);
            return false;
        }
    }

    getStats() {
        const fallbackStats = this.fallbackCache.getStats();
        return {
            redis: {
                connected: this.isRedisConnected,
                hits: this.stats.redisHits,
                misses: this.stats.redisMisses
            },
            fallback: {
                hits: this.stats.fallbackHits,
                misses: this.stats.fallbackMisses,
                keys: fallbackStats.keys,
                ksize: fallbackStats.ksize,
                vsize: fallbackStats.vsize
            },
            errors: this.stats.errors,
            hitRate: this.calculateHitRate()
        };
    }

    calculateHitRate() {
        const totalHits = this.stats.redisHits + this.stats.fallbackHits;
        const totalRequests = totalHits + this.stats.redisMisses + this.stats.fallbackMisses;
        return totalRequests > 0 ? (totalHits / totalRequests * 100).toFixed(2) : 0;
    }

    // Graceful shutdown
    async disconnect() {
        if (this.redisClient) {
            await this.redisClient.quit();
        }
        this.fallbackCache.close();
    }
}

module.exports = new HybridCacheService();
