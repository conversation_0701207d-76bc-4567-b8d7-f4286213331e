/**
 * Comprehensive Memory Analysis Script
 * Run with: node --expose-gc run-memory-analysis.js
 * 
 * This script will:
 * 1. Profile the "globa" search with detailed memory tracking
 * 2. Generate heap dumps for analysis
 * 3. Track object counts and memory usage
 * 4. Test manual memory cleanup
 * 5. Provide recommendations for optimization
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔬 Comprehensive Memory Analysis for "globa" Search');
console.log('=' .repeat(60));
console.log('');

// Check if server is running
async function checkServer() {
    return new Promise((resolve) => {
        const axios = require('axios');
        axios.get('http://localhost:3000/health')
            .then(() => resolve(true))
            .catch(() => resolve(false));
    });
}

// Run the profiling test
async function runProfilingTest() {
    console.log('🎯 Step 1: Running Detailed Memory Profiling Test');
    console.log('-'.repeat(50));
    
    return new Promise((resolve, reject) => {
        const testProcess = spawn('node', ['--expose-gc', 'test-globa-search-profiling.js'], {
            cwd: __dirname,
            stdio: 'inherit'
        });

        testProcess.on('close', (code) => {
            if (code === 0) {
                console.log('✅ Profiling test completed successfully');
                resolve();
            } else {
                console.error(`❌ Profiling test failed with code ${code}`);
                reject(new Error(`Test failed with code ${code}`));
            }
        });

        testProcess.on('error', (error) => {
            console.error('❌ Error running profiling test:', error);
            reject(error);
        });
    });
}

// Analyze generated reports
function analyzeReports() {
    console.log('\n🔍 Step 2: Analyzing Generated Reports');
    console.log('-'.repeat(50));
    
    const logsDir = path.join(__dirname, 'logs');
    
    if (!fs.existsSync(logsDir)) {
        console.log('❌ No logs directory found');
        return;
    }

    const files = fs.readdirSync(logsDir);
    const reportFiles = files.filter(f => f.startsWith('memory-profile-') && f.endsWith('.json'));
    const heapDumpFiles = files.filter(f => f.startsWith('heapdump-') && f.endsWith('.heapsnapshot'));

    console.log(`📄 Found ${reportFiles.length} memory profile reports`);
    console.log(`💾 Found ${heapDumpFiles.length} heap dump files`);

    // Analyze the most recent report
    if (reportFiles.length > 0) {
        const latestReport = reportFiles.sort().pop();
        const reportPath = path.join(logsDir, latestReport);
        
        try {
            const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
            console.log(`\n📊 Analysis of ${latestReport}:`);
            console.log(`   Operation: ${report.operationName}`);
            console.log(`   Duration: ${report.totalDuration}ms`);
            console.log(`   Snapshots: ${report.snapshots.length}`);
            
            if (report.snapshots.length >= 2) {
                const start = report.snapshots[0];
                const end = report.snapshots[report.snapshots.length - 1];
                const heapIncrease = end.memoryUsage.heapUsed - start.memoryUsage.heapUsed;
                
                console.log(`   Memory Change: ${(heapIncrease / 1024 / 1024).toFixed(1)}MB`);
                console.log(`   Start Heap: ${(start.memoryUsage.heapUsed / 1024 / 1024).toFixed(1)}MB`);
                console.log(`   End Heap: ${(end.memoryUsage.heapUsed / 1024 / 1024).toFixed(1)}MB`);
            }
            
            if (report.memoryLeaks && report.memoryLeaks.length > 0) {
                console.log(`\n🚨 Memory Leaks Detected:`);
                report.memoryLeaks.forEach(leak => {
                    console.log(`   - ${leak.type}: ${leak.description} (${leak.severity})`);
                    console.log(`     Recommendation: ${leak.recommendation}`);
                });
            }
            
            if (report.recommendations && report.recommendations.length > 0) {
                console.log(`\n💡 Recommendations:`);
                report.recommendations.forEach(rec => {
                    console.log(`   - ${rec}`);
                });
            }
            
        } catch (error) {
            console.error(`❌ Error reading report ${latestReport}:`, error.message);
        }
    }

    // List heap dump files for manual analysis
    if (heapDumpFiles.length > 0) {
        console.log(`\n💾 Heap Dump Files for Chrome DevTools Analysis:`);
        heapDumpFiles.forEach(file => {
            const filePath = path.join(logsDir, file);
            const stats = fs.statSync(filePath);
            console.log(`   - ${file} (${(stats.size / 1024 / 1024).toFixed(1)}MB)`);
        });
        
        console.log(`\n📋 To analyze heap dumps:`);
        console.log(`   1. Open Chrome DevTools (F12)`);
        console.log(`   2. Go to Memory tab`);
        console.log(`   3. Click "Load" and select a .heapsnapshot file`);
        console.log(`   4. Compare snapshots to identify memory leaks`);
    }
}

// Test memory cleanup utilities
function testMemoryCleanup() {
    console.log('\n🧹 Step 3: Testing Memory Cleanup Utilities');
    console.log('-'.repeat(50));
    
    try {
        const memoryCleanup = require('./src/utils/memoryCleanup');
        
        // Test object pool creation
        console.log('🏊 Testing Object Pools:');
        memoryCleanup.createObjectPool('testPool', 
            () => ({ data: [], processed: false }), 
            (obj) => { obj.data.length = 0; obj.processed = false; },
            10
        );
        
        // Test pool usage
        const obj1 = memoryCleanup.getFromPool('testPool');
        const obj2 = memoryCleanup.getFromPool('testPool');
        memoryCleanup.returnToPool('testPool', obj1);
        memoryCleanup.returnToPool('testPool', obj2);
        
        const stats = memoryCleanup.getPoolStats('testPool');
        console.log(`   Pool efficiency: ${stats.efficiency.toFixed(1)}%`);
        console.log(`   Objects created: ${stats.created}, reused: ${stats.reused}`);
        
        // Test memory report
        console.log('\n📊 Memory Report:');
        const report = memoryCleanup.getMemoryReport();
        console.log(`   Heap Used: ${report.memoryUsage.heapUsed}`);
        console.log(`   Object Pools: ${report.objectPools.length}`);
        console.log(`   Cleanup Callbacks: ${report.cleanupCallbacks}`);
        
        if (report.recommendations.length > 0) {
            console.log(`   Recommendations:`);
            report.recommendations.forEach(rec => {
                console.log(`     - ${rec}`);
            });
        }
        
        // Test garbage collection
        console.log('\n🗑️ Testing Garbage Collection:');
        const gcResult = memoryCleanup.forceGarbageCollection();
        if (gcResult) {
            console.log(`   Memory freed: ${(gcResult.freed / 1024 / 1024).toFixed(1)}MB`);
        }
        
    } catch (error) {
        console.error('❌ Error testing memory cleanup:', error.message);
    }
}

// Generate final recommendations
function generateRecommendations() {
    console.log('\n💡 Step 4: Final Recommendations');
    console.log('-'.repeat(50));
    
    console.log('Based on the memory analysis, here are the key recommendations:');
    console.log('');
    
    console.log('🎯 Immediate Actions:');
    console.log('   1. Review the generated memory profile reports');
    console.log('   2. Analyze heap dumps in Chrome DevTools');
    console.log('   3. Identify the largest memory consumers');
    console.log('   4. Look for objects that are not being garbage collected');
    console.log('');
    
    console.log('🔧 Code Optimizations:');
    console.log('   1. Implement object pooling for frequently created objects');
    console.log('   2. Clear large arrays and objects explicitly after use');
    console.log('   3. Avoid caching large temporary lists unless necessary');
    console.log('   4. Use streaming for large datasets');
    console.log('   5. Force garbage collection at strategic points');
    console.log('');
    
    console.log('📊 Monitoring:');
    console.log('   1. Set up continuous memory monitoring');
    console.log('   2. Implement automatic cleanup triggers');
    console.log('   3. Add memory usage alerts');
    console.log('   4. Regular heap dump analysis');
    console.log('');
    
    console.log('🚨 Emergency Measures:');
    console.log('   1. Implement circuit breakers for memory-intensive operations');
    console.log('   2. Add request throttling during high memory usage');
    console.log('   3. Automatic service restart when memory exceeds limits');
    console.log('   4. Graceful degradation for broad searches');
}

// Main execution
async function main() {
    try {
        // Check if GC is available
        if (!global.gc) {
            console.log('⚠️  Warning: Garbage collection not available');
            console.log('   For best results, run with: node --expose-gc run-memory-analysis.js');
            console.log('');
        }

        // Check if server is running
        console.log('🔍 Checking server status...');
        const serverRunning = await checkServer();
        
        if (!serverRunning) {
            console.log('❌ Server is not running on http://localhost:3000');
            console.log('   Please start the server first: npm start');
            process.exit(1);
        }
        
        console.log('✅ Server is running and accessible');
        console.log('');

        // Run profiling test
        await runProfilingTest();
        
        // Wait a bit for files to be written
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Analyze reports
        analyzeReports();
        
        // Test memory cleanup
        testMemoryCleanup();
        
        // Generate recommendations
        generateRecommendations();
        
        console.log('\n🎉 Memory analysis completed successfully!');
        console.log('');
        console.log('📁 Check the logs/ directory for detailed reports and heap dumps');
        
    } catch (error) {
        console.error('\n❌ Memory analysis failed:', error.message);
        process.exit(1);
    }
}

// Handle errors
process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection:', reason);
    process.exit(1);
});

process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    process.exit(1);
});

// Run the analysis
main();
