{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "cypress:open": "cypress open", "cypress:run": "cypress run", "test:e2e": "start-server-and-test start 3000 cypress:run"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.2.2", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-form": "^0.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-primitive": "^2.0.2", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.5", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@tanstack/react-query": "^5.18.1", "@tanstack/react-virtual": "^3.13.6", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "axios": "^1.6.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "framer-motion": "^12.4.7", "js-cookie": "^3.0.5", "lucide-react": "^0.476.0", "react": "^18.3.1", "react-datepicker": "^8.1.0", "react-day-picker": "^9.5.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.1", "react-intersection-observer": "^9.16.0", "react-router-dom": "^6.22.0", "recharts": "^2.15.1", "shadcn-ui": "^0.9.4", "socket.io-client": "^4.8.1", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@radix-ui/primitive": "^1.1.1", "@radix-ui/react-alert-dialog": "^1.1.6", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.17", "cypress": "^12.17.4", "cypress-multi-reporters": "^2.0.5", "eslint": "^9.17.0", "eslint-plugin-cypress": "^4.2.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "mocha-junit-reporter": "^2.2.1", "postcss": "^8.4.33", "start-server-and-test": "^2.0.11", "tailwindcss": "^3.4.1", "terser": "^5.40.0", "vite": "^6.3.5"}}