// Debug BillionConnect sync process
require('dotenv').config();

const { EsimPlan, Provider, Country } = require('./src/models');
const billionconnectService = require('./src/services/billionconnect.service');
const providerFactory = require('./src/services/provider.factory');

// Helper function to check if a provider's plans are currently hidden
const isProviderPlansHidden = async (providerId) => {
    if (!providerId) return false;

    try {
        // Check if there are any visible plans for this provider
        const visiblePlanCount = await EsimPlan.count({
            where: {
                providerId: providerId,
                status: 'visible',
                isActive: true
            }
        });

        // Check if there are any plans at all for this provider
        const totalPlanCount = await EsimPlan.count({
            where: {
                providerId: providerId,
                isActive: true
            }
        });

        // If there are plans but none are visible, the provider is hidden
        return totalPlanCount > 0 && visiblePlanCount === 0;
    } catch (error) {
        console.error('Error checking provider plan visibility:', error);
        return false; // Default to visible if there's an error
    }
};

async function debugSync() {
    console.log('🔍 Debugging BillionConnect Sync Process...\n');

    try {
        // Step 1: Check provider
        const provider = await Provider.findOne({ where: { name: 'billionconnect' } });
        if (!provider) {
            console.log('❌ BillionConnect provider not found');
            return;
        }
        console.log('✅ Provider found:', provider.name);

        // Step 2: Get products with prices
        console.log('\n📦 Getting products with prices...');
        const externalPlans = await billionconnectService.getProductsWithPrices();
        console.log(`✅ Retrieved ${externalPlans.length} products with prices`);

        if (externalPlans.length === 0) {
            console.log('❌ No products retrieved');
            return;
        }

        // Step 3: Show first product details
        const firstProduct = externalPlans[0];
        console.log('\n📋 First Product Details:');
        console.log('Name:', firstProduct.name);
        console.log('SKU ID:', firstProduct.externalSkuId);
        console.log('Buying Price:', firstProduct.buyingPrice);
        console.log('Country:', firstProduct.country);
        console.log('Supported Countries:', firstProduct.supportedCountries);
        console.log('Has Price Data:', firstProduct.hasPriceData);

        // Step 4: Standardize the product
        console.log('\n🔄 Standardizing first product...');
        const standardizedPlan = await providerFactory.standardizeProduct(provider.name, firstProduct);
        console.log('✅ Product standardized');
        console.log('Standardized Name:', standardizedPlan.name);
        console.log('Standardized Buying Price:', standardizedPlan.buyingPrice);
        console.log('Supported Countries:', standardizedPlan.supportedCountries);
        console.log('Plan Type:', standardizedPlan.planType);
        console.log('Speed:', standardizedPlan.speed);

        // Step 5: Check country mapping
        console.log('\n🌍 Checking country mapping...');
        const allCountries = await Country.findAll({
            attributes: ['id', 'name', 'iso3']
        });
        console.log(`✅ Found ${allCountries.length} countries in database`);

        // Create country map
        const countryMap = new Map();
        allCountries.forEach(country => {
            countryMap.set(country.name.toLowerCase(), country);
            countryMap.set(country.iso3.toLowerCase(), country);
            countryMap.set(country.id.toLowerCase(), country);
        });

        // Check if supported countries exist
        const supportedCountries = standardizedPlan.supportedCountries || [];
        console.log('Supported country codes:', supportedCountries);
        
        supportedCountries.forEach(countryCode => {
            const country = countryMap.get(countryCode.toLowerCase());
            console.log(`Country ${countryCode}: ${country ? 'Found' : 'NOT FOUND'}`);
            if (country) {
                console.log(`  - ID: ${country.id}, Name: ${country.name}`);
            }
        });

        // Step 6: Check existing plans
        console.log('\n📊 Checking existing BillionConnect plans...');
        const existingPlans = await EsimPlan.findAll({
            where: { providerId: provider.id },
            include: [
                {
                    model: Country,
                    as: 'countries',
                    through: { attributes: [] }
                }
            ]
        });
        console.log(`✅ Found ${existingPlans.length} existing BillionConnect plans`);

        existingPlans.forEach((plan, index) => {
            console.log(`\nPlan ${index + 1}:`);
            console.log(`  Name: ${plan.name}`);
            console.log(`  SKU: ${plan.externalSkuId}`);
            console.log(`  Buying Price: $${plan.buyingPrice}`);
            console.log(`  Plan Type: ${plan.planType}`);
            console.log(`  Speed: ${plan.speed}`);
            console.log(`  Countries: ${plan.countries.map(c => c.name).join(', ')}`);
            console.log(`  Is Active: ${plan.isActive}`);
        });

        // Step 7: Test creating one plan manually
        console.log('\n🧪 Testing manual plan creation...');
        
        // Set category to esim_realtime for BillionConnect plans
        standardizedPlan.category = 'esim_realtime';
        
        // Check if plan already exists
        let testPlan = await EsimPlan.findOne({
            where: {
                externalSkuId: standardizedPlan.externalSkuId
            }
        });

        if (testPlan) {
            console.log('✅ Plan already exists, updating...');
            await testPlan.update({
                ...standardizedPlan,
                providerId: provider.id,
                isActive: true
            });
        } else {
            console.log('✅ Creating new plan...');

            // Check if provider's plans are currently hidden
            const providerPlansHidden = await isProviderPlansHidden(provider.id);
            console.log(`👁️ Provider plans hidden: ${providerPlansHidden}`);

            testPlan = await EsimPlan.create({
                ...standardizedPlan,
                providerId: provider.id,
                productId: `BC${Date.now()}`, // Generate a simple product ID
                // Set status based on provider's current visibility
                status: providerPlansHidden ? 'hidden' : 'visible',
                isActive: true
            });
        }

        // Handle country associations
        console.log('\n🌍 Setting up country associations...');
        await testPlan.setCountries([]);

        if (Array.isArray(supportedCountries) && supportedCountries.length > 0) {
            const countryAssociations = [];
            supportedCountries.forEach(countryCode => {
                const country = countryMap.get(countryCode.toLowerCase());
                if (country) {
                    countryAssociations.push({
                        countryId: country.id,
                        isDefault: countryAssociations.length === 0
                    });
                    console.log(`✅ Will associate with country: ${country.name} (${country.id})`);
                } else {
                    console.log(`❌ Country not found for code: ${countryCode}`);
                }
            });

            if (countryAssociations.length > 0) {
                await testPlan.addCountries(
                    countryAssociations.map(assoc => assoc.countryId),
                    {
                        through: countryAssociations.map(assoc => ({
                            isDefault: assoc.isDefault
                        }))
                    }
                );
                console.log(`✅ Associated plan with ${countryAssociations.length} countries`);
            }
        }

        // Verify the plan was created with countries
        const verifyPlan = await EsimPlan.findByPk(testPlan.id, {
            include: [
                {
                    model: Country,
                    as: 'countries',
                    through: { attributes: [] }
                }
            ]
        });

        console.log('\n✅ Plan verification:');
        console.log(`Name: ${verifyPlan.name}`);
        console.log(`Buying Price: $${verifyPlan.buyingPrice}`);
        console.log(`Plan Type: ${verifyPlan.planType}`);
        console.log(`Speed: ${verifyPlan.speed}`);
        console.log(`Countries: ${verifyPlan.countries.map(c => c.name).join(', ')}`);

        console.log('\n🎉 Debug sync completed successfully!');

    } catch (error) {
        console.error('❌ Debug sync failed:', error);
        console.error('Stack trace:', error.stack);
    }
}

debugSync();
