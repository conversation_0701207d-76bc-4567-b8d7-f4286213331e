#!/usr/bin/env node

/**
 * Emergency memory cleanup script
 * Use this when the application is experiencing memory issues
 */

const axios = require('axios');

class EmergencyMemoryCleanup {
    constructor() {
        this.baseUrl = process.env.BASE_URL || 'http://localhost:3000';
    }

    /**
     * Get current memory status
     */
    async getMemoryStatus() {
        try {
            const response = await axios.get(`${this.baseUrl}/emergency/memory/status`);
            return response.data;
        } catch (error) {
            console.error('Error getting memory status:', error.message);
            return null;
        }
    }

    /**
     * Perform emergency cleanup
     */
    async performCleanup() {
        try {
            console.log('🚨 Performing emergency memory cleanup...');
            const response = await axios.post(`${this.baseUrl}/emergency/memory/cleanup`);
            return response.data;
        } catch (error) {
            console.error('Error performing cleanup:', error.message);
            return null;
        }
    }

    /**
     * Force garbage collection
     */
    async forceGC() {
        try {
            console.log('🗑️ Forcing garbage collection...');
            const response = await axios.post(`${this.baseUrl}/emergency/memory/gc`);
            return response.data;
        } catch (error) {
            console.error('Error forcing GC:', error.message);
            return null;
        }
    }

    /**
     * Display memory status in a readable format
     */
    displayStatus(status) {
        if (!status || !status.success) {
            console.log('❌ Failed to get memory status');
            return;
        }

        const data = status.data;
        console.log('\n📊 Memory Status:');
        console.log(`   Heap Used: ${data.memory.heapUsedMB}MB (${data.memory.heapUsagePercent.toFixed(1)}%)`);
        console.log(`   Heap Total: ${data.memory.heapTotalMB}MB`);
        console.log(`   Max Heap: ${data.memory.maxHeapMB}MB`);
        console.log(`   RSS: ${(data.memory.rss / 1024 / 1024).toFixed(1)}MB`);

        console.log('\n💾 Cache Status:');
        console.log(`   Total Entries: ${data.cache.totalEntries}`);
        console.log(`   Valid Entries: ${data.cache.validEntries}`);
        console.log(`   Expired Entries: ${data.cache.expiredEntries}`);
        console.log(`   Hit Rate: ${data.cache.hitRate ? (data.cache.hitRate * 100).toFixed(1) + '%' : 'N/A'}`);

        console.log('\n🔍 Memory Leak Detection:');
        console.log(`   Detected: ${data.memoryLeak.detected ? '❌ YES' : '✅ NO'}`);
        console.log(`   Score: ${data.memoryLeak.score}/4`);

        console.log('\n🚦 Status Indicators:');
        console.log(`   Safe to Process: ${data.status.isSafeToProcess ? '✅' : '❌'}`);
        console.log(`   Warning Level: ${data.status.isWarning ? '⚠️' : '✅'}`);
        console.log(`   Critical Level: ${data.status.isCritical ? '🚨' : '✅'}`);

        if (data.recommendations && data.recommendations.length > 0) {
            console.log('\n💡 Recommendations:');
            data.recommendations.forEach(rec => {
                console.log(`   • ${rec}`);
            });
        }
    }

    /**
     * Display cleanup results
     */
    displayCleanupResults(result) {
        if (!result || !result.success) {
            console.log('❌ Cleanup failed');
            return;
        }

        const data = result.data;
        console.log('\n✅ Emergency Cleanup Completed:');
        console.log(`   Memory Before: ${data.before.memoryUsageMB.toFixed(1)}MB (${data.before.memoryUsagePercent.toFixed(1)}%)`);
        console.log(`   Memory After: ${data.after.memoryUsageMB.toFixed(1)}MB (${data.after.memoryUsagePercent.toFixed(1)}%)`);
        console.log(`   Memory Freed: ${data.freed.memoryMB.toFixed(1)}MB`);
        console.log(`   Cache Entries Before: ${data.before.cacheEntries}`);
        console.log(`   Cache Entries After: ${data.after.cacheEntries}`);
        console.log(`   Cache Entries Cleared: ${data.freed.cacheEntries}`);
        console.log(`   GC Freed: ${data.freed.gcFreedMB}MB`);
    }

    /**
     * Display GC results
     */
    displayGCResults(result) {
        if (!result || !result.success) {
            console.log('❌ Garbage collection failed');
            return;
        }

        const data = result.data;
        console.log('\n🗑️ Garbage Collection Completed:');
        console.log(`   Memory Before: ${data.before.heapUsedMB.toFixed(1)}MB (${data.before.heapUsagePercent.toFixed(1)}%)`);
        console.log(`   Memory After: ${data.after.heapUsedMB.toFixed(1)}MB (${data.after.heapUsagePercent.toFixed(1)}%)`);
        console.log(`   Memory Freed: ${data.freedMB}MB`);
    }

    /**
     * Run emergency procedures
     */
    async runEmergency() {
        console.log('🚨 Emergency Memory Management Tool');
        console.log('=====================================\n');

        // Get initial status
        console.log('📊 Getting current memory status...');
        const initialStatus = await this.getMemoryStatus();
        if (initialStatus) {
            this.displayStatus(initialStatus);
        }

        // Check if emergency action is needed
        if (initialStatus && initialStatus.success) {
            const data = initialStatus.data;
            
            if (data.status.isCritical) {
                console.log('\n🚨 CRITICAL MEMORY USAGE DETECTED - Performing emergency cleanup...');
                const cleanupResult = await this.performCleanup();
                if (cleanupResult) {
                    this.displayCleanupResults(cleanupResult);
                }
            } else if (data.status.isWarning) {
                console.log('\n⚠️ HIGH MEMORY USAGE - Forcing garbage collection...');
                const gcResult = await this.forceGC();
                if (gcResult) {
                    this.displayGCResults(gcResult);
                }
            } else {
                console.log('\n✅ Memory usage is normal - no emergency action needed');
            }
        }

        // Get final status
        console.log('\n📊 Getting final memory status...');
        const finalStatus = await this.getMemoryStatus();
        if (finalStatus) {
            this.displayStatus(finalStatus);
        }
    }

    /**
     * Run specific action based on command line argument
     */
    async runAction(action) {
        switch (action) {
            case 'status':
                console.log('📊 Getting memory status...');
                const status = await this.getMemoryStatus();
                if (status) {
                    this.displayStatus(status);
                }
                break;

            case 'cleanup':
                console.log('🚨 Performing emergency cleanup...');
                const cleanupResult = await this.performCleanup();
                if (cleanupResult) {
                    this.displayCleanupResults(cleanupResult);
                }
                break;

            case 'gc':
                console.log('🗑️ Forcing garbage collection...');
                const gcResult = await this.forceGC();
                if (gcResult) {
                    this.displayGCResults(gcResult);
                }
                break;

            default:
                await this.runEmergency();
                break;
        }
    }
}

// Main execution
if (require.main === module) {
    const cleanup = new EmergencyMemoryCleanup();
    const args = process.argv.slice(2);
    const action = args[0];

    if (args.includes('--help')) {
        console.log('Usage: node scripts/emergency-memory-cleanup.js [action]');
        console.log('Actions:');
        console.log('  status   - Get current memory status');
        console.log('  cleanup  - Perform emergency memory cleanup');
        console.log('  gc       - Force garbage collection');
        console.log('  (none)   - Run full emergency assessment and action');
        console.log('');
        console.log('Environment Variables:');
        console.log('  BASE_URL - Base URL of the application (default: http://localhost:3000)');
        process.exit(0);
    }

    cleanup.runAction(action).catch(error => {
        console.error('Emergency cleanup failed:', error.message);
        process.exit(1);
    });
}

module.exports = EmergencyMemoryCleanup;
