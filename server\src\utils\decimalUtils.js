/**
 * Utility functions for handling decimal operations with proper precision
 * Prevents floating-point arithmetic issues in financial calculations
 */

/**
 * Add two decimal numbers with proper precision
 * @param {number} a - First number
 * @param {number} b - Second number
 * @param {number} precision - Decimal places (default: 2)
 * @returns {number} - Result with proper precision
 */
function addDecimal(a, b, precision = 2) {
    const result = Number(a) + Number(b);
    return Number(result.toFixed(precision));
}

/**
 * Subtract two decimal numbers with proper precision
 * @param {number} a - First number
 * @param {number} b - Second number
 * @param {number} precision - Decimal places (default: 2)
 * @returns {number} - Result with proper precision
 */
function subtractDecimal(a, b, precision = 2) {
    const result = Number(a) - Number(b);
    return Number(result.toFixed(precision));
}

/**
 * Multiply two decimal numbers with proper precision
 * @param {number} a - First number
 * @param {number} b - Second number
 * @param {number} precision - Decimal places (default: 2)
 * @returns {number} - Result with proper precision
 */
function multiplyDecimal(a, b, precision = 2) {
    const result = Number(a) * Number(b);
    return Number(result.toFixed(precision));
}

/**
 * Divide two decimal numbers with proper precision
 * @param {number} a - Dividend
 * @param {number} b - Divisor
 * @param {number} precision - Decimal places (default: 2)
 * @returns {number} - Result with proper precision
 */
function divideDecimal(a, b, precision = 2) {
    if (Number(b) === 0) {
        throw new Error('Division by zero');
    }
    const result = Number(a) / Number(b);
    return Number(result.toFixed(precision));
}

/**
 * Compare two decimal numbers with tolerance for floating-point precision
 * @param {number} a - First number
 * @param {number} b - Second number
 * @param {number} tolerance - Tolerance for comparison (default: 0.005)
 * @returns {boolean} - True if numbers are equal within tolerance
 */
function isDecimalEqual(a, b, tolerance = 0.005) {
    return Math.abs(Number(a) - Number(b)) < tolerance;
}

/**
 * Format a number to proper decimal precision
 * @param {number} value - Number to format
 * @param {number} precision - Decimal places (default: 2)
 * @returns {number} - Formatted number
 */
function formatDecimal(value, precision = 2) {
    return Number(Number(value).toFixed(precision));
}

/**
 * Validate that a value is a valid decimal number
 * @param {any} value - Value to validate
 * @param {number} minValue - Minimum allowed value (optional)
 * @param {number} maxValue - Maximum allowed value (optional)
 * @returns {boolean} - True if valid
 */
function isValidDecimal(value, minValue = null, maxValue = null) {
    const num = Number(value);
    
    // Check if it's a valid number
    if (isNaN(num) || !isFinite(num)) {
        return false;
    }
    
    // Check minimum value
    if (minValue !== null && num < minValue) {
        return false;
    }
    
    // Check maximum value
    if (maxValue !== null && num > maxValue) {
        return false;
    }
    
    return true;
}

/**
 * Safe decimal operation wrapper that handles errors
 * @param {Function} operation - Operation function to execute
 * @param {Array} args - Arguments for the operation
 * @param {any} defaultValue - Default value if operation fails
 * @returns {any} - Result or default value
 */
function safeDecimalOperation(operation, args, defaultValue = 0) {
    try {
        return operation(...args);
    } catch (error) {
        console.error('Decimal operation error:', error);
        return defaultValue;
    }
}

/**
 * Calculate percentage with proper decimal precision
 * @param {number} value - Value to calculate percentage of
 * @param {number} percentage - Percentage (e.g., 10 for 10%)
 * @param {number} precision - Decimal places (default: 2)
 * @returns {number} - Percentage amount
 */
function calculatePercentage(value, percentage, precision = 2) {
    return formatDecimal((Number(value) * Number(percentage)) / 100, precision);
}

/**
 * Round to nearest cent (0.01)
 * @param {number} value - Value to round
 * @returns {number} - Rounded value
 */
function roundToCent(value) {
    return Math.round(Number(value) * 100) / 100;
}

module.exports = {
    addDecimal,
    subtractDecimal,
    multiplyDecimal,
    divideDecimal,
    isDecimalEqual,
    formatDecimal,
    isValidDecimal,
    safeDecimalOperation,
    calculatePercentage,
    roundToCent
};
