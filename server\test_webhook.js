const axios = require('axios');
const { Order, EsimStock } = require('./src/models');
const billionconnectService = require('./src/services/billionconnect.service');

// Function to create webhook test data
async function createWebhookData(externalOrderId) {
    try {
        // First, get the actual order details from BillionConnect
        const orderDetails = await billionconnectService.getOrderStatus(externalOrderId);
        console.log('Retrieved order details from BillionConnect:', orderDetails);

        // Create webhook data using actual order details
        return {
            tradeType: 'N009',
            tradeTime: new Date().toISOString().replace('T', ' ').split('.')[0],
            tradeData: {
                orderId: externalOrderId,
                channelOrderId: orderDetails.channelOrderId || 'order_8ef58573-525d-11f0-966a-0ae1562662bd_1750940371748',
                subOrderList: [{
                    iccid: orderDetails.iccid,
                    qrCodeContent: orderDetails.qrCodeUrl,
                    apn: orderDetails.apn,
                    smdpAddress: orderDetails.smdpAddress,
                    lpaString: orderDetails.qrCodeUrl, // BillionConnect uses qrCodeContent as lpaString
                    activationCode: orderDetails.activationCode
                }]
            }
        };
    } catch (error) {
        console.error('Error getting order details from BillionConnect:', error);
        console.log('Falling back to test data...');
        
        // Fallback to test data if we can't get actual details
    return {
        tradeType: 'N009',
            tradeTime: new Date().toISOString().replace('T', ' ').split('.')[0],
        tradeData: {
            orderId: externalOrderId,
                channelOrderId: 'order_8ef58573-525d-11f0-966a-0ae1562662bd_1750940371748',
            subOrderList: [{
                iccid: '8988228045500123456',
                qrCodeContent: 'LPA:1$test.example.com$1234-5678-90',
                apn: 'internet',
                smdpAddress: 'test.example.com',
                lpaString: 'LPA:1$test.example.com$1234-5678-90',
                activationCode: '1234-5678-90'
            }]
        }
    };
    }
}

async function checkOrderStatus(orderId) {
    try {
        const order = await Order.findOne({
            where: { externalOrderId: orderId },
            include: [{ model: EsimStock, as: 'stock' }]
        });

        if (!order) {
            console.log('Order not found');
            return;
        }

        console.log('Order details:');
        console.log('- Status:', order.status);
        console.log('- Order ID:', order.id);
        console.log('- External Order ID:', order.externalOrderId);
        
        if (order.stock) {
            console.log('Stock details:');
            console.log('- ICCID:', order.stock.iccid);
            console.log('- QR Code:', order.stock.qrCodeUrl);
            console.log('- Status:', order.stock.status);
        } else {
            console.log('No stock record found');
        }
    } catch (error) {
        console.error('Error checking order status:', error);
    }
}

async function testWebhook(externalOrderId) {
    try {
        const webhookData = await createWebhookData(externalOrderId);
        const WEBHOOK_URL = 'https://api.vizlync.net/api/orders/webhook/billionconnect';
    
    console.log('Sending webhook request to:', WEBHOOK_URL);
    console.log('With data:', JSON.stringify(webhookData, null, 2));
    
        const response = await axios.post(WEBHOOK_URL, webhookData, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 30000 // 30 second timeout
        });
        
        console.log('Webhook Response:', {
            status: response.status,
            statusText: response.statusText,
            data: response.data
        });

        // Wait a moment for the database to update
        console.log('Waiting for order processing...');
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Check the order status
        await checkOrderStatus(externalOrderId);
    } catch (error) {
        console.error('Error sending webhook:');
        if (error.response) {
            // The server responded with a status code outside of 2xx
            console.error('Server responded with error:', {
                status: error.response.status,
                statusText: error.response.statusText,
                data: error.response.data
            });
        } else if (error.request) {
            // The request was made but no response was received
            console.error('No response received from server');
            console.error('Request details:', {
                method: error.request.method,
                path: error.request.path,
                headers: error.request.headers
            });
        } else {
            // Something happened in setting up the request
            console.error('Error setting up request:', error.message);
        }
    }
}

// Get externalOrderId from command line argument
const externalOrderId = process.argv[2];

if (!externalOrderId) {
    console.error('Please provide an externalOrderId as a command line argument');
    console.error('Usage: node test_webhook.js <externalOrderId>');
    process.exit(1);
}

console.log('Testing webhook for externalOrderId:', externalOrderId);
// Run the test once
testWebhook(externalOrderId); 