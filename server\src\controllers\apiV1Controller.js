const { EsimPlan, Country, Order, EsimStock, User, Wallet, WalletTransaction, Provider, EsimPlanCountries, EsimPlanStockHistory } = require('../models');
const models = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');
const generateOrderId = require('../utils/generateOrderId');
const { v4: uuidv4 } = require('uuid');
const QRCode = require('qrcode');
const providerFactory = require('../services/provider.factory');
const mobimatterService = require('../services/mobimatter.service');
const { getCachedPlan, setCachePlan, invalidatePlanCache } = require('../utils/cacheManager');
const emailService = require('../utils/emailService');
const memoryMonitor = require('../utils/memoryMonitor');
const cacheService = require('../services/cache.service');
const productCacheService = require('../services/productCache.service');

// Helper function to validate ISO date format (YYYY-MM-DD)
const isValidISODate = (dateString) => {
    if (!/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
        return false;
    }
    
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
        return false;
    }
    
    return date.toISOString().slice(0, 10) === dateString;
};

/**
 * Get all available products with ultra-fast caching
 * @route GET /api/v1/products
 */
exports.getProducts = async (req, res) => {
    const startTime = Date.now();

    try {
        // CRITICAL FIX: Memory circuit breaker - reject requests if memory is too high
        if (memoryMonitor.isMemoryCritical()) {
            console.warn('🚨 CIRCUIT BREAKER: Rejecting products API request due to critical memory usage');
            return res.status(503).json({
                success: false,
                error: {
                    code: 'SERVICE_UNAVAILABLE',
                    message: 'Service temporarily unavailable due to high memory usage. Please try again later.'
                }
            });
        }

        const { region, country } = req.query;
        const partner = req.partner;

        // Create cache key using the service
        const cacheKey = productCacheService.generateCacheKey(partner.id, region, country);

        // Check cache first - return immediately if cached
        const cachedData = getCachedPlan(cacheKey);
        if (cachedData) {
            const responseTime = Date.now() - startTime;
            console.log(`🚀 Cache HIT for products API: ${responseTime}ms`);
            return res.json(cachedData);
        }

        console.log(`💾 Cache MISS for products API, generating fresh data...`);

        // Double-check memory before proceeding with database operations
        if (memoryMonitor.isMemoryWarning()) {
            console.warn('⚠️ Memory warning - proceeding with caution...');
        }

        // Get base products from shared cache (much more efficient!)
        let products = await productCacheService.getBaseProducts();
        console.log(`📊 Retrieved ${products.length} products from base cache`);

        // Apply filters if needed (in memory - very fast)
        if (region) {
            products = products.filter(product =>
                product.region && product.region.toLowerCase().includes(region.toLowerCase())
            );
            console.log(`🔍 Filtered to ${products.length} products by region: ${region}`);
        }

        if (country) {
            const countryUpper = country.toUpperCase();
            products = products.filter(product => {
                if (product.provider?.name === 'Mobimatter') {
                    // For Mobimatter, check provider metadata
                    const supportedCountries = product.providerMetadata?.originalData?.supportedCountries || [];
                    return supportedCountries.includes(countryUpper);
                } else {
                    // For local plans, check countries association
                    return product.countries && product.countries.some(c => c.id === countryUpper);
                }
            });
            console.log(`� Filtered to ${products.length} products by country: ${country}`);
        }

        // Transform products using cache service (applies partner-specific pricing)
        const formattedProducts = productCacheService.transformProducts(products, partner);

        // Prepare response
        const responseData = {
            success: true,
            data: {
                products: formattedProducts,
                total: formattedProducts.length
            }
        };

        // Cache the response for 5 minutes
        setCachePlan(cacheKey, responseData, 300 * 1000);

        const responseTime = Date.now() - startTime;
        console.log(`✅ Generated fresh products data in ${responseTime}ms`);

        res.json(responseData);

    } catch (error) {
        console.error('Error fetching products:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_SERVER_ERROR',
                message: 'Failed to fetch products'
            }
        });
    }
};

// Helper function to generate QR code as data URL
const generateQRCode = async (text) => {
    try {
        return await QRCode.toDataURL(text, {
            errorCorrectionLevel: 'H',
            type: 'image/png',
            margin: 2,
            width: 400,
            color: {
                dark: '#000000',
                light: '#ffffff'
            },
            rendererOpts: {
                quality: 1
            }
        });
    } catch (err) {
        console.error('Error generating QR code:', err);
        return null;
    }
};

/**
 * Create a new order
 * @route POST /api/v1/order
 */
exports.createOrder = async (req, res) => {
    const t = await sequelize.transaction();
    try {
        // Get partner from request (added by authenticateApiKey middleware)
        const partner = req.partner;
        const userId = partner.id;

        // Validate request body
        const { productId, startDate } = req.body;
        
        if (!productId) {
            await t.rollback();
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_REQUEST',
                    message: 'Product ID is required'
                }
            });
        }
        
        // Validate productId format (should be a string of alphanumeric characters)
        if (typeof productId !== 'string' || !/^[A-Z0-9]{6,12}$/.test(productId)) {
            await t.rollback();
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_PRODUCT_ID',
                    message: 'Invalid product ID format'
                }
            });
        }
        
        // Validate startDate format if provided
        if (startDate && !isValidISODate(startDate)) {
            await t.rollback();
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_DATE_FORMAT',
                    message: 'Start date must be in ISO format (YYYY-MM-DD)'
                }
            });
        }
        
        // Get plan details
        const plan = await EsimPlan.findOne({
            where: { 
                productId,
                status: 'visible',
                isActive: true
            },
            include: [{
                model: Provider,
                as: 'provider',
                attributes: ['id', 'name', 'type', 'country']
            }, {
                model: Country,
                as: 'countries',
                through: { attributes: [] }
            }],
            transaction: t
        });

        if (!plan) {
            await t.rollback();
            return res.status(404).json({
                success: false,
                error: {
                    code: 'PRODUCT_NOT_FOUND',
                    message: 'Product not found or not available'
                }
            });
        }
        
        // Calculate the price based on the partner's markup if sellingPrice is null
        let orderTotal = plan.sellingPrice;
        if (orderTotal === null && partner.markupPercentage) {
            const markup = parseFloat(partner.markupPercentage) / 100;
            orderTotal = parseFloat(plan.buyingPrice) * (1 + markup);
            // Round to 2 decimal places
            orderTotal = Math.round(orderTotal * 100) / 100;
        }
        
        // Check if the eSIM plan allows start date
        if (startDate && !plan.startDateEnabled) {
            await t.rollback();
            return res.status(400).json({
                success: false,
                error: {
                    code: 'START_DATE_NOT_ALLOWED',
                    message: 'This product does not support setting a start date'
                }
            });
        }

        // Get partner's wallet
        const wallet = await Wallet.findOne({
            where: { userId },
            transaction: t,
            lock: true
        });

        if (!wallet) {
            await t.rollback();
            return res.status(400).json({
                success: false,
                error: {
                    code: 'WALLET_NOT_FOUND',
                    message: 'Partner wallet not found'
                }
            });
        }

        // Check wallet balance
        if (parseFloat(wallet.balance) < orderTotal) {
            await t.rollback();
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INSUFFICIENT_BALANCE',
                    message: 'Insufficient wallet balance',
                    required: orderTotal,
                    available: parseFloat(wallet.balance)
                }
            });
        }

        let order = null;
        const walletAuthTransactionId = uuidv4();
        
        try {
            // Check if the plan is from an external provider (e.g., Mobimatter)
            const provider = await plan.getProvider({ transaction: t });
            
            if (provider && provider.type === 'API') {
                // Store provider in plan object for later use
                plan.provider = provider;
                
                // Get provider service instance
                const providerService = providerFactory.getProvider(provider.name);
                
                // Create initial order record with pending status
                order = await Order.create({
                    userId,
                    esimPlanId: plan.id,
                    esimStockId: null,
                    quantity: 1,
                    orderTotal,
                    startDate: startDate || null,
                    status: 'pending',
                    walletAuthTransactionId
                }, { transaction: t });
                
                // Create external order with the provider
                const externalOrder = await providerService.createOrder({
                    productId: plan.externalProductId,
                    skuId: plan.externalSkuId,
                    quantity: 1,
                    customerReference: `api_${userId}_${Date.now()}`
                });
                
                if (!externalOrder || !externalOrder.orderId) {
                    throw new Error('Invalid response from provider: Missing order ID');
                }

                // Different flow for BillionConnect vs other providers
                if (plan.provider.name === 'Billionconnect') {
                    // For BillionConnect, we wait for the N009 webhook
                    await order.update({
                        externalOrderId: externalOrder.orderId,
                        providerResponse: externalOrder.providerResponse,
                        providerMetadata: {},
                        providerOrderStatus: 'PENDING',
                        lastProviderCheck: new Date()
                    }, { transaction: t });

                    console.log('BillionConnect order created, waiting for webhook:', {
                        orderId: order.id,
                        externalOrderId: externalOrder.orderId
                    });

                    // Process wallet transaction for BillionConnect orders
                    const newBalance = parseFloat(wallet.balance) - orderTotal;
                    await WalletTransaction.create({
                        id: uuidv4(),
                        walletId: wallet.id,
                        type: 'debit',
                        amount: orderTotal,
                        balance: newBalance,
                        description: `Payment for API order: ${order.id}`,
                        status: 'completed',
                        referenceType: 'order',
                        referenceId: order.id,
                        metadata: {
                            orderId: order.id,
                            totalAmount: orderTotal,
                            api: true,
                            provider: 'Billionconnect'
                        }
                    }, { transaction: t });

                    // Update wallet balance
                    await wallet.update({
                        balance: newBalance,
                        updatedAt: new Date()
                    }, { transaction: t });

                    // Return early for BillionConnect orders
                    await t.commit();

                    // Note: No initial confirmation email for BillionConnect API orders
                    // The final email with eSIM details will be sent via webhook when order is processed
                    console.log('BillionConnect API order created, email will be sent via webhook when processed');

                    // Trigger notification processor to handle any pending notifications
                    const notificationProcessor = require('../services/notificationProcessor.service');
                    notificationProcessor.triggerProcessing(1000); // Small delay to ensure order is fully committed

                    return res.status(201).json({
                        success: true,
                        orderId: order.id,
                        message: 'Order created successfully. eSIM details will be available once the order is processed.'
                    });
                }

                // For other providers, continue with existing flow
                const completedOrder = providerService.completeOrder ? 
                    await providerService.completeOrder(externalOrder.orderId) : 
                    externalOrder;
                
                // Extract eSIM details from the completed order response
                let iccid, smdpAddress, lpaString, accessPointName, activationCode, phoneNumber, qrCodeUrl;
                
                // Check if response contains orderLineItem.lineItemDetails
                if (completedOrder.orderLineItem?.lineItemDetails) {
                    const lineItems = completedOrder.orderLineItem.lineItemDetails;
                    const getLineItemValue = (name) => lineItems.find(item => item.name === name)?.value || '';
                    
                    iccid = getLineItemValue('ICCID');
                    smdpAddress = getLineItemValue('SMDP_ADDRESS');
                    lpaString = getLineItemValue('LOCAL_PROFILE_ASSISTANT');
                    accessPointName = getLineItemValue('ACCESS_POINT_NAME');
                    activationCode = getLineItemValue('ACTIVATION_CODE');
                    phoneNumber = getLineItemValue('PHONE_NUMBER');
                    qrCodeUrl = getLineItemValue('QR_CODE');
                    
                    // Generate QR code if not provided but LPA is available
                    if (!qrCodeUrl && lpaString) {
                        try {
                            qrCodeUrl = await generateQRCode(lpaString);
                        } catch (error) {
                            console.error('Error generating QR code:', error);
                        }
                    }
                } else {
                    // Direct properties in the response
                    iccid = completedOrder.iccid;
                    smdpAddress = completedOrder.smdpAddress;
                    lpaString = completedOrder.lpaString || completedOrder.activationCode;
                    accessPointName = completedOrder.accessPointName;
                    activationCode = completedOrder.activationCode;
                    phoneNumber = completedOrder.phoneNumber;
                    qrCodeUrl = completedOrder.qrCodeUrl;
                    
                    // Generate QR code if not provided
                    if (!qrCodeUrl && lpaString) {
                        try {
                            qrCodeUrl = await generateQRCode(lpaString);
                        } catch (error) {
                            console.error('Error generating QR code:', error);
                        }
                    }
                }
                
                // Create eSIM stock record
                const stockData = {
                    id: uuidv4(),
                    esimPlanId: plan.id,
                    iccid: iccid,
                    smdpAddress: smdpAddress,
                    lpaString: lpaString,
                    accessPointName: accessPointName,
                    activationCode: activationCode,
                    phoneNumber: phoneNumber,
                    qrCodeUrl: qrCodeUrl,
                    status: 'assigned',
                    externalStockId: completedOrder.orderId,
                    externalIccid: iccid,
                    providerStatus: completedOrder.status || 'completed',
                    providerMetadata: completedOrder,
                    walletAuthTransactionId: walletAuthTransactionId,
                    orderId: order.id,
                    orderDate: new Date()
                };
                
                // Save stock record
                const stock = await EsimStock.create(stockData, { transaction: t });
                
                // Create stock history record - check if one already exists first
                const existingStockHistory = await EsimPlanStockHistory.findOne({
                    where: { orderId: order.id },
                    transaction: t
                });

                if (!existingStockHistory) {
                    try {
                        await EsimPlanStockHistory.create({
                            id: uuidv4(),
                            esimPlanId: plan.id,
                            esimStockId: stock.id,
                            iccid: iccid,
                            smdpAddress: smdpAddress,
                            lpaString: lpaString,
                            accessPointName: accessPointName,
                            activationCode: activationCode,
                            phoneNumber: phoneNumber,
                            orderId: order.id,
                            orderDate: new Date(),
                            quantity: 1,
                            status: 'assigned',
                            reason: 'API Order placement',
                            createdBy: userId
                        }, { transaction: t });
                    } catch (error) {
                        if (error.name === 'SequelizeUniqueConstraintError') {
                            console.log(`Stock history already exists for order ${order.id}, skipping creation`);
                        } else {
                            throw error;
                        }
                    }
                }
                
                // Prepare provider metadata
                const providerMetadata = {
                    iccid: iccid,
                    smdpAddress: smdpAddress,
                    lpaString: lpaString,
                    accessPointName: accessPointName,
                    activationCode: activationCode,
                    phoneNumber: phoneNumber,
                    qrCodeUrl: qrCodeUrl,
                    providerStatus: completedOrder.status,
                    expiryDate: completedOrder.expiryDate
                };
                
                // Update our order record with the external order details
                await order.update({
                    status: 'completed',
                    esimStockId: stock.id,
                    externalOrderId: externalOrder.orderId,
                    providerResponse: completedOrder,
                    providerMetadata: providerMetadata,
                    expiryDate: completedOrder.expiryDate
                }, { transaction: t });
                
            } else {
                // Handle local stock order - FIFO (First In, First Out)
                const stock = await EsimStock.findOne({
                    where: {
                        esimPlanId: plan.id,
                        status: 'available'
                    },
                    order: [['createdAt', 'ASC']], // FIFO: oldest stock first
                    transaction: t,
                    lock: true
                });
                
                if (!stock) {
                    throw new Error(`No stock available for plan: ${plan.name}`);
                }
                
                // Create order record
                order = await Order.create({
                    userId,
                    esimPlanId: plan.id,
                    esimStockId: stock.id,
                    quantity: 1,
                    orderTotal,
                    startDate: startDate || null,
                    status: 'completed',
                    walletAuthTransactionId,
                    providerResponse: stock.providerMetadata || null,
                    providerMetadata: {
                        iccid: stock.iccid,
                        smdpAddress: stock.smdpAddress,
                        lpaString: stock.lpaString,
                        accessPointName: stock.accessPointName,
                        activationCode: stock.activationCode,
                        phoneNumber: stock.phoneNumber,
                        qrCodeUrl: stock.qrCodeUrl,
                        providerStatus: stock.providerStatus
                    }
                }, { transaction: t });
                
                // Update stock status to assigned
                await stock.update({
                    status: 'assigned',
                    orderId: order.id,
                    orderDate: new Date()
                }, { transaction: t });
            }
            
            // Process wallet transaction
            const newBalance = parseFloat(wallet.balance) - orderTotal;
            
            // Create wallet transaction record
            await WalletTransaction.create({
                id: uuidv4(),
                walletId: wallet.id,
                type: 'debit',
                amount: orderTotal,
                balance: newBalance,
                description: `Payment for API order: ${order.id}`,
                status: 'completed',
                referenceType: 'order',
                referenceId: order.id,
                metadata: {
                    orderId: order.id,
                    totalAmount: orderTotal,
                    api: true
                }
            }, { transaction: t });
            
            // Update wallet balance
            await wallet.update({
                balance: newBalance,
                updatedAt: new Date()
            }, { transaction: t });
            
            await t.commit();

            // Send email notifications for API orders
            try {
                // Get the completed order with all relations for email
                const completedOrder = await Order.findByPk(order.id, {
                    include: [{
                        model: EsimPlan,
                        as: 'plan',
                        include: [{
                            model: Country,
                            as: 'countries',
                            through: { attributes: [] }
                        }, {
                            model: Provider,
                            as: 'provider'
                        }]
                    }, {
                        model: EsimStock,
                        as: 'stock'
                    }]
                });

                // Determine if this is an external order from a provider like Mobimatter
                const isExternalOrder = completedOrder.plan?.provider?.type === 'API';

                // Extract eSIM data for email
                let esimData = {};

                if (isExternalOrder) {
                    // For external orders, extract eSIM data from the provider response
                    if (completedOrder.providerResponse?.orderLineItem?.lineItemDetails) {
                        const lineItems = completedOrder.providerResponse.orderLineItem.lineItemDetails;

                        esimData = {
                            iccid: lineItems.find(item => item.name === 'ICCID')?.value,
                            smdpAddress: lineItems.find(item => item.name === 'SMDP_ADDRESS')?.value,
                            lpaString: lineItems.find(item => item.name === 'LOCAL_PROFILE_ASSISTANT')?.value,
                            accessPointName: lineItems.find(item => item.name === 'ACCESS_POINT_NAME')?.value,
                            activationCode: lineItems.find(item => item.name === 'ACTIVATION_CODE')?.value,
                            phoneNumber: lineItems.find(item => item.name === 'PHONE_NUMBER')?.value,
                            qrCodeUrl: lineItems.find(item => item.name === 'QR_CODE')?.value || null
                        };
                    } else {
                        // If it's not in the expected format, use providerMetadata
                        esimData = completedOrder.providerMetadata || {};
                    }
                } else {
                    // For local stock orders, use the stock data
                    esimData = {
                        iccid: completedOrder.stock?.iccid,
                        smdpAddress: completedOrder.stock?.smdpAddress,
                        lpaString: completedOrder.stock?.lpaString,
                        accessPointName: completedOrder.stock?.accessPointName,
                        activationCode: completedOrder.stock?.activationCode,
                        phoneNumber: completedOrder.stock?.phoneNumber,
                        qrCodeUrl: completedOrder.stock?.qrCodeUrl
                    };
                }

                // Generate QR code if LPA string is available but QR code URL is not
                let qrCode = null;
                if (esimData.lpaString && !esimData.qrCodeUrl) {
                    try {
                        qrCode = await generateQRCode(esimData.lpaString);
                    } catch (error) {
                        console.error('Error generating QR code for email:', error);
                    }
                } else if (esimData.qrCodeUrl) {
                    qrCode = esimData.qrCodeUrl;
                }

                // Prepare order details for email
                const orderDetails = {
                    order: {
                        id: completedOrder.id,
                        status: completedOrder.status
                    },
                    plan: {
                        name: completedOrder.plan.name,
                        productId: completedOrder.plan.productId
                    },
                    orderTotal: parseFloat(completedOrder.orderTotal),
                    quantity: completedOrder.quantity,
                    startDate: completedOrder.startDate,
                    expiryDate: completedOrder.expiryDate,
                    iccid: esimData.iccid,
                    smdpAddress: esimData.smdpAddress,
                    lpaString: esimData.lpaString,
                    accessPointName: esimData.accessPointName,
                    activationCode: esimData.activationCode,
                    phoneNumber: esimData.phoneNumber,
                    qrCode: qrCode,
                    partner: {
                        id: partner.id,
                        firstName: partner.firstName,
                        lastName: partner.lastName,
                        email: partner.email
                    }
                };

                // Send partner notification email
                try {
                    await emailService.sendApiPartnerOrderEmail(partner.email, orderDetails);
                    console.log('API order email sent to partner:', partner.email);
                } catch (error) {
                    console.error('Failed to send API partner order email:', error);
                    // Don't fail the order creation due to email error
                }

                // Send admin notification emails
                try {
                    const admins = await User.findAll({
                        where: {
                            role: 'admin',
                            isActive: true
                        },
                        attributes: ['email']
                    });

                    if (admins && admins.length > 0) {
                        const adminEmails = admins.map(admin => admin.email);
                        await emailService.sendApiAdminOrderEmail(adminEmails, orderDetails);
                        console.log('API order admin notifications sent to:', adminEmails);
                    }
                } catch (error) {
                    console.error('Failed to send API admin order notifications:', error);
                    // Don't fail the order creation due to email error
                }

            } catch (error) {
                console.error('Error sending API order email notifications:', error);
                // Don't fail the order creation due to email error
            }

            // Return simplified response - detailed information available via Order Details API
            return res.status(201).json({
                success: true,
                orderId: order.id,
                message: 'Order created successfully'
            });
            
        } catch (error) {
            console.error('Error creating order:', error);
            if (order && order.status === 'pending') {
                // Update order to failed status if it exists but failed during processing
                await order.update({
                    status: 'failed',
                    providerErrorMessage: error.message
                }, { transaction: false }); // Outside transaction since it may have been rolled back
            }
            
            throw error; // Re-throw to be caught by outer try-catch
        }
        
    } catch (error) {
        console.error('Error in order creation:', error);
        await t.rollback();
        
        // Check for specific error types and return appropriate status codes
        if (error.message && error.message.includes('No stock available')) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'STOCK_UNAVAILABLE',
                    message: 'This product is currently out of stock. Please try again later or contact support.'
                }
            });
        }
        
        return res.status(500).json({
            success: false,
            error: {
                code: 'ORDER_CREATION_FAILED',
                message: error.message || 'Failed to create order'
            }
        });
    }
};

/**
 * Get order details
 * @route GET /api/v1/order/:orderId
 */
exports.getOrderDetails = async (req, res) => {
    try {
        // Get partner from request (added by authenticateApiKey middleware)
        const partner = req.partner;
        const userId = partner.id;

        // Get orderId from URL parameters and decode it
        let { orderId } = req.params;

        if (!orderId) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_REQUEST',
                    message: 'Order ID is required'
                }
            });
        }

        // Decode URL-encoded characters and trim whitespace
        orderId = decodeURIComponent(orderId).trim();

        // Validate order ID format (should not contain control characters)
        if (!/^[A-Za-z0-9_-]+$/.test(orderId)) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_ORDER_ID',
                    message: 'Order ID contains invalid characters'
                }
            });
        }

        // Find the order and ensure it belongs to the partner
        const order = await Order.findOne({
            where: {
                id: orderId,
                userId // Ensure the order belongs to the partner
            },
            include: [{
                model: EsimPlan,
                as: 'plan',
                include: [{
                    model: Country,
                    as: 'countries',
                    through: { attributes: [] }
                }, {
                    model: Provider,
                    as: 'provider'
                }]
            }, {
                model: EsimStock,
                as: 'stock'
            }]
        });

        if (!order) {
            // Check if the order exists but doesn't belong to this partner
            const anyOrder = await Order.findByPk(orderId);
            if (anyOrder) {
                // Order exists but doesn't belong to this partner
                return res.status(403).json({
                    success: false,
                    error: {
                        code: 'ACCESS_DENIED',
                        message: 'You do not have access to this order'
                    }
                });
            }

            // Order doesn't exist at all
            return res.status(404).json({
                success: false,
                error: {
                    code: 'ORDER_NOT_FOUND',
                    message: 'Order not found'
                }
            });
        }

        // Determine if this is an external order from a provider like Mobimatter
        const isExternalOrder = order.plan?.provider?.type === 'API';

        // For external orders, extract eSIM data from the provider response
        let esimData = {};

        if (isExternalOrder) {
            // For Mobimatter orders, the data is in orderLineItem.lineItemDetails
            if (order.providerResponse?.orderLineItem?.lineItemDetails) {
                const lineItems = order.providerResponse.orderLineItem.lineItemDetails;

                // Extract data from line items
                esimData = {
                    iccid: lineItems.find(item => item.name === 'ICCID')?.value,
                    smdpAddress: lineItems.find(item => item.name === 'SMDP_ADDRESS')?.value,
                    lpaString: lineItems.find(item => item.name === 'LOCAL_PROFILE_ASSISTANT')?.value,
                    accessPointName: lineItems.find(item => item.name === 'ACCESS_POINT_NAME')?.value,
                    activationCode: lineItems.find(item => item.name === 'ACTIVATION_CODE')?.value,
                    phoneNumber: lineItems.find(item => item.name === 'PHONE_NUMBER')?.value,
                    qrCodeUrl: lineItems.find(item => item.name === 'QR_CODE')?.value || null
                };
            } else {
                // If it's not in the expected format, use providerMetadata
                esimData = order.providerMetadata || {};
            }
        } else {
            // For local stock orders, use the stock data
            esimData = {
                iccid: order.stock?.iccid,
                smdpAddress: order.stock?.smdpAddress,
                lpaString: order.stock?.lpaString,
                accessPointName: order.stock?.accessPointName,
                activationCode: order.stock?.activationCode,
                phoneNumber: order.stock?.phoneNumber,
                qrCodeUrl: order.stock?.qrCodeUrl
            };
        }

        // Format the response
        return res.json({
            success: true,
            data: {
                orderId: order.id,
                status: order.status,
                product: {
                    productId: order.plan.productId,
                    name: order.plan.name
                },
                orderTotal: parseFloat(order.orderTotal),
                quantity: order.quantity,
                startDate: order.startDate,
                expiryDate: order.expiryDate,
                iccid: esimData.iccid || order.providerMetadata?.iccid || null,
                smdpAddress: esimData.smdpAddress || order.providerMetadata?.smdpAddress || null,
                accessPointName: esimData.accessPointName || order.providerMetadata?.accessPointName || null,
                lpaString: esimData.lpaString || order.providerMetadata?.lpaString || null,
                activationCode: esimData.activationCode || order.providerMetadata?.activationCode || null,
                qrCodeUrl: esimData.qrCodeUrl || order.providerMetadata?.qrCodeUrl || null,
                walletAuthTransactionId: order.walletAuthTransactionId,
                createdAt: order.createdAt
            }
        });
    } catch (error) {
        console.error('Error fetching order details:', error);
        return res.status(500).json({
            success: false,
            error: {
                code: 'SERVER_ERROR',
                message: 'Error retrieving order details'
            }
        });
    }
};

/**
 * Get usage information for an eSIM
 * @route GET /api/v1/usage/:orderId
 */
exports.getUsageInfo = async (req, res) => {
    try {
        // Get partner from request (added by authenticateApiKey middleware)
        const partner = req.partner;
        const userId = partner.id;
        
        // Get orderId from URL parameters and decode it
        let { orderId } = req.params;

        if (!orderId) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_REQUEST',
                    message: 'Order ID is required'
                }
            });
        }

        // Decode URL-encoded characters and trim whitespace
        orderId = decodeURIComponent(orderId).trim();

        // Validate order ID format (should not contain control characters)
        if (!/^[A-Za-z0-9_-]+$/.test(orderId)) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_ORDER_ID',
                    message: 'Order ID contains invalid characters'
                }
            });
        }
        
        // Find the order with plan and provider details
        const order = await Order.findOne({
            where: { 
                id: orderId,
                userId // Ensure the order belongs to the partner
            },
            include: [{
                model: EsimPlan,
                as: 'plan',
                include: [
                    {
                        model: sequelize.models.Provider, // Use the correct Provider model
                        as: 'provider'
                    }
                ]
            }]
        });
        
        if (!order) {
            // Check if the order exists but doesn't belong to this partner
            const anyOrder = await Order.findByPk(orderId);
            if (anyOrder) {
                // Order exists but doesn't belong to this partner
                return res.status(403).json({
                    success: false,
                    error: {
                        code: 'ACCESS_DENIED',
                        message: 'You do not have access to this order'
                    }
                });
            }
            
            // Order doesn't exist at all
            return res.status(404).json({
                success: false,
                error: {
                    code: 'ORDER_NOT_FOUND',
                    message: 'Order not found'
                }
            });
        }
        
        // Check if we already have cached usage data that's less than 1 hour old
        const cachedData = order.usageData;
        const lastUsageCheck = order.lastUsageCheck;
        const now = new Date();
        
        if (cachedData && lastUsageCheck && (now - new Date(lastUsageCheck)) < 60 * 60 * 1000) {
            return res.json({
                success: true,
                data: {
                    orderId: order.id,
                    dataUsage: order.dataUsage,
                    dataAllowance: order.dataAllowance,
                    status: order.usageStatus || 'Unknown',
                    expiryDate: order.expiryDate,
                    lastUpdated: order.lastUsageCheck,
                    message: order.usageMessage || null,
                    isRealtime: (order.plan.provider?.type === 'API'),
                    fromCache: true
                }
            });
        }
        
        // Check if this is from a provider with usage capabilities (e.g., Mobimatter)
        if (!order.plan.provider || order.plan.provider.type !== 'API') {
            return res.json({
                success: true,
                data: {
                    orderId: order.id,
                    dataUsage: null,
                    dataAllowance: null,
                    status: 'Not Available',
                    expiryDate: order.expiryDate,
                    lastUpdated: null,
                    message: 'Usage data is not available for plans from this provider',
                    isRealtime: false,
                    fromCache: false
                }
            });
        }
        
        // Get the external order ID
        const externalOrderId = order.externalOrderId || order.providerResponse?.id;
        if (!externalOrderId) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_ORDER',
                    message: 'External order ID not found'
                }
            });
        }

        // Check if the order is completed - usage data is only available for completed orders
        if (order.status === 'pending') {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'ORDER_PENDING',
                    message: 'Usage data is not available for pending orders. Please wait for the order to be completed.'
                }
            });
        }
        
        // Get usage data from provider
        const providerName = order.plan.provider.name;
        const providerService = providerFactory.getProvider(providerName);
        if (!providerService || typeof providerService.getUsageInfo !== 'function') {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'PROVIDER_NOT_SUPPORTED',
                    message: 'Usage data is not available for this provider'
                }
            });
        }

        let usageData;

        if (providerName === 'Mobimatter') {
            usageData = await providerService.getUsageInfo(externalOrderId);
        } else if (providerName === 'Billionconnect') {
            // For BillionConnect, we need the ICCID as well
            const iccid = order.stock?.iccid ||
                         order.providerMetadata?.iccid ||
                         order.providerResponse?.tradeData?.iccid;

            if (!iccid) {
                return res.status(400).json({
                    success: false,
                    error: {
                        code: 'MISSING_ICCID',
                        message: 'ICCID not found for BillionConnect usage query'
                    }
                });
            }

            // Get channel order ID (our internal order ID)
            const channelOrderId = order.id;
            usageData = await providerService.getUsageInfo(externalOrderId, channelOrderId, iccid);
        } else {
            usageData = await providerService.getUsageInfo(externalOrderId);
        }
        
        // Update the order with the new usage data
        await Order.update({
            usageData: usageData,
            dataUsage: usageData.dataUsage,
            dataAllowance: usageData.dataAllowance,
            usageStatus: usageData.status || 'Unknown',
            usageMessage: usageData.message || usageData.usageMessage || null,
            lastUsageCheck: now
        }, {
            where: { id: orderId }
        });
        
        return res.json({
            success: true,
            data: {
                orderId: order.id,
                dataUsage: usageData.dataUsage,
                dataAllowance: usageData.dataAllowance,
                status: usageData.status || 'Unknown',
                expiryDate: order.expiryDate || usageData.expiryDate,
                lastUpdated: now,
                message: usageData.message || null,
                isRealtime: true,
                fromCache: false
            }
        });
    } catch (error) {
        console.error('Error fetching usage info:', error);
        return res.status(500).json({
            success: false,
            error: {
                code: 'SERVER_ERROR',
                message: 'Error retrieving usage information'
            }
        });
    }
};

/**
 * Get topup plans for a specific order
 * @route GET /api/v1/order/:orderId/topup-plans
 */
exports.getTopupPlans = async (req, res) => {
    try {
        // Get partner from request (added by authenticateApiKey middleware)
        const partner = req.partner;
        const userId = partner.id;

        // Get orderId from URL parameters and decode it
        let { orderId } = req.params;

        if (!orderId) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_REQUEST',
                    message: 'Order ID is required'
                }
            });
        }

        // Decode URL-encoded characters and trim whitespace
        orderId = decodeURIComponent(orderId).trim();

        // Validate order ID format (should not contain control characters)
        if (!/^[A-Za-z0-9_-]+$/.test(orderId)) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_ORDER_ID',
                    message: 'Order ID contains invalid characters'
                }
            });
        }

        // First, get the original order to verify ownership and get network info
        const originalOrder = await Order.findOne({
            where: {
                id: orderId,
                userId: userId
            },
            include: [
                {
                    model: EsimPlan,
                    as: 'plan',
                    include: [{
                        model: Provider,
                        as: 'provider'
                    }]
                },
                {
                    model: EsimStock,
                    as: 'stock',
                    required: false // Left join - stock might not exist for pending orders
                }
            ]
        });

        if (!originalOrder) {
            // Check if the order exists but doesn't belong to this partner
            const anyOrder = await Order.findByPk(orderId);
            if (anyOrder) {
                // Order exists but doesn't belong to this partner
                return res.status(403).json({
                    success: false,
                    error: {
                        code: 'ACCESS_DENIED',
                        message: 'You do not have access to this order'
                    }
                });
            }

            // Order doesn't exist at all
            return res.status(404).json({
                success: false,
                error: {
                    code: 'ORDER_NOT_FOUND',
                    message: 'Order not found'
                }
            });
        }

        // Check if the order is completed
        if (originalOrder.status !== 'completed') {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'ORDER_NOT_COMPLETED',
                    message: 'Topup is only available for completed orders'
                }
            });
        }

        // Check if the plan supports topup
        if (originalOrder.plan.top_up !== 'Available') {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'TOPUP_NOT_SUPPORTED',
                    message: 'This plan does not support topup'
                }
            });
        }

        // Check if the order is from a supported provider
        const providerName = originalOrder.plan.provider.name;
        if (providerName !== 'Mobimatter' && providerName !== 'Billionconnect') {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'PROVIDER_NOT_SUPPORTED',
                    message: 'Topup is only available for Mobimatter and BillionConnect orders'
                }
            });
        }

        let topupPlans = [];

        if (providerName === 'Mobimatter') {
            // Get the network from the original plan
            const originalNetwork = originalOrder.plan.networkName;

            if (!originalNetwork) {
                return res.status(400).json({
                    success: false,
                    error: {
                        code: 'NETWORK_NOT_FOUND',
                        message: 'Cannot determine network for topup plans'
                    }
                });
            }

            // Get all Mobimatter addon plans with the same network
            const mobimatterPlans = await EsimPlan.findAll({
                where: {
                    status: 'visible',
                    isActive: true,
                    category: 'esim_addon',
                    networkName: originalNetwork
                },
                include: [
                    {
                        model: Provider,
                        as: 'provider',
                        where: { name: 'Mobimatter' }
                    },
                    {
                        model: Country,
                        as: 'countries',
                        through: { attributes: [] }
                    }
                ],
                order: [['createdAt', 'DESC']]
            });

            // Apply markup-based pricing for all plans
            topupPlans = mobimatterPlans.map(plan => {
                const planData = plan.toJSON ? plan.toJSON() : plan;

                // Calculate display price based on markup
                if (!planData.sellingPrice) {
                    const markup = partner.markupPercentage || 0;
                    const calculatedPrice = parseFloat(planData.buyingPrice) * (1 + markup / 100);
                    planData.displayPrice = Math.round(calculatedPrice * 100) / 100;
                } else {
                    planData.displayPrice = parseFloat(planData.sellingPrice);
                }

                // Determine countries based on provider type (same logic as products endpoint)
                let countries = [];
                if (planData.provider && planData.provider.name === 'Mobimatter') {
                    // For Mobimatter plans, use the supported countries from provider metadata
                    countries = planData.providerMetadata?.originalData?.supportedCountries || [];
                } else {
                    // For local plans, use the countries array from database associations
                    countries = planData.countries ? planData.countries.map(c => c.id) : [];
                }

                return {
                    productId: planData.productId,
                    name: planData.name,
                    description: planData.description,
                    planInfo: planData.planInfo,
                    price: planData.displayPrice,
                    validityDays: planData.validityDays,
                    dataAmount: planData.planData,
                    dataUnit: planData.planDataUnit,
                    customPlanData: planData.customPlanData,
                    voiceMin: planData.voiceMin,
                    voiceMinUnit: planData.voiceMinUnit,
                    sms: planData.sms || null,
                    speed: planData.speed,
                    planType: planData.planType,
                    category: planData.category,
                    networkType: planData.networkType,
                    countries: countries,
                    region: typeof planData.region === 'string'
                        ? planData.region.split(',').map(r => r.trim())
                        : planData.region,
                    features: planData.features || [],
                    isVoiceAvailable: planData.is_voice === 'Available',
                    isSmsAvailable: planData.is_sms === 'Available',
                    hotspotAvailable: planData.hotspot === 'Available',
                    profile: planData.profile || 'roaming',
                };
            });

        } else if (providerName === 'Billionconnect') {
            // Get ICCID from the original order
            const iccid = originalOrder.stock?.iccid ||
                         originalOrder.providerMetadata?.iccid ||
                         originalOrder.providerResponse?.tradeData?.iccid;

            if (!iccid) {
                return res.status(400).json({
                    success: false,
                    error: {
                        code: 'ICCID_NOT_FOUND',
                        message: 'Cannot find ICCID for BillionConnect topup. Order may not be completed yet.'
                    }
                });
            }

            try {
                // Get BillionConnect service
                const billionconnectService = require('../services/billionconnect.service');

                // Query available recharge SKUs
                const availableSkuIds = await billionconnectService.queryRechargeCommodities(iccid);

                if (!availableSkuIds || availableSkuIds.length === 0) {
                    return res.json({
                        success: true,
                        data: {
                            originalOrder: {
                                orderId: originalOrder.id,
                                productId: originalOrder.plan.productId,
                                name: originalOrder.plan.name,
                                iccid: iccid,
                                provider: providerName
                            },
                            topupPlans: []
                        }
                    });
                }

                // Get BillionConnect plans that match the available SKUs
                const billionconnectPlans = await EsimPlan.findAll({
                    where: {
                        status: 'visible',
                        isActive: true,
                        externalSkuId: {
                            [Op.in]: availableSkuIds
                        }
                    },
                    include: [
                        {
                            model: Provider,
                            as: 'provider',
                            where: { name: 'Billionconnect' }
                        },
                        {
                            model: Country,
                            as: 'countries',
                            through: { attributes: [] }
                        }
                    ],
                    order: [['createdAt', 'DESC']]
                });

                // Apply markup-based pricing for BillionConnect plans
                topupPlans = billionconnectPlans.map(plan => {
                    const planData = plan.toJSON ? plan.toJSON() : plan;

                    // Calculate display price based on markup
                    if (!planData.sellingPrice) {
                        const markup = partner.markupPercentage || 0;
                        const calculatedPrice = parseFloat(planData.buyingPrice) * (1 + markup / 100);
                        planData.displayPrice = Math.round(calculatedPrice * 100) / 100;
                    } else {
                        planData.displayPrice = parseFloat(planData.sellingPrice);
                    }

                    // Determine countries based on provider type (same logic as products endpoint)
                    let countries = [];
                    if (planData.provider && planData.provider.name === 'Billionconnect') {
                        // For BillionConnect plans, use the supported countries from provider metadata
                        countries = planData.providerMetadata?.originalData?.supportedCountries || [];
                    } else {
                        // For local plans, use the countries array from database associations
                        countries = planData.countries ? planData.countries.map(c => c.id) : [];
                    }

                    return {
                        productId: planData.productId,
                        name: planData.name,
                        description: planData.description,
                        planInfo: planData.planInfo,
                        price: planData.displayPrice,
                        validityDays: planData.validityDays,
                        dataAmount: planData.planData,
                        dataUnit: planData.planDataUnit,
                        customPlanData: planData.customPlanData,
                        voiceMin: planData.voiceMin,
                        voiceMinUnit: planData.voiceMinUnit,
                        sms: planData.sms || null,
                        speed: planData.speed,
                        planType: planData.planType,
                        category: planData.category,
                        networkType: planData.networkType,
                        countries: countries,
                        region: typeof planData.region === 'string'
                            ? planData.region.split(',').map(r => r.trim())
                            : planData.region,
                        features: planData.features || [],
                        isVoiceAvailable: planData.is_voice === 'Available',
                        isSmsAvailable: planData.is_sms === 'Available',
                        hotspotAvailable: planData.hotspot === 'Available',
                        profile: planData.profile || 'roaming',
                    };
                });

            } catch (error) {
                console.error('Error querying BillionConnect recharge commodities:', error);
                return res.status(500).json({
                    success: false,
                    error: {
                        code: 'PROVIDER_ERROR',
                        message: 'Failed to fetch available topup plans from BillionConnect'
                    }
                });
            }
        }

        // Get ICCID from the original order
        let orderIccid = null;
        if (providerName === 'Mobimatter') {
            // For Mobimatter, get ICCID from provider metadata or stock
            orderIccid = originalOrder.providerMetadata?.iccid ||
                        originalOrder.stock?.iccid ||
                        (originalOrder.providerResponse?.orderLineItem?.lineItemDetails?.find(item => item.name === 'ICCID')?.value);
        } else if (providerName === 'Billionconnect') {
            // For BillionConnect, get ICCID from stock, provider metadata, or provider response
            orderIccid = originalOrder.stock?.iccid ||
                        originalOrder.providerMetadata?.iccid ||
                        originalOrder.providerResponse?.tradeData?.iccid;
        }

        res.json({
            success: true,
            data: {
                originalOrder: {
                    orderId: originalOrder.id,
                    productId: originalOrder.plan.productId,
                    name: originalOrder.plan.name,
                    iccid: orderIccid
                },
                topupPlans: topupPlans
            }
        });

    } catch (error) {
        console.error('Error fetching topup plans:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_SERVER_ERROR',
                message: 'Error retrieving topup plans'
            }
        });
    }
};

/**
 * Create a topup order
 * @route POST /api/v1/order/:orderId/topup
 */
exports.createTopupOrder = async (req, res) => {
    const t = await sequelize.transaction();
    try {
        // Get partner from request (added by authenticateApiKey middleware)
        const partner = req.partner;
        const userId = partner.id;

        // Get orderId from URL parameters and productId from request body
        let { orderId } = req.params;
        const { productId } = req.body;

        if (!orderId) {
            await t.rollback();
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_REQUEST',
                    message: 'Order ID is required'
                }
            });
        }

        // Decode URL-encoded characters and trim whitespace
        orderId = decodeURIComponent(orderId).trim();

        // Validate order ID format (should not contain control characters)
        if (!/^[A-Za-z0-9_-]+$/.test(orderId)) {
            await t.rollback();
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_ORDER_ID',
                    message: 'Order ID contains invalid characters'
                }
            });
        }

        if (!productId) {
            await t.rollback();
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_REQUEST',
                    message: 'Product ID is required'
                }
            });
        }

        // Validate productId format (should be a string of alphanumeric characters)
        if (typeof productId !== 'string' || !/^[A-Z0-9]{6,12}$/.test(productId)) {
            await t.rollback();
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_PRODUCT_ID',
                    message: 'Invalid product ID format'
                }
            });
        }

        // Get the original order to verify ownership and get details
        const originalOrder = await Order.findOne({
            where: {
                id: orderId,
                userId: userId
            },
            include: [
                {
                    model: EsimPlan,
                    as: 'plan',
                    include: [{
                        model: Provider,
                        as: 'provider'
                    }]
                },
                {
                    model: EsimStock,
                    as: 'stock',
                    required: false
                }
            ],
            transaction: t
        });

        if (!originalOrder) {
            await t.rollback();
            // Check if the order exists but doesn't belong to this partner
            const anyOrder = await Order.findByPk(orderId);
            if (anyOrder) {
                return res.status(403).json({
                    success: false,
                    error: {
                        code: 'ACCESS_DENIED',
                        message: 'You do not have access to this order'
                    }
                });
            }

            return res.status(404).json({
                success: false,
                error: {
                    code: 'ORDER_NOT_FOUND',
                    message: 'Order not found'
                }
            });
        }

        // Check if the order is completed
        if (originalOrder.status !== 'completed') {
            await t.rollback();
            return res.status(400).json({
                success: false,
                error: {
                    code: 'ORDER_NOT_COMPLETED',
                    message: 'Topup is only available for completed orders'
                }
            });
        }

        // Check if the plan supports topup
        if (originalOrder.plan.top_up !== 'Available') {
            await t.rollback();
            return res.status(400).json({
                success: false,
                error: {
                    code: 'TOPUP_NOT_SUPPORTED',
                    message: 'This plan does not support topup'
                }
            });
        }

        // Get topup plan details
        const topupPlan = await EsimPlan.findOne({
            where: {
                productId,
                status: 'visible',
                isActive: true
            },
            include: [{
                model: Provider,
                as: 'provider',
                attributes: ['id', 'name', 'type', 'country']
            }],
            transaction: t
        });

        if (!topupPlan) {
            await t.rollback();
            return res.status(404).json({
                success: false,
                error: {
                    code: 'PRODUCT_NOT_FOUND',
                    message: 'Topup product not found or not available'
                }
            });
        }

        // Verify that the topup plan is compatible with the original order
        const providerName = originalOrder.plan.provider.name;
        if (topupPlan.provider.name !== providerName) {
            await t.rollback();
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INCOMPATIBLE_PROVIDER',
                    message: 'Topup plan must be from the same provider as the original order'
                }
            });
        }

        // For Mobimatter, verify network compatibility
        if (providerName === 'Mobimatter') {
            if (topupPlan.networkName !== originalOrder.plan.networkName) {
                await t.rollback();
                return res.status(400).json({
                    success: false,
                    error: {
                        code: 'INCOMPATIBLE_NETWORK',
                        message: 'Topup plan must be from the same network as the original order'
                    }
                });
            }

            // Verify it's an addon plan
            if (topupPlan.category !== 'esim_addon') {
                await t.rollback();
                return res.status(400).json({
                    success: false,
                    error: {
                        code: 'INVALID_TOPUP_PLAN',
                        message: 'Only addon plans can be used for topup'
                    }
                });
            }
        }

        // Calculate the price based on the partner's markup if sellingPrice is null
        let orderTotal = topupPlan.sellingPrice;
        if (orderTotal === null && partner.markupPercentage) {
            const markup = parseFloat(partner.markupPercentage) / 100;
            orderTotal = parseFloat(topupPlan.buyingPrice) * (1 + markup);
            // Round to 2 decimal places
            orderTotal = Math.round(orderTotal * 100) / 100;
        }

        // Get partner's wallet
        const wallet = await Wallet.findOne({
            where: { userId },
            transaction: t,
            lock: true
        });

        if (!wallet) {
            await t.rollback();
            return res.status(400).json({
                success: false,
                error: {
                    code: 'WALLET_NOT_FOUND',
                    message: 'Partner wallet not found'
                }
            });
        }

        // Check wallet balance
        if (parseFloat(wallet.balance) < orderTotal) {
            await t.rollback();
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INSUFFICIENT_BALANCE',
                    message: 'Insufficient wallet balance',
                    required: orderTotal,
                    available: parseFloat(wallet.balance)
                }
            });
        }

        let topupOrder = null;
        const walletAuthTransactionId = uuidv4();

        try {
            // Create topup order record with pending status
            topupOrder = await Order.create({
                userId,
                esimPlanId: topupPlan.id,
                esimStockId: null,
                quantity: 1,
                orderTotal,
                startDate: null,
                status: 'pending',
                walletAuthTransactionId,
                parentOrderId: originalOrder.id // Link to original order
            }, { transaction: t });

            // Get provider service instance
            const providerService = providerFactory.getProvider(providerName);

            if (providerName === 'Mobimatter') {
                // For Mobimatter topup orders
                const orderPayload = {
                    productId: topupPlan.externalProductId,
                    skuId: topupPlan.externalSkuId,
                    quantity: 1,
                    customerReference: `api_${userId}_${Date.now()}`,
                    parentOrderId: originalOrder.externalOrderId
                };

                // Create external topup order
                const externalOrder = await providerService.createOrder(orderPayload);

                if (!externalOrder || !externalOrder.orderId) {
                    throw new Error('Invalid response from provider: Missing order ID');
                }

                // Complete the order
                const completedOrder = providerService.completeOrder ?
                    await providerService.completeOrder(externalOrder.orderId) :
                    externalOrder;

                // Update topup order with completed status
                await topupOrder.update({
                    status: 'completed',
                    externalOrderId: externalOrder.orderId,
                    providerResponse: completedOrder,
                    providerMetadata: {
                        parentOrderId: originalOrder.id,
                        parentExternalOrderId: originalOrder.externalOrderId,
                        providerStatus: completedOrder.status || 'completed'
                    },
                    providerOrderStatus: completedOrder.orderState || 'ACTIVE',
                    lastProviderCheck: new Date()
                }, { transaction: t });

            } else if (providerName === 'Billionconnect') {
                // For BillionConnect topup orders, use F007 API directly
                const iccid = originalOrder.stock?.iccid ||
                             originalOrder.providerMetadata?.iccid ||
                             originalOrder.providerResponse?.tradeData?.iccid;

                if (!iccid) {
                    throw new Error('ICCID not found for BillionConnect topup');
                }

                // Get BillionConnect service
                const billionconnectService = require('../services/billionconnect.service');

                // Use F007 API for recharge order with unique channel order ID
                const rechargeChannelOrderId = `api_${userId}_${Date.now()}_topup`;

                // Determine the correct number of copies for recharge order
                let rechargeCopies = 1;
                let rechargeSkuId = topupPlan.externalSkuId;

                // Create recharge order
                const rechargeOrder = await billionconnectService.createRechargeOrder({
                    channelOrderId: rechargeChannelOrderId,
                    iccidArray: [iccid],
                    skuId: rechargeSkuId,
                    copies: rechargeCopies,
                    totalAmount: orderTotal.toString(),
                    comment: `API Topup for order ${originalOrder.id}`
                });

                // Update topup order with completed status
                await topupOrder.update({
                    externalOrderId: rechargeOrder.orderId,
                    providerResponse: rechargeOrder.providerResponse,
                    providerMetadata: {
                        iccid: iccid,
                        rechargeOrder: true,
                        parentOrderId: originalOrder.id
                    },
                    status: 'completed',
                    providerOrderStatus: 'ACTIVE',
                    lastProviderCheck: new Date()
                }, { transaction: t });
            }

            // Process wallet transaction
            const newBalance = parseFloat(wallet.balance) - orderTotal;

            // Create wallet transaction record
            await WalletTransaction.create({
                id: uuidv4(),
                walletId: wallet.id,
                type: 'debit',
                amount: orderTotal,
                balance: newBalance,
                description: `Payment for API topup order: ${topupOrder.id}`,
                status: 'completed',
                referenceType: 'order',
                referenceId: topupOrder.id,
                metadata: {
                    orderId: topupOrder.id,
                    parentOrderId: originalOrder.id,
                    totalAmount: orderTotal,
                    api: true,
                    topup: true,
                    provider: providerName
                }
            }, { transaction: t });

            // Update wallet balance
            await wallet.update({
                balance: newBalance,
                updatedAt: new Date()
            }, { transaction: t });

            await t.commit();

            // Send email notifications for API topup orders
            try {
                // Get the completed topup order with all relations for email
                const completedTopupOrder = await Order.findByPk(topupOrder.id, {
                    include: [{
                        model: EsimPlan,
                        as: 'plan',
                        include: [{
                            model: Country,
                            as: 'countries',
                            through: { attributes: [] }
                        }, {
                            model: Provider,
                            as: 'provider'
                        }]
                    }]
                });

                // Prepare order details for email
                const orderDetails = {
                    order: {
                        id: completedTopupOrder.id,
                        status: completedTopupOrder.status,
                        parentOrderId: originalOrder.id
                    },
                    plan: {
                        name: completedTopupOrder.plan.name,
                        productId: completedTopupOrder.plan.productId
                    },
                    orderTotal: parseFloat(completedTopupOrder.orderTotal),
                    quantity: completedTopupOrder.quantity,
                    startDate: completedTopupOrder.startDate,
                    expiryDate: completedTopupOrder.expiryDate,
                    isTopUp: true,
                    partner: {
                        id: partner.id,
                        firstName: partner.firstName,
                        lastName: partner.lastName,
                        email: partner.email
                    }
                };

                // Send partner notification email
                try {
                    await emailService.sendApiPartnerOrderEmail(partner.email, orderDetails);
                    console.log('API topup order email sent to partner:', partner.email);
                } catch (error) {
                    console.error('Failed to send API partner topup order email:', error);
                    // Don't fail the order creation due to email error
                }

                // Send admin notification emails
                try {
                    const admins = await User.findAll({
                        where: {
                            role: 'admin',
                            isActive: true
                        },
                        attributes: ['email']
                    });

                    if (admins && admins.length > 0) {
                        const adminEmails = admins.map(admin => admin.email);
                        await emailService.sendApiAdminOrderEmail(adminEmails, orderDetails);
                        console.log('API topup order admin notifications sent to:', adminEmails);
                    }
                } catch (error) {
                    console.error('Failed to send API admin topup order notifications:', error);
                    // Don't fail the order creation due to email error
                }

            } catch (error) {
                console.error('Error sending API topup order email notifications:', error);
                // Don't fail the order creation due to email error
            }

            // Return success response
            return res.status(201).json({
                success: true,
                data: {
                    topupOrderId: topupOrder.id,
                    originalOrderId: originalOrder.id,
                    status: 'completed',
                    message: 'Topup order created successfully'
                }
            });

        } catch (error) {
            console.error('Error creating topup order:', error);
            if (topupOrder && topupOrder.status === 'pending') {
                // Update order to failed status if it exists but failed during processing
                await topupOrder.update({
                    status: 'failed',
                    providerErrorMessage: error.message
                }, { transaction: false }); // Outside transaction since it may have been rolled back
            }

            throw error; // Re-throw to be caught by outer try-catch
        }

    } catch (error) {
        console.error('Error in topup order creation:', error);
        await t.rollback();

        return res.status(500).json({
            success: false,
            error: {
                code: 'TOPUP_ORDER_CREATION_FAILED',
                message: error.message || 'Failed to create topup order'
            }
        });
    }
};

/**
 * Resend eSIM email for a BillionConnect order
 * @route POST /api/v1/resend-email
 */
exports.resendEsimEmail = async (req, res) => {
    try {
        // Get partner from request (added by authenticateApiKey middleware)
        const partner = req.partner;
        const userId = partner.id;

        // Get orderId and email from request body
        const { orderId, email } = req.body;

        if (!orderId) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_REQUEST',
                    message: 'Order ID is required'
                }
            });
        }

        // Email is optional, defaults to configured F041 email if not provided
        const emailToUse = email || process.env.F041_DEFAULT_EMAIL || '<EMAIL>';

        // Validate email format if provided
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (email && !emailRegex.test(email)) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_EMAIL',
                    message: 'Invalid email format'
                }
            });
        }

        // Find the order to verify it belongs to this partner and is a BillionConnect order
        const order = await models.Order.findOne({
            where: {
                id: orderId,
                userId: userId,
                status: 'completed' // Only allow resending for completed orders
            },
            include: [{
                model: models.EsimPlan,
                as: 'plan',
                include: [{
                    model: models.Provider,
                    as: 'provider',
                    attributes: ['name', 'type']
                }]
            }]
        });

        if (!order) {
            return res.status(404).json({
                success: false,
                error: {
                    code: 'ORDER_NOT_FOUND',
                    message: 'Order not found or not accessible'
                }
            });
        }

        // Verify this is a BillionConnect order
        if (!order.plan.provider || order.plan.provider.name !== 'Billionconnect') {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_PROVIDER',
                    message: 'Email resend is only available for BillionConnect orders'
                }
            });
        }

        // Get the external order ID (BillionConnect order ID)
        const externalOrderId = order.externalOrderId;
        if (!externalOrderId) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'MISSING_EXTERNAL_ORDER_ID',
                    message: 'External order ID not found for this order'
                }
            });
        }

        // Get the provider service
        const providerService = providerFactory.getProvider(order.plan.provider.name);
        if (!providerService || typeof providerService.resendEsimEmail !== 'function') {
            return res.status(500).json({
                success: false,
                error: {
                    code: 'PROVIDER_ERROR',
                    message: 'Provider does not support email resending'
                }
            });
        }

        // Call the provider's resend email method
        const result = await providerService.resendEsimEmail(externalOrderId, emailToUse);

        console.log('eSIM email resend successful:', {
            orderId: order.id,
            externalOrderId,
            email: emailToUse.replace(/^(.{2})(.*)(@.*)$/, '$1***$3'),
            partnerId: userId
        });

        return res.json({
            success: true,
            message: 'eSIM email resent successfully',
            data: {
                orderId: order.id,
                email: emailToUse,
                tradeCode: result.tradeCode,
                tradeMsg: result.tradeMsg
            }
        });

    } catch (error) {
        console.error('Error resending eSIM email:', error);
        return res.status(500).json({
            success: false,
            error: {
                code: 'SERVER_ERROR',
                message: 'Error resending eSIM email'
            }
        });
    }
};