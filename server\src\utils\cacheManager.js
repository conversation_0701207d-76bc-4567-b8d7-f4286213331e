const planCache = new Map();
const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes in milliseconds
const MAX_CACHE_SIZE = 500; // Reduced maximum number of cache entries to prevent memory issues
const MEMORY_PRESSURE_THRESHOLD = 0.7; // Clear cache when memory usage exceeds 70%

// Helper function to invalidate cache for a specific plan
// If planId is null, invalidate all cached plans
const invalidatePlanCache = (planId) => {
    if (planId === null) {
        // Clear the entire cache
        planCache.clear();
        return;
    }

    // Remove all cache entries that contain this plan ID
    for (const key of planCache.keys()) {
        if (key.includes(planId)) {
            planCache.delete(key);
        }
    }
};

// Helper function to clean up expired cache entries
const cleanupExpiredCache = () => {
    const now = Date.now();
    for (const [key, value] of planCache.entries()) {
        if (now - value.timestamp > CACHE_DURATION) {
            planCache.delete(key);
        }
    }
};

// Helper function to manage cache size
const manageCacheSize = () => {
    if (planCache.size > MAX_CACHE_SIZE) {
        console.warn(`🗑️ Cache size exceeded (${planCache.size}/${MAX_CACHE_SIZE}), removing old entries`);

        // Remove oldest entries (LRU-style) more aggressively
        const entries = Array.from(planCache.entries());
        entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

        // Remove 30% of entries when limit is exceeded (more aggressive)
        const toRemove = Math.max(
            planCache.size - MAX_CACHE_SIZE,
            Math.floor(entries.length * 0.3)
        );

        for (let i = 0; i < toRemove; i++) {
            planCache.delete(entries[i][0]);
        }

        console.log(`🗑️ Removed ${toRemove} old cache entries`);
    }
};

// Emergency cache clearing function
const emergencyCacheClear = () => {
    const originalSize = planCache.size;
    planCache.clear();
    console.log(`🚨 Emergency cache clear: removed ${originalSize} entries`);
};

// Helper function to get cached plan with timestamp validation
const getCachedPlan = (cacheKey) => {
    if (planCache.has(cacheKey)) {
        const { plan, timestamp } = planCache.get(cacheKey);
        const now = Date.now();

        // Return plan if cache is still valid
        if (now - timestamp < CACHE_DURATION) {
            // Update timestamp for LRU behavior
            planCache.set(cacheKey, { plan, timestamp: now });
            return plan;
        }

        // Remove expired cache entry
        planCache.delete(cacheKey);
    }
    return null;
};

// Helper function to set cache with timestamp
const setCachePlan = (cacheKey, plan, duration = CACHE_DURATION) => {
    try {
        // Check memory pressure before caching
        const memoryMonitor = require('./memoryMonitor');
        if (memoryMonitor.isMemoryCritical()) {
            console.warn('⚠️ Memory critical - skipping cache to prevent memory leak');
            return;
        }

        // Clean up expired entries periodically
        if (Math.random() < 0.2) { // Increased chance to trigger cleanup (20%)
            cleanupExpiredCache();
        }

        // Manage cache size more aggressively
        manageCacheSize();

        // Check if we're approaching memory limits
        if (planCache.size >= MAX_CACHE_SIZE * 0.8) {
            console.warn(`⚠️ Cache approaching size limit (${planCache.size}/${MAX_CACHE_SIZE}), clearing old entries`);
            cleanupExpiredCache();
        }

        planCache.set(cacheKey, {
            plan,
            timestamp: Date.now(),
            size: JSON.stringify(plan).length // Track approximate size
        });

        // Set timeout to automatically clear cache after duration
        setTimeout(() => {
            if (planCache.has(cacheKey)) {
                planCache.delete(cacheKey);
            }
        }, duration);

    } catch (error) {
        console.error('Error setting cache:', error);
        // Don't cache if there's an error to prevent memory issues
    }
};

// Cache warming function for common queries
const warmCache = async () => {
    try {
        // This would be called by a background job or on server startup
        // to pre-populate cache with common queries
        console.log('Cache warming completed');
    } catch (error) {
        console.error('Error warming cache:', error);
    }
};

// Get cache statistics
const getCacheStats = () => {
    const now = Date.now();
    let validEntries = 0;
    let expiredEntries = 0;

    for (const [key, value] of planCache.entries()) {
        if (now - value.timestamp > CACHE_DURATION) {
            expiredEntries++;
        } else {
            validEntries++;
        }
    }

    return {
        totalEntries: planCache.size,
        validEntries,
        expiredEntries,
        hitRate: planCache.hitRate || 0
    };
};

module.exports = {
    planCache,
    CACHE_DURATION,
    invalidatePlanCache,
    getCachedPlan,
    setCachePlan,
    cleanupExpiredCache,
    warmCache,
    getCacheStats,
    emergencyCacheClear
};