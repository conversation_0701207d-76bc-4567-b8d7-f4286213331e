const sequelize = require('../config/database');

// Define models
const User = require('./User');
const Country = require('./Country');
const EsimPlan = require('./EsimPlan');
const EsimPlanCountries = require('./EsimPlanCountries');
const EsimPlanStockHistory = require('./EsimPlanStockHistory');
const CentralWallet = require('./centralWallet');
const OTP = require('./OTP');
const EsimStock = require('./EsimStock');
const Wallet = require('./Wallet');
const WalletTransaction = require('./WalletTransaction');
const Provider = require('./Provider');
const Cart = require('./Cart');
const Order = require('./Order');
const KnowledgeBase = require('./KnowledgeBase');
const NotificationMessage = require('./NotificationMessage');

// Define the models object
const models = {
    User,
    Country,
    EsimPlan,
    EsimPlanCountries,
    EsimPlanStockHistory,
    CentralWallet,
    OTP,
    EsimStock,
    Wallet,
    WalletTransaction,
    Provider,
    Cart,
    Order,
    KnowledgeBase,
    NotificationMessage
};

// Initialize associations
Object.values(models).forEach(model => {
    if (model.associate) {
        model.associate(models);
    }
});

module.exports = {
    ...models,
    sequelize
};
