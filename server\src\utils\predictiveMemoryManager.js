/**
 * Predictive Memory Manager
 * Uses machine learning-like patterns to predict and prevent memory leaks
 * before they occur during intensive operations
 */

const { EventEmitter } = require('events');
const memoryCleanup = require('./memoryCleanup');
const searchMemoryManager = require('./searchMemoryManager');

class PredictiveMemoryManager extends EventEmitter {
    constructor() {
        super();
        this.memoryPatterns = new Map();
        this.operationHistory = [];
        this.maxHistorySize = 1000;
        this.predictionAccuracy = new Map();
        this.isLearning = true;
        this.riskThresholds = {
            low: 0.3,
            medium: 0.6,
            high: 0.8,
            critical: 0.9
        };
        
        // Start background pattern analysis
        this.startPatternAnalysis();
    }

    /**
     * Start background pattern analysis
     */
    startPatternAnalysis() {
        setInterval(() => {
            this.analyzeMemoryPatterns();
            this.updatePredictionModels();
            this.cleanupOldHistory();
        }, 60000); // Every minute
    }

    /**
     * Record operation for pattern learning
     */
    recordOperation(operation) {
        const record = {
            timestamp: Date.now(),
            operationType: operation.type || 'unknown',
            searchType: operation.searchType || 'none',
            searchTerm: operation.searchTerm || '',
            startMemory: operation.startMemory || 0,
            endMemory: operation.endMemory || 0,
            memoryIncrease: operation.memoryIncrease || 0,
            duration: operation.duration || 0,
            success: operation.success !== false,
            abortReason: operation.abortReason || null,
            cleanupActions: operation.cleanupActions || [],
            resultCount: operation.resultCount || 0
        };

        this.operationHistory.push(record);

        // Keep history size manageable
        if (this.operationHistory.length > this.maxHistorySize) {
            this.operationHistory = this.operationHistory.slice(-this.maxHistorySize);
        }

        // Update patterns
        this.updatePatterns(record);
        
        console.log(`📊 [PREDICTIVE] Recorded operation: ${record.operationType} (${record.memoryIncrease.toFixed(1)}MB)`);
    }

    /**
     * Update memory patterns based on operation
     */
    updatePatterns(record) {
        const patternKey = this.generatePatternKey(record);
        
        if (!this.memoryPatterns.has(patternKey)) {
            this.memoryPatterns.set(patternKey, {
                count: 0,
                totalMemoryIncrease: 0,
                averageMemoryIncrease: 0,
                maxMemoryIncrease: 0,
                successRate: 0,
                averageDuration: 0,
                riskScore: 0,
                lastUpdated: Date.now()
            });
        }

        const pattern = this.memoryPatterns.get(patternKey);
        pattern.count++;
        pattern.totalMemoryIncrease += record.memoryIncrease;
        pattern.averageMemoryIncrease = pattern.totalMemoryIncrease / pattern.count;
        pattern.maxMemoryIncrease = Math.max(pattern.maxMemoryIncrease, record.memoryIncrease);
        pattern.successRate = this.calculateSuccessRate(patternKey);
        pattern.averageDuration = this.calculateAverageDuration(patternKey);
        pattern.riskScore = this.calculateRiskScore(pattern);
        pattern.lastUpdated = Date.now();
    }

    /**
     * Generate pattern key for operation
     */
    generatePatternKey(record) {
        const searchTypeKey = record.searchType || 'none';
        const termLengthKey = record.searchTerm ? (record.searchTerm.length <= 3 ? 'short' : 'long') : 'none';
        const resultSizeKey = record.resultCount > 1000 ? 'large' : (record.resultCount > 100 ? 'medium' : 'small');
        
        return `${record.operationType}_${searchTypeKey}_${termLengthKey}_${resultSizeKey}`;
    }

    /**
     * Calculate success rate for pattern
     */
    calculateSuccessRate(patternKey) {
        const relevantOperations = this.operationHistory.filter(op => 
            this.generatePatternKey(op) === patternKey
        );
        
        if (relevantOperations.length === 0) return 1.0;
        
        const successfulOps = relevantOperations.filter(op => op.success).length;
        return successfulOps / relevantOperations.length;
    }

    /**
     * Calculate average duration for pattern
     */
    calculateAverageDuration(patternKey) {
        const relevantOperations = this.operationHistory.filter(op => 
            this.generatePatternKey(op) === patternKey
        );
        
        if (relevantOperations.length === 0) return 0;
        
        const totalDuration = relevantOperations.reduce((sum, op) => sum + op.duration, 0);
        return totalDuration / relevantOperations.length;
    }

    /**
     * Calculate risk score for pattern
     */
    calculateRiskScore(pattern) {
        let riskScore = 0;

        // Memory increase risk
        if (pattern.averageMemoryIncrease > 200) riskScore += 0.4;
        else if (pattern.averageMemoryIncrease > 100) riskScore += 0.3;
        else if (pattern.averageMemoryIncrease > 50) riskScore += 0.2;

        // Success rate risk
        if (pattern.successRate < 0.5) riskScore += 0.3;
        else if (pattern.successRate < 0.8) riskScore += 0.2;
        else if (pattern.successRate < 0.9) riskScore += 0.1;

        // Max memory increase risk
        if (pattern.maxMemoryIncrease > 500) riskScore += 0.2;
        else if (pattern.maxMemoryIncrease > 300) riskScore += 0.1;

        // Duration risk
        if (pattern.averageDuration > 30000) riskScore += 0.1; // > 30 seconds

        return Math.min(riskScore, 1.0);
    }

    /**
     * Predict memory usage for upcoming operation
     */
    predictMemoryUsage(operationInfo) {
        const patternKey = this.generatePatternKey(operationInfo);
        const pattern = this.memoryPatterns.get(patternKey);

        if (!pattern) {
            return {
                predictedMemoryIncrease: this.getConservativeEstimate(operationInfo),
                confidence: 0.3,
                riskLevel: 'medium',
                recommendations: ['Monitor closely', 'Enable aggressive cleanup']
            };
        }

        const prediction = {
            predictedMemoryIncrease: pattern.averageMemoryIncrease,
            maxPredictedIncrease: pattern.maxMemoryIncrease,
            confidence: Math.min(pattern.count / 10, 1.0),
            riskLevel: this.getRiskLevel(pattern.riskScore),
            riskScore: pattern.riskScore,
            successRate: pattern.successRate,
            estimatedDuration: pattern.averageDuration,
            recommendations: this.generateRecommendations(pattern)
        };

        console.log(`🔮 [PREDICTIVE] Prediction for ${patternKey}: ${prediction.predictedMemoryIncrease.toFixed(1)}MB (${prediction.riskLevel} risk)`);
        
        return prediction;
    }

    /**
     * Get conservative memory estimate for unknown patterns
     */
    getConservativeEstimate(operationInfo) {
        const baseEstimate = 25; // 25MB base
        
        let estimate = baseEstimate;
        
        if (operationInfo.searchType === 'broad') estimate *= 3;
        else if (operationInfo.searchType === 'specific') estimate *= 1.5;
        
        if (operationInfo.searchTerm && operationInfo.searchTerm.length <= 3) estimate *= 2;
        
        return estimate;
    }

    /**
     * Get risk level from risk score
     */
    getRiskLevel(riskScore) {
        if (riskScore >= this.riskThresholds.critical) return 'critical';
        if (riskScore >= this.riskThresholds.high) return 'high';
        if (riskScore >= this.riskThresholds.medium) return 'medium';
        return 'low';
    }

    /**
     * Generate recommendations based on pattern
     */
    generateRecommendations(pattern) {
        const recommendations = [];

        if (pattern.riskScore >= this.riskThresholds.high) {
            recommendations.push('Enable streaming cleanup');
            recommendations.push('Use aggressive garbage collection');
            recommendations.push('Consider request throttling');
        }

        if (pattern.averageMemoryIncrease > 150) {
            recommendations.push('Enable proactive memory monitoring');
            recommendations.push('Clear object pools frequently');
        }

        if (pattern.successRate < 0.8) {
            recommendations.push('Implement early abort conditions');
            recommendations.push('Reduce batch sizes');
        }

        if (pattern.averageDuration > 20000) {
            recommendations.push('Implement timeout protection');
            recommendations.push('Use pagination for large results');
        }

        if (recommendations.length === 0) {
            recommendations.push('Standard monitoring sufficient');
        }

        return recommendations;
    }

    /**
     * Should operation proceed based on prediction
     */
    shouldProceedWithOperation(operationInfo, currentMemory) {
        const prediction = this.predictMemoryUsage(operationInfo);
        const projectedMemory = currentMemory + prediction.predictedMemoryIncrease;
        
        // Memory thresholds (in MB)
        const warningThreshold = 1200;
        const criticalThreshold = 1500;

        if (projectedMemory > criticalThreshold) {
            return {
                proceed: false,
                reason: 'projected_memory_critical',
                projectedMemory,
                prediction
            };
        }

        if (prediction.riskLevel === 'critical') {
            return {
                proceed: false,
                reason: 'operation_risk_critical',
                projectedMemory,
                prediction
            };
        }

        if (projectedMemory > warningThreshold || prediction.riskLevel === 'high') {
            return {
                proceed: true,
                warning: true,
                reason: 'high_risk_operation',
                projectedMemory,
                prediction,
                recommendations: prediction.recommendations
            };
        }

        return {
            proceed: true,
            projectedMemory,
            prediction
        };
    }

    /**
     * Analyze memory patterns for insights
     */
    analyzeMemoryPatterns() {
        if (this.memoryPatterns.size === 0) return;

        const patterns = Array.from(this.memoryPatterns.entries());
        const highRiskPatterns = patterns.filter(([key, pattern]) => pattern.riskScore >= this.riskThresholds.high);
        
        if (highRiskPatterns.length > 0) {
            console.log(`⚠️ [PREDICTIVE] Found ${highRiskPatterns.length} high-risk patterns:`);
            highRiskPatterns.forEach(([key, pattern]) => {
                console.log(`   ${key}: ${pattern.averageMemoryIncrease.toFixed(1)}MB avg, ${(pattern.successRate * 100).toFixed(1)}% success`);
            });
        }

        // Emit insights
        this.emit('patternAnalysis', {
            totalPatterns: patterns.length,
            highRiskPatterns: highRiskPatterns.length,
            averageMemoryIncrease: patterns.reduce((sum, [, p]) => sum + p.averageMemoryIncrease, 0) / patterns.length
        });
    }

    /**
     * Update prediction models
     */
    updatePredictionModels() {
        for (const [key, pattern] of this.memoryPatterns) {
            if (Date.now() - pattern.lastUpdated > 86400000) { // 24 hours
                pattern.riskScore *= 0.95;
            }
        }
    }

    /**
     * Clean up old history
     */
    cleanupOldHistory() {
        const cutoffTime = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7 days
        this.operationHistory = this.operationHistory.filter(op => op.timestamp > cutoffTime);
        
        const patternCutoffTime = Date.now() - (30 * 24 * 60 * 60 * 1000); // 30 days
        for (const [key, pattern] of this.memoryPatterns) {
            if (pattern.lastUpdated < patternCutoffTime) {
                this.memoryPatterns.delete(key);
            }
        }
    }

    /**
     * Get comprehensive memory insights
     */
    getMemoryInsights() {
        const patterns = Array.from(this.memoryPatterns.entries());
        
        return {
            totalPatterns: patterns.length,
            totalOperations: this.operationHistory.length,
            riskDistribution: {
                low: patterns.filter(([, p]) => p.riskScore < this.riskThresholds.medium).length,
                medium: patterns.filter(([, p]) => p.riskScore >= this.riskThresholds.medium && p.riskScore < this.riskThresholds.high).length,
                high: patterns.filter(([, p]) => p.riskScore >= this.riskThresholds.high && p.riskScore < this.riskThresholds.critical).length,
                critical: patterns.filter(([, p]) => p.riskScore >= this.riskThresholds.critical).length
            },
            topRiskyPatterns: patterns
                .sort(([, a], [, b]) => b.riskScore - a.riskScore)
                .slice(0, 5)
                .map(([key, pattern]) => ({
                    pattern: key,
                    riskScore: pattern.riskScore,
                    averageMemoryIncrease: pattern.averageMemoryIncrease,
                    successRate: pattern.successRate
                })),
            recentOperations: this.operationHistory.slice(-10)
        };
    }
}

module.exports = new PredictiveMemoryManager();
