/**
 * Test script to profile the "globa" search with detailed memory tracking
 * Run with: node --expose-gc test-globa-search-profiling.js
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

console.log('🔬 Starting Detailed Memory Profiling for "globa" Search\n');

// Configuration
const BASE_URL = 'http://localhost:3000';
const SEARCH_TERM = 'globa';
const PARTNER_ID = '7ff6a0f3-406a-11f0-8998-02e4ff3e0f11'; // Replace with actual partner ID

async function profileGlobaSearch() {
    console.log('🎯 Test Configuration:');
    console.log(`   Search Term: "${SEARCH_TERM}"`);
    console.log(`   Base URL: ${BASE_URL}`);
    console.log(`   Partner ID: ${PARTNER_ID}`);
    console.log('');

    try {
        // Test 1: Profile Page 1
        console.log('📊 Test 1: Profiling Page 1 Search');
        await profileSingleRequest(1);

        // Wait a bit between requests
        console.log('\n⏳ Waiting 5 seconds before next test...\n');
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Test 2: Profile Page 2 (to test pagination)
        console.log('📊 Test 2: Profiling Page 2 Search');
        await profileSingleRequest(2);

        // Wait a bit between requests
        console.log('\n⏳ Waiting 5 seconds before next test...\n');
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Test 3: Force garbage collection and test again
        console.log('📊 Test 3: After Garbage Collection');
        if (global.gc) {
            console.log('🗑️ Forcing garbage collection...');
            global.gc();
        }
        await profileSingleRequest(1);

        console.log('\n✅ All profiling tests completed!');
        console.log('\n📄 Check the logs/ directory for detailed profiling reports:');
        console.log('   - memory-profile-broad-search-globa-*.json');
        console.log('   - heapdump-broad-search-globa-*.heapsnapshot');

    } catch (error) {
        console.error('\n❌ Profiling test failed:', error.message);
        if (error.response) {
            console.error('   Status:', error.response.status);
            console.error('   Data:', error.response.data);
        }
    }
}

async function profileSingleRequest(page) {
    const startTime = Date.now();
    
    console.log(`🔍 Making request to page ${page}...`);
    
    const url = `${BASE_URL}/api/esim-plans/partner`;
    const params = {
        countryId: '',
        region: '',
        category: 'esim_realtime',
        page: page,
        limit: 24,
        search: SEARCH_TERM
    };

    // Add partner authentication if needed
    const headers = {
        'Content-Type': 'application/json',
        // Add any required authentication headers here
    };

    try {
        const response = await axios.get(url, { 
            params, 
            headers,
            timeout: 30000 // 30 second timeout
        });

        const duration = Date.now() - startTime;
        const responseSize = JSON.stringify(response.data).length;

        console.log(`✅ Request completed successfully:`);
        console.log(`   Duration: ${duration}ms`);
        console.log(`   Response Size: ${(responseSize / 1024).toFixed(1)}KB`);
        console.log(`   Plans Returned: ${response.data.plans ? response.data.plans.length : 0}`);
        console.log(`   Total Items: ${response.data.totalItems || 'unknown'}`);
        console.log(`   Total Pages: ${response.data.totalPages || 'unknown'}`);
        console.log(`   Current Page: ${response.data.page || 'unknown'}`);

        // Check if metadata was included
        const hasCountries = response.data.countries && response.data.countries.length > 0;
        const hasRegions = response.data.regions && response.data.regions.length > 0;
        console.log(`   Metadata Included: Countries=${hasCountries}, Regions=${hasRegions}`);

        return response.data;

    } catch (error) {
        const duration = Date.now() - startTime;
        console.error(`❌ Request failed after ${duration}ms:`);
        
        if (error.response) {
            console.error(`   Status: ${error.response.status}`);
            console.error(`   Error: ${error.response.data?.error || 'Unknown error'}`);
            console.error(`   Message: ${error.response.data?.message || 'No message'}`);
        } else if (error.code === 'ECONNREFUSED') {
            console.error(`   Error: Cannot connect to server at ${BASE_URL}`);
            console.error(`   Make sure the server is running on port 3000`);
        } else {
            console.error(`   Error: ${error.message}`);
        }
        
        throw error;
    }
}

function checkServerStatus() {
    return new Promise((resolve) => {
        axios.get(`${BASE_URL}/health`)
            .then(() => {
                console.log('✅ Server is running and accessible');
                resolve(true);
            })
            .catch(() => {
                console.log('❌ Server is not accessible');
                console.log('   Make sure to start the server with: npm start');
                console.log('   And ensure it\'s running on port 3000');
                resolve(false);
            });
    });
}

async function checkLogsDirectory() {
    const logsDir = path.join(__dirname, 'logs');
    
    if (!fs.existsSync(logsDir)) {
        console.log('📁 Creating logs directory...');
        fs.mkdirSync(logsDir, { recursive: true });
    }
    
    console.log(`📁 Logs will be saved to: ${logsDir}`);
}

function showInstructions() {
    console.log('📋 Instructions for Analysis:');
    console.log('');
    console.log('1. 📄 Check the generated JSON report:');
    console.log('   - Look for memory differences between snapshots');
    console.log('   - Identify which operation caused the largest memory increase');
    console.log('   - Review potential memory leaks and recommendations');
    console.log('');
    console.log('2. 🔍 Analyze heap dumps (if generated):');
    console.log('   - Open .heapsnapshot files in Chrome DevTools');
    console.log('   - Go to Chrome DevTools > Memory > Load');
    console.log('   - Compare snapshots to see object retention');
    console.log('');
    console.log('3. 📊 Monitor server logs:');
    console.log('   - Watch for detailed profiler messages');
    console.log('   - Look for memory snapshots at each stage');
    console.log('   - Check for object count changes');
    console.log('');
    console.log('4. 🎯 Key metrics to watch:');
    console.log('   - Heap used difference between start and end');
    console.log('   - Memory increase during database queries');
    console.log('   - Object count changes in heap spaces');
    console.log('   - Detached contexts (potential memory leaks)');
}

// Main execution
async function main() {
    console.log('🚀 Detailed Memory Profiling Test for "globa" Search');
    console.log('=' .repeat(60));
    console.log('');

    // Check if GC is available
    if (!global.gc) {
        console.log('⚠️  Warning: Garbage collection not available');
        console.log('   For best results, run with: node --expose-gc test-globa-search-profiling.js');
        console.log('');
    } else {
        console.log('✅ Garbage collection is available');
        console.log('');
    }

    // Setup
    await checkLogsDirectory();
    
    // Check server status
    const serverRunning = await checkServerStatus();
    if (!serverRunning) {
        console.log('\n❌ Cannot proceed without server running');
        process.exit(1);
    }

    console.log('');
    
    // Run profiling tests
    await profileGlobaSearch();
    
    console.log('');
    showInstructions();
}

// Handle errors
process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    process.exit(1);
});

// Run the test
main().catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
});
