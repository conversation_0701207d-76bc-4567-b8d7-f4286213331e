/**
 * Memory Management Routes
 * Provides endpoints for monitoring and managing application memory
 */

const express = require('express');
const router = express.Router();
const { isAuthenticated, isAdmin } = require('../middleware/auth');
const memoryManagementController = require('../controllers/memoryManagementController');
const { memoryStats } = require('../middleware/memoryLeakPrevention');

// Apply authentication and memory stats middleware to all routes
router.use(isAuthenticated);
router.use(memoryStats);

/**
 * GET /api/memory/status
 * Get comprehensive memory status and statistics
 */
router.get('/status', memoryManagementController.getMemoryStatus);

/**
 * GET /api/memory/prediction
 * Get memory usage prediction for an operation
 * Query params: operationType, searchType, searchTerm, resultCount
 */
router.get('/prediction', memoryManagementController.getMemoryPrediction);

/**
 * GET /api/memory/searches
 * Get active search operations and their memory usage
 */
router.get('/searches', memoryManagementController.getActiveSearches);

/**
 * GET /api/memory/history
 * Get memory usage history and patterns
 * Query params: limit (default: 20)
 */
router.get('/history', memoryManagementController.getMemoryHistory);

// Admin-only routes for memory management
router.use(isAdmin);

/**
 * POST /api/memory/cleanup
 * Trigger manual memory cleanup
 * Body: { type: 'standard|emergency|proactive|gc|pools|weak_references', aggressive: boolean }
 */
router.post('/cleanup', memoryManagementController.triggerMemoryCleanup);

/**
 * POST /api/memory/emergency
 * Trigger emergency memory cleanup for all operations
 */
router.post('/emergency', memoryManagementController.emergencyCleanup);

/**
 * PUT /api/memory/settings
 * Configure memory management settings
 * Body: { autoCleanupThreshold, autoCleanupInterval, emergencyThreshold, enablePredictiveManagement }
 */
router.put('/settings', memoryManagementController.configureMemorySettings);

/**
 * POST /api/memory/test
 * Run comprehensive memory cleanup tests
 */
router.post('/test', memoryManagementController.runMemoryTests);

/**
 * GET /api/memory/test/results
 * Get memory test results and history
 */
router.get('/test/results', memoryManagementController.getMemoryTestResults);

/**
 * GET /api/memory/debug
 * Get detailed debug information (admin only)
 */
router.get('/debug', (req, res) => {
    try {
        const memoryStats = req.getMemoryStats();
        const processInfo = {
            pid: process.pid,
            uptime: process.uptime(),
            version: process.version,
            platform: process.platform,
            arch: process.arch,
            memoryUsage: process.memoryUsage(),
            cpuUsage: process.cpuUsage()
        };

        res.json({
            process: processInfo,
            memory: memoryStats,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error getting debug info:', error);
        res.status(500).json({
            error: 'Failed to get debug information',
            message: error.message
        });
    }
});

/**
 * GET /api/memory/health
 * Simple health check endpoint
 */
router.get('/health', (req, res) => {
    try {
        const memUsage = process.memoryUsage();
        const heapUsedMB = memUsage.heapUsed / 1024 / 1024;
        const heapTotalMB = memUsage.heapTotal / 1024 / 1024;
        const heapUtilization = (heapUsedMB / heapTotalMB) * 100;

        const status = heapUsedMB > 1500 ? 'critical' : 
                      heapUsedMB > 1000 ? 'warning' : 
                      heapUtilization > 80 ? 'caution' : 'healthy';

        res.json({
            status,
            memory: {
                heapUsed: heapUsedMB.toFixed(1) + 'MB',
                heapTotal: heapTotalMB.toFixed(1) + 'MB',
                heapUtilization: heapUtilization.toFixed(1) + '%',
                rss: (memUsage.rss / 1024 / 1024).toFixed(1) + 'MB'
            },
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            status: 'error',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

module.exports = router;
