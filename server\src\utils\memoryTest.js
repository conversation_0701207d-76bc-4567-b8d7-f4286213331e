/**
 * Memory testing utility to simulate different memory environments
 * Usage: node src/utils/memoryTest.js [memory_limit_mb]
 */

const memoryMonitor = require('./memoryMonitor');

function testMemoryConfiguration(customMemoryLimitMB = null) {
    console.log('🧪 Memory Configuration Test\n');
    
    // Create memory monitor with custom limit if provided
    const options = customMemoryLimitMB ? {
        maxHeapUsage: customMemoryLimitMB * 1024 * 1024
    } : {};
    
    const monitor = new (require('./memoryMonitor').constructor)(options);
    
    console.log('📊 Current Memory Status:');
    monitor.logMemoryStatus();
    
    console.log('\n🎯 Processing Strategy:');
    const strategy = monitor.getProcessingStrategy();
    console.log(`   Strategy: ${strategy.strategy}`);
    console.log(`   Batch Size: ${strategy.batchSize}`);
    console.log(`   GC Frequency: Every ${strategy.gcFrequency} items`);
    console.log(`   Description: ${strategy.description}`);
    
    console.log('\n🤔 Single Query Analysis:');
    const testCases = [100, 500, 1000, 2000, 5000, 10000];
    
    testCases.forEach(recordCount => {
        const shouldUse = monitor.shouldUseSingleQuery(recordCount);
        console.log(`   ${recordCount} records: ${shouldUse ? '✅ Single Query' : '❌ Batch Processing'}`);
    });
    
    console.log('\n💡 Memory Recommendations:');
    const maxHeapMB = monitor.maxHeapUsage / 1024 / 1024;
    
    if (maxHeapMB < 512) {
        console.log('   ⚠️  Very Low Memory Environment');
        console.log('   • Use batch processing for exports > 100 records');
        console.log('   • Force GC every 50 items');
        console.log('   • Consider limiting concurrent operations');
    } else if (maxHeapMB < 1024) {
        console.log('   ⚠️  Low Memory Environment');
        console.log('   • Use batch processing for exports > 500 records');
        console.log('   • Force GC every 100 items');
        console.log('   • Monitor memory usage closely');
    } else if (maxHeapMB < 2048) {
        console.log('   📊 Medium Memory Environment');
        console.log('   • Single query safe for exports < 2000 records');
        console.log('   • Force GC every 250 items');
        console.log('   • Good balance of performance and safety');
    } else {
        console.log('   🚀 High Memory Environment');
        console.log('   • Single query safe for most export sizes');
        console.log('   • Force GC every 500 items');
        console.log('   • Optimal performance available');
    }
}

function simulateMemoryEnvironments() {
    console.log('🌍 Simulating Different Memory Environments\n');
    
    const environments = [
        { name: 'Raspberry Pi / Very Low Memory', memory: 256 },
        { name: 'Basic VPS / Low Memory', memory: 512 },
        { name: 'Standard VPS / Medium Memory', memory: 1024 },
        { name: 'High-End VPS / High Memory', memory: 2048 },
        { name: 'Dedicated Server / Very High Memory', memory: 4096 }
    ];
    
    environments.forEach((env, index) => {
        console.log(`\n${'='.repeat(60)}`);
        console.log(`${index + 1}. ${env.name} (${env.memory}MB)`);
        console.log('='.repeat(60));
        testMemoryConfiguration(env.memory);
    });
}

// Main execution
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length > 0) {
        const customMemory = parseInt(args[0]);
        if (isNaN(customMemory) || customMemory < 128) {
            console.error('❌ Please provide a valid memory limit in MB (minimum 128MB)');
            process.exit(1);
        }
        console.log(`🎯 Testing with custom memory limit: ${customMemory}MB\n`);
        testMemoryConfiguration(customMemory);
    } else {
        console.log('🔍 Auto-detecting current environment:\n');
        testMemoryConfiguration();
        
        console.log('\n\n🌍 Want to see all environments? Run:');
        console.log('   node src/utils/memoryTest.js simulate');
        
        if (args[0] === 'simulate') {
            simulateMemoryEnvironments();
        }
    }
}

module.exports = {
    testMemoryConfiguration,
    simulateMemoryEnvironments
};
