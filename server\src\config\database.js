const { Sequelize } = require('sequelize');
require('dotenv').config();

// Configure connection pool based on environment
const getPoolConfig = () => {
    if (process.env.NODE_ENV === 'test') {
        return {
            max: 200, 
            min: 50, 
            acquire: 120000, 
            idle: 60000, 
            evict: 30000 
        };
    }

    // Production-optimized pool configuration
    if (process.env.NODE_ENV === 'production') {
        return {
            max: 30, 
            min: 10, 
            acquire: 60000, 
            idle: 300000,
            evict: 60000 
        };
    }

    // Development configuration
    return {
        max: 20, 
        min: 5, 
        acquire: 30000,
        idle: 10000
    };
};

const sequelize = new Sequelize(
    process.env.DB_NAME || 'esim_demo',
    process.env.DB_USER || 'root',
    process.env.DB_PASSWORD || '',
    {
        host: process.env.DB_HOST || 'localhost',
        dialect: 'mysql',
        logging: process.env.NODE_ENV === 'development' ? console.log : false,
        pool: getPoolConfig(),
        define: {
            // Use underscored naming convention
            underscored: false,
            // Don't add timestamps
            timestamps: true,
            // Force table names to match model names exactly
            freezeTableName: false,
            // Disable automatic pluralization of table names
            pluralize: false
        },
        // Optimized dialect options for production performance
        dialectOptions: {
            insecureAuth: true,
            charset: 'utf8mb4',
            // Production optimizations (MySQL2 compatible)
            connectTimeout: 60000, // 60 seconds connection timeout
            // SSL configuration (uncomment if using SSL)
            // ssl: process.env.NODE_ENV === 'production' ? {
            //     rejectUnauthorized: false
            // } : false
        },
        // Query optimization settings
        benchmark: process.env.NODE_ENV === 'development' // Log query execution time in development
    }
);

// Connection warming and health check
const warmUpConnections = async () => {
    try {
        console.log('Warming up database connections...');
        const startTime = Date.now();

        // Simple connection test first
        await sequelize.query('SELECT 1 as connection_test');

        // Pre-warm a few connections sequentially to avoid overwhelming the pool
        const poolConfig = getPoolConfig();
        const warmUpCount = Math.min(poolConfig.min || 3, 5); // Conservative approach

        for (let i = 0; i < warmUpCount; i++) {
            try {
                await sequelize.query('SELECT 1 as test');
                // Small delay between connections to avoid overwhelming
                await new Promise(resolve => setTimeout(resolve, 100));
            } catch (error) {
                console.warn(`Warning: Failed to warm up connection ${i + 1}:`, error.message);
            }
        }

        const duration = Date.now() - startTime;
        console.log(`Database connections warmed up successfully in ${duration}ms`);

        // Set up periodic health checks in production (less frequent)
        if (process.env.NODE_ENV === 'production') {
            setInterval(async () => {
                try {
                    await sequelize.query('SELECT 1 as health_check');
                } catch (error) {
                    console.error('Database health check failed:', error.message);
                }
            }, 600000); // Every 10 minutes (reduced frequency)
        }

    } catch (error) {
        console.error('Failed to warm up database connections:', error.message);
        // Don't throw error - let the app continue without warmup
        console.log('Continuing without connection warmup...');
    }
};

// Export both sequelize instance and warm-up function
module.exports = sequelize;
module.exports.warmUpConnections = warmUpConnections;
