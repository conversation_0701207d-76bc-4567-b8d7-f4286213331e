-- Create the database if it doesn't exist
CREATE DATABASE IF NOT EXISTS esim_demo
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

-- Use the database
USE esim_demo;


CREATE TABLE IF NOT EXISTS countries (
    id CHAR(2) PRIMARY KEY,
    iso3 CHAR(3) NOT NULL,
    name VARCHAR(100) NOT NULL,
    flagEmoji VARCHAR(10),         -- Store Unicode flag emoji
    flagUrl VARCHAR(255),          -- Store URL to flag image
    phoneCode VARCHAR(10),
    currencyCode CHAR(3),
    currencySymbol VARCHAR(5),
    region VARCHAR(50),
    isActive BOOLEAN DEFAULT true,
    createdAt DATETIME NOT NULL,
    updatedAt DATETIME NOT NULL,
    UNIQUE KEY idx_country_iso3 (iso3),
    INDEX idx_country_search (name, region, isActive)
) ENGINE=InnoDB;



-- Create Users table
CREATE TABLE IF NOT EXISTS users (
    id CHAR(36) PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    alternateEmail VARCHAR(255),
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'partner') NOT NULL DEFAULT 'partner',
    apiKey VARCHAR(255) NULL,
    apiKeyHash VARCHAR(255) NULL,
    apiKeyLastReset TIMESTAMP NULL,
    firstName VARCHAR(100) NOT NULL,
    lastName VARCHAR(100) NOT NULL,
    countryId CHAR(2),
    phoneNumber VARCHAR(20),
    alternatePhoneNumber VARCHAR(20),
    businessName VARCHAR(200),
    businessEmail VARCHAR(255),
    billingAddressLine1 VARCHAR(255),
    billingAddressLine2 VARCHAR(255),
    billingCity VARCHAR(100),
    billingProvince VARCHAR(100),
    billingCountryId CHAR(2),
    billingPostalCode VARCHAR(20),
    markupPercentage DECIMAL(5,2) NOT NULL DEFAULT 0 CHECK (markupPercentage >= 0 AND markupPercentage <= 100),
    isActive BOOLEAN DEFAULT true,
    lastLogin DATETIME,
    loginAttempts TINYINT UNSIGNED DEFAULT 0,
    lockUntil DATETIME,
    resetToken VARCHAR(255),
    resetTokenExpiry TIMESTAMP,
    createdAt DATETIME NOT NULL,
    updatedAt DATETIME NOT NULL,
    INDEX idx_api_key_hash (apiKeyHash),
    UNIQUE KEY unique_email (email),
    UNIQUE KEY unique_business_email (businessEmail),
    FOREIGN KEY (countryId) REFERENCES countries(id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (billingCountryId) REFERENCES countries(id) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB;

-- Create OTPs table
CREATE TABLE IF NOT EXISTS OTPs (
    id CHAR(36) PRIMARY KEY,
    userId CHAR(36) NOT NULL,
    code CHAR(6) NOT NULL,
    expiresAt DATETIME NOT NULL,
    isUsed BOOLEAN DEFAULT false,
    createdAt DATETIME NOT NULL,
    updatedAt DATETIME NOT NULL,
    INDEX idx_user_expires (userId, expiresAt, isUsed),
    INDEX idx_cleanup (expiresAt, isUsed),
    FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB;

-- Create Sessions table for better session management
CREATE TABLE IF NOT EXISTS sessions (
    id CHAR(36) PRIMARY KEY,
    userId CHAR(36) NOT NULL,
    token VARCHAR(500) NOT NULL,
    expiresAt DATETIME NOT NULL,
    createdAt DATETIME NOT NULL,
    updatedAt DATETIME NOT NULL,
    userAgent VARCHAR(255),
    ipAddress VARCHAR(45),  -- IPv6 compatible
    isValid BOOLEAN DEFAULT true,
    INDEX idx_user_valid (userId, isValid),
    INDEX idx_token_expires (token(64), expiresAt),  -- Partial index on token
    INDEX idx_cleanup (expiresAt, isValid),
    FOREIGN KEY fk_session_user (userId)
        REFERENCES users(id)
        ON DELETE CASCADE
) ENGINE=InnoDB;

-- Create AuditLog table for security tracking
CREATE TABLE IF NOT EXISTS auditLog (
    id BIGINT UNSIGNED AUTO_INCREMENT,
    userId CHAR(36),
    action VARCHAR(50) NOT NULL,
    details JSON,
    ipAddress VARCHAR(45),
    userAgent VARCHAR(255),
    createdAt DATETIME NOT NULL,
    PRIMARY KEY (id, createdAt),
    INDEX idx_user_action (userId, action, createdAt),
    INDEX idx_created_at (createdAt)
) ENGINE=InnoDB
PARTITION BY RANGE (TO_DAYS(createdAt)) (
    PARTITION p_2024 VALUES LESS THAN (TO_DAYS('2025-01-01')),
    PARTITION p_2025 VALUES LESS THAN (TO_DAYS('2026-01-01')),
    PARTITION p_2026 VALUES LESS THAN (TO_DAYS('2027-01-01')),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);


-- Create stored procedure for cleaning up expired OTPs
DELIMITER //
CREATE PROCEDURE cleanup_expired_otps()
BEGIN
    DELETE FROM OTPs 
    WHERE (expiresAt < NOW() AND isUsed = false) 
    OR createdAt < DATE_SUB(NOW(), INTERVAL 7 DAY);
END //
DELIMITER ;


-- Create event to automatically clean up expired OTPs
CREATE EVENT IF NOT EXISTS cleanup_otps_event
ON SCHEDULE EVERY 1 DAY
DO CALL cleanup_expired_otps();

-- Create stored procedure for cleaning up expired sessions
DELIMITER //
CREATE PROCEDURE cleanup_expired_sessions()
BEGIN
    DELETE FROM sessions 
    WHERE expiresAt < NOW() 
    OR createdAt < DATE_SUB(NOW(), INTERVAL 30 DAY);
END //
DELIMITER ;

-- Create event to automatically clean up expired sessions
CREATE EVENT IF NOT EXISTS cleanup_sessions_event
ON SCHEDULE EVERY 1 DAY
DO CALL cleanup_expired_sessions();

-- Create notification_messages table for webhook notification storage
CREATE TABLE IF NOT EXISTS notification_messages (
    id CHAR(36) PRIMARY KEY,
    provider VARCHAR(50) NOT NULL COMMENT 'Provider name (e.g., billionconnect, mobimatter)',
    notificationType VARCHAR(20) NOT NULL COMMENT 'Notification type (e.g., N009, F041, etc.)',
    rawPayload JSON NOT NULL COMMENT 'Complete raw webhook payload as received',
    orderId VARCHAR(100) NULL COMMENT 'External order ID from the notification',
    channelOrderId VARCHAR(100) NULL COMMENT 'Channel order ID from the notification',
    internalOrderId CHAR(36) NULL COMMENT 'Reference to internal order if found',
    status ENUM('pending', 'processing', 'completed', 'failed', 'ignored') NOT NULL DEFAULT 'pending' COMMENT 'Processing status of the notification',
    processingAttempts INT NOT NULL DEFAULT 0 COMMENT 'Number of processing attempts made',
    lastProcessingAttempt DATETIME NULL COMMENT 'Timestamp of last processing attempt',
    processingError TEXT NULL COMMENT 'Error message from last failed processing attempt',
    processingResult JSON NULL COMMENT 'Result data from successful processing',
    receivedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp when notification was received',
    processedAt DATETIME NULL COMMENT 'Timestamp when notification was successfully processed',
    metadata JSON NULL COMMENT 'Additional metadata for processing context',
    createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_notification_provider_type (provider, notificationType),
    INDEX idx_notification_status (status, receivedAt),
    INDEX idx_notification_order_ids (orderId, channelOrderId),
    INDEX idx_notification_processing (status, processingAttempts, lastProcessingAttempt),
    INDEX idx_notification_cleanup (status, createdAt),
    FOREIGN KEY (internalOrderId) REFERENCES orders(id) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB;


