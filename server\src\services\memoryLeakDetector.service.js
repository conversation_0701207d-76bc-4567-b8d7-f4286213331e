/**
 * Memory Leak Detection Service
 * Monitors memory usage patterns and detects potential memory leaks
 */

const memoryMonitor = require('../utils/memoryMonitor');

class MemoryLeakDetectorService {
    constructor() {
        this.memoryHistory = [];
        this.maxHistorySize = 100; // Keep last 100 memory readings
        this.alertThreshold = 3; // Alert after 3 consecutive leak detections
        this.consecutiveLeaks = 0;
        this.lastAlertTime = 0;
        this.alertCooldown = 5 * 60 * 1000; // 5 minutes between alerts
        this.isMonitoring = false;
        this.monitoringInterval = null;
    }

    /**
     * Start memory leak detection monitoring
     */
    startMonitoring() {
        if (this.isMonitoring) return;

        this.isMonitoring = true;
        console.log('🔍 Memory leak detector started');

        // Start with normal monitoring interval
        this.normalInterval = 30000; // 30 seconds
        this.criticalInterval = 10000; // 10 seconds during critical periods
        this.currentInterval = this.normalInterval;

        this.monitoringInterval = setInterval(() => {
            this.checkForMemoryLeaks();
            this.adjustMonitoringFrequency();
        }, this.currentInterval);
    }

    /**
     * Adjust monitoring frequency based on memory pressure
     */
    adjustMonitoringFrequency() {
        const usage = memoryMonitor.getMemoryUsage();
        const shouldUseCriticalInterval = usage.heapUsagePercent > 80 || this.consecutiveLeaks > 0;

        if (shouldUseCriticalInterval && this.currentInterval !== this.criticalInterval) {
            console.log('🔍 Switching to critical monitoring frequency (10s)');
            this.currentInterval = this.criticalInterval;
            this.restartMonitoring();
        } else if (!shouldUseCriticalInterval && this.currentInterval !== this.normalInterval) {
            console.log('🔍 Switching to normal monitoring frequency (30s)');
            this.currentInterval = this.normalInterval;
            this.restartMonitoring();
        }
    }

    /**
     * Restart monitoring with new interval
     */
    restartMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
        }

        this.monitoringInterval = setInterval(() => {
            this.checkForMemoryLeaks();
            this.adjustMonitoringFrequency();
        }, this.currentInterval);
    }

    /**
     * Stop memory leak detection monitoring
     */
    stopMonitoring() {
        if (!this.isMonitoring) return;

        this.isMonitoring = false;
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        console.log('🔍 Memory leak detector stopped');
    }

    /**
     * Record current memory usage
     */
    recordMemoryUsage() {
        const usage = memoryMonitor.getMemoryUsage();
        const timestamp = Date.now();

        this.memoryHistory.push({
            timestamp,
            heapUsed: usage.heapUsed,
            heapTotal: usage.heapTotal,
            heapUsagePercent: usage.heapUsagePercent,
            external: usage.external,
            rss: usage.rss
        });

        // Keep only the last N readings
        if (this.memoryHistory.length > this.maxHistorySize) {
            this.memoryHistory.shift();
        }
    }

    /**
     * Analyze memory usage patterns for potential leaks
     */
    analyzeMemoryPattern() {
        if (this.memoryHistory.length < 10) {
            return { detected: false, reason: 'Insufficient data' };
        }

        const recent = this.memoryHistory.slice(-10); // Last 10 readings
        const older = this.memoryHistory.slice(-20, -10); // Previous 10 readings

        if (older.length === 0) {
            return { detected: false, reason: 'Insufficient historical data' };
        }

        // Calculate average memory usage for both periods
        const recentAvg = recent.reduce((sum, r) => sum + r.heapUsed, 0) / recent.length;
        const olderAvg = older.reduce((sum, r) => sum + r.heapUsed, 0) / older.length;

        // Calculate growth rate
        const growthRate = ((recentAvg - olderAvg) / olderAvg) * 100;

        // Check for concerning patterns
        const indicators = {
            steadyGrowth: growthRate > 5, // More than 5% growth
            highMemoryUsage: recentAvg > (memoryMonitor.maxHeapUsage * 0.8),
            rapidGrowth: growthRate > 15, // More than 15% growth
            consistentHigh: recent.every(r => r.heapUsagePercent > 70)
        };

        const leakScore = Object.values(indicators).filter(Boolean).length;
        const detected = leakScore >= 2;

        return {
            detected,
            leakScore,
            indicators,
            growthRate: growthRate.toFixed(2),
            recentAvg: Math.round(recentAvg / 1024 / 1024),
            olderAvg: Math.round(olderAvg / 1024 / 1024)
        };
    }

    /**
     * Check for memory leaks and take action if detected
     */
    checkForMemoryLeaks() {
        try {
            // Record current memory usage
            this.recordMemoryUsage();

            // Analyze patterns
            const analysis = this.analyzeMemoryPattern();

            if (analysis.detected) {
                this.consecutiveLeaks++;
                console.warn(`🚨 Memory leak pattern detected (${this.consecutiveLeaks}/${this.alertThreshold})`);
                console.warn(`   Growth rate: ${analysis.growthRate}%`);
                console.warn(`   Recent avg: ${analysis.recentAvg}MB, Older avg: ${analysis.olderAvg}MB`);
                console.warn(`   Indicators:`, analysis.indicators);

                // Alert if threshold reached and not in cooldown
                if (this.consecutiveLeaks >= this.alertThreshold && this.shouldAlert()) {
                    this.sendMemoryLeakAlert(analysis);
                    this.lastAlertTime = Date.now();
                }
            } else {
                // Reset consecutive counter if no leak detected
                if (this.consecutiveLeaks > 0) {
                    console.log(`✅ Memory pattern normalized (was ${this.consecutiveLeaks} consecutive leaks)`);
                    this.consecutiveLeaks = 0;
                }
            }

            // Also check the memory monitor's leak detection
            const monitorCheck = memoryMonitor.checkForMemoryLeaks();
            if (monitorCheck.detected && this.shouldAlert()) {
                this.sendCriticalMemoryAlert(monitorCheck);
                this.lastAlertTime = Date.now();
            }

        } catch (error) {
            console.error('Error in memory leak detection:', error);
        }
    }

    /**
     * Check if we should send an alert (not in cooldown)
     */
    shouldAlert() {
        return (Date.now() - this.lastAlertTime) > this.alertCooldown;
    }

    /**
     * Send memory leak alert
     */
    sendMemoryLeakAlert(analysis) {
        console.error('🚨 MEMORY LEAK ALERT 🚨');
        console.error(`   Consecutive detections: ${this.consecutiveLeaks}`);
        console.error(`   Memory growth rate: ${analysis.growthRate}%`);
        console.error(`   Recent average: ${analysis.recentAvg}MB`);
        console.error(`   Older average: ${analysis.olderAvg}MB`);
        console.error(`   Leak indicators:`, analysis.indicators);
        console.error('   Recommendation: Consider restarting the application');

        // Force garbage collection
        console.error('   Forcing garbage collection...');
        memoryMonitor.forceGarbageCollection();
    }

    /**
     * Send critical memory alert
     */
    sendCriticalMemoryAlert(monitorCheck) {
        console.error('🚨 CRITICAL MEMORY ALERT 🚨');
        console.error(`   Leak score: ${monitorCheck.score}/4`);
        console.error(`   Current usage: ${monitorCheck.usage.heapUsagePercent.toFixed(1)}%`);
        console.error(`   Indicators:`, monitorCheck.indicators);
        console.error('   URGENT: Application may crash soon - restart recommended');

        // Emergency actions for critical memory
        if (monitorCheck.usage.heapUsagePercent > 95) {
            console.error('🚨 EMERGENCY: Activating crash prevention measures');

            // Clear all caches immediately
            memoryMonitor.emergencyCacheClear();

            // Reset our own memory history to free up space
            this.memoryHistory = this.memoryHistory.slice(-10); // Keep only last 10 entries
            console.error('🗑️ Cleared memory history to free space');
        } else {
            // Force garbage collection
            console.error('   Forcing garbage collection...');
            memoryMonitor.forceGarbageCollection();
        }
    }

    /**
     * Get memory leak detection statistics
     */
    getStats() {
        return {
            isMonitoring: this.isMonitoring,
            historySize: this.memoryHistory.length,
            consecutiveLeaks: this.consecutiveLeaks,
            lastAlertTime: this.lastAlertTime,
            currentMemoryUsage: memoryMonitor.getMemoryUsage()
        };
    }

    /**
     * Get memory usage history
     */
    getMemoryHistory() {
        return this.memoryHistory.slice(); // Return copy
    }

    /**
     * Clear memory history (useful for testing)
     */
    clearHistory() {
        this.memoryHistory = [];
        this.consecutiveLeaks = 0;
        console.log('🗑️ Memory leak detector history cleared');
    }
}

// Create singleton instance
const memoryLeakDetector = new MemoryLeakDetectorService();

module.exports = memoryLeakDetector;
