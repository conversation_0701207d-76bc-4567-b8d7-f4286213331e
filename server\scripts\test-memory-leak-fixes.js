#!/usr/bin/env node

/**
 * Test script to validate memory leak fixes
 * This script simulates high load on the products API to test memory management
 */

const axios = require('axios');
const memoryMonitor = require('../src/utils/memoryMonitor');
const { getCacheStats } = require('../src/utils/cacheManager');

class MemoryLeakTester {
    constructor() {
        this.baseUrl = process.env.TEST_BASE_URL || 'http://localhost:3000';
        this.apiKey = process.env.TEST_API_KEY || 'your-test-api-key';
        this.testDuration = 5 * 60 * 1000; // 5 minutes
        this.requestInterval = 1000; // 1 second between requests
        this.maxConcurrentRequests = 5;
        this.results = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            memorySnapshots: [],
            cacheStats: [],
            errors: []
        };
    }

    /**
     * Log test results
     */
    logResult(testName, passed, details) {
        const status = passed ? '✅ PASS' : '❌ FAIL';
        console.log(`${status} ${testName}`);
        if (details) {
            console.log(`   ${details}`);
        }
    }

    /**
     * Take memory snapshot
     */
    takeMemorySnapshot() {
        const usage = memoryMonitor.getMemoryUsage();
        const cacheStats = getCacheStats();
        
        const snapshot = {
            timestamp: Date.now(),
            heapUsedMB: usage.heapUsedMB,
            heapUsagePercent: usage.heapUsagePercent,
            cacheEntries: cacheStats.totalEntries,
            validCacheEntries: cacheStats.validEntries
        };
        
        this.results.memorySnapshots.push(snapshot);
        this.results.cacheStats.push(cacheStats);
        
        return snapshot;
    }

    /**
     * Make API request to products endpoint
     */
    async makeProductsRequest() {
        try {
            const response = await axios.get(`${this.baseUrl}/api/v1/products`, {
                headers: {
                    'X-API-Key': this.apiKey
                },
                timeout: 10000
            });
            
            this.results.successfulRequests++;
            return { success: true, data: response.data };
        } catch (error) {
            this.results.failedRequests++;
            this.results.errors.push({
                timestamp: Date.now(),
                error: error.message,
                status: error.response?.status
            });
            return { success: false, error: error.message };
        }
    }

    /**
     * Run concurrent requests
     */
    async runConcurrentRequests(count = 5) {
        const promises = [];
        for (let i = 0; i < count; i++) {
            promises.push(this.makeProductsRequest());
        }
        
        const results = await Promise.allSettled(promises);
        return results;
    }

    /**
     * Test memory stability under load
     */
    async testMemoryStability() {
        console.log('🧪 Starting memory stability test...');
        console.log(`   Duration: ${this.testDuration / 1000}s`);
        console.log(`   Request interval: ${this.requestInterval}ms`);
        console.log(`   Concurrent requests: ${this.maxConcurrentRequests}`);
        
        const startTime = Date.now();
        let intervalId;
        
        // Take initial snapshot
        const initialSnapshot = this.takeMemorySnapshot();
        console.log(`📊 Initial memory: ${initialSnapshot.heapUsedMB}MB (${initialSnapshot.heapUsagePercent.toFixed(1)}%)`);
        
        return new Promise((resolve) => {
            intervalId = setInterval(async () => {
                const elapsed = Date.now() - startTime;
                
                if (elapsed >= this.testDuration) {
                    clearInterval(intervalId);
                    resolve();
                    return;
                }
                
                // Make concurrent requests
                await this.runConcurrentRequests(this.maxConcurrentRequests);
                this.results.totalRequests += this.maxConcurrentRequests;
                
                // Take memory snapshot every 30 seconds
                if (this.results.totalRequests % 30 === 0) {
                    const snapshot = this.takeMemorySnapshot();
                    console.log(`📊 Memory: ${snapshot.heapUsedMB}MB (${snapshot.heapUsagePercent.toFixed(1)}%), Cache: ${snapshot.cacheEntries} entries`);
                }
                
            }, this.requestInterval);
        });
    }

    /**
     * Analyze memory leak patterns
     */
    analyzeMemoryLeaks() {
        if (this.results.memorySnapshots.length < 2) {
            return { detected: false, reason: 'Insufficient data' };
        }
        
        const snapshots = this.results.memorySnapshots;
        const first = snapshots[0];
        const last = snapshots[snapshots.length - 1];
        
        // Calculate memory growth
        const memoryGrowth = last.heapUsedMB - first.heapUsedMB;
        const percentageGrowth = ((last.heapUsedMB - first.heapUsedMB) / first.heapUsedMB) * 100;
        
        // Check for concerning patterns
        const indicators = {
            highMemoryGrowth: memoryGrowth > 100, // More than 100MB growth
            highPercentageGrowth: percentageGrowth > 50, // More than 50% growth
            finalMemoryHigh: last.heapUsagePercent > 80, // Final memory usage > 80%
            steadyGrowth: this.checkSteadyGrowth(snapshots)
        };
        
        const leakScore = Object.values(indicators).filter(Boolean).length;
        
        return {
            detected: leakScore >= 2,
            leakScore,
            indicators,
            memoryGrowth,
            percentageGrowth,
            initialMemory: first.heapUsedMB,
            finalMemory: last.heapUsedMB
        };
    }

    /**
     * Check for steady memory growth pattern
     */
    checkSteadyGrowth(snapshots) {
        if (snapshots.length < 3) return false;
        
        let growthCount = 0;
        for (let i = 1; i < snapshots.length; i++) {
            if (snapshots[i].heapUsedMB > snapshots[i-1].heapUsedMB) {
                growthCount++;
            }
        }
        
        // If more than 70% of measurements show growth, it's concerning
        return (growthCount / (snapshots.length - 1)) > 0.7;
    }

    /**
     * Test cache efficiency
     */
    testCacheEfficiency() {
        const cacheStats = this.results.cacheStats;
        if (cacheStats.length === 0) return { passed: false, reason: 'No cache stats' };
        
        const lastStats = cacheStats[cacheStats.length - 1];
        const hitRate = lastStats.hitRate || 0;
        const validEntries = lastStats.validEntries || 0;
        const totalEntries = lastStats.totalEntries || 0;
        
        // Cache should have reasonable hit rate and not accumulate too many entries
        const passed = hitRate > 0.3 && totalEntries < 1000; // 30% hit rate, max 1000 entries
        
        return {
            passed,
            hitRate,
            validEntries,
            totalEntries,
            reason: passed ? 'Cache performing well' : 'Cache issues detected'
        };
    }

    /**
     * Run all tests
     */
    async runAllTests() {
        console.log('🧪 Starting Memory Leak Fix Validation Tests\n');
        
        // Test 1: Memory stability under load
        await this.testMemoryStability();
        
        // Test 2: Analyze memory leak patterns
        const leakAnalysis = this.analyzeMemoryLeaks();
        this.logResult(
            'Memory Leak Detection',
            !leakAnalysis.detected,
            leakAnalysis.detected ? 
                `Leak detected (score: ${leakAnalysis.leakScore}/4, growth: ${leakAnalysis.memoryGrowth.toFixed(1)}MB)` :
                `No leak detected (growth: ${leakAnalysis.memoryGrowth?.toFixed(1) || 0}MB)`
        );
        
        // Test 3: Cache efficiency
        const cacheTest = this.testCacheEfficiency();
        this.logResult(
            'Cache Efficiency',
            cacheTest.passed,
            `Hit rate: ${(cacheTest.hitRate * 100).toFixed(1)}%, Entries: ${cacheTest.totalEntries}`
        );
        
        // Test 4: Request success rate
        const successRate = (this.results.successfulRequests / this.results.totalRequests) * 100;
        this.logResult(
            'Request Success Rate',
            successRate > 95,
            `${successRate.toFixed(1)}% (${this.results.successfulRequests}/${this.results.totalRequests})`
        );
        
        // Summary
        console.log('\n📊 Test Summary:');
        console.log(`   Total requests: ${this.results.totalRequests}`);
        console.log(`   Successful: ${this.results.successfulRequests}`);
        console.log(`   Failed: ${this.results.failedRequests}`);
        console.log(`   Memory snapshots: ${this.results.memorySnapshots.length}`);
        console.log(`   Errors: ${this.results.errors.length}`);
        
        if (this.results.errors.length > 0) {
            console.log('\n❌ Errors encountered:');
            this.results.errors.slice(0, 5).forEach(error => {
                console.log(`   ${new Date(error.timestamp).toISOString()}: ${error.error}`);
            });
        }
        
        const finalSnapshot = this.results.memorySnapshots[this.results.memorySnapshots.length - 1];
        if (finalSnapshot) {
            console.log(`\n📊 Final memory usage: ${finalSnapshot.heapUsedMB}MB (${finalSnapshot.heapUsagePercent.toFixed(1)}%)`);
        }
    }
}

// Main execution
if (require.main === module) {
    const tester = new MemoryLeakTester();
    
    // Handle command line arguments
    const args = process.argv.slice(2);
    if (args.includes('--help')) {
        console.log('Usage: node scripts/test-memory-leak-fixes.js [options]');
        console.log('Options:');
        console.log('  --duration <ms>    Test duration in milliseconds (default: 300000)');
        console.log('  --interval <ms>    Request interval in milliseconds (default: 1000)');
        console.log('  --concurrent <n>   Number of concurrent requests (default: 5)');
        console.log('  --help            Show this help message');
        process.exit(0);
    }
    
    // Parse options
    const durationIndex = args.indexOf('--duration');
    if (durationIndex !== -1 && args[durationIndex + 1]) {
        tester.testDuration = parseInt(args[durationIndex + 1]);
    }
    
    const intervalIndex = args.indexOf('--interval');
    if (intervalIndex !== -1 && args[intervalIndex + 1]) {
        tester.requestInterval = parseInt(args[intervalIndex + 1]);
    }
    
    const concurrentIndex = args.indexOf('--concurrent');
    if (concurrentIndex !== -1 && args[concurrentIndex + 1]) {
        tester.maxConcurrentRequests = parseInt(args[concurrentIndex + 1]);
    }
    
    tester.runAllTests().catch(error => {
        console.error('Test failed:', error);
        process.exit(1);
    });
}

module.exports = MemoryLeakTester;
