const axios = require('axios');

class ExchangeRateService {
    constructor() {
        // Cache for exchange rates to avoid excessive API calls
        this.rateCache = new Map();
        this.cacheExpiry = new Map();
        this.cacheDuration = 30 * 60 * 1000; // 30 minutes in milliseconds
        
        // Fallback rate in case all APIs fail
        this.fallbackRate = 0.14; // 1 CNY = 0.14 USD (approximate)
        
        // API configurations (in order of preference)
        this.apiConfigs = [
            {
                name: 'ExchangeRate-API',
                url: 'https://api.exchangerate-api.com/v4/latest/CNY',
                extractRate: (data) => data.rates?.USD,
                free: true
            },
            {
                name: 'Fixer.io',
                url: `https://api.fixer.io/latest?base=CNY&symbols=USD&access_key=${process.env.FIXER_API_KEY}`,
                extractRate: (data) => data.rates?.USD,
                requiresKey: true
            },
            {
                name: 'CurrencyAPI',
                url: `https://api.currencyapi.com/v3/latest?apikey=${process.env.CURRENCY_API_KEY}&base_currency=CNY&currencies=USD`,
                extractRate: (data) => data.data?.USD?.value,
                requiresKey: true
            },
            {
                name: 'ExchangeRatesAPI',
                url: 'https://api.exchangeratesapi.io/v1/latest?base=CNY&symbols=USD&access_key=' + process.env.EXCHANGE_RATES_API_KEY,
                extractRate: (data) => data.rates?.USD,
                requiresKey: true
            }
        ];
    }

    /**
     * Get cached exchange rate if still valid
     * @param {string} cacheKey - Cache key for the rate
     * @returns {number|null} Cached rate or null if expired/not found
     */
    getCachedRate(cacheKey) {
        const expiry = this.cacheExpiry.get(cacheKey);
        if (expiry && Date.now() < expiry) {
            return this.rateCache.get(cacheKey);
        }
        
        // Clean up expired cache entries
        this.rateCache.delete(cacheKey);
        this.cacheExpiry.delete(cacheKey);
        return null;
    }

    /**
     * Cache exchange rate with expiry
     * @param {string} cacheKey - Cache key for the rate
     * @param {number} rate - Exchange rate to cache
     */
    setCachedRate(cacheKey, rate) {
        this.rateCache.set(cacheKey, rate);
        this.cacheExpiry.set(cacheKey, Date.now() + this.cacheDuration);
    }

    /**
     * Fetch exchange rate from a specific API
     * @param {Object} apiConfig - API configuration object
     * @returns {Promise<number|null>} Exchange rate or null if failed
     */
    async fetchFromAPI(apiConfig) {
        try {
            // Skip APIs that require keys if not configured
            if (apiConfig.requiresKey && !this.isAPIKeyConfigured(apiConfig)) {
                console.log(`[ExchangeRate] Skipping ${apiConfig.name} - API key not configured`);
                return null;
            }

            console.log(`[ExchangeRate] Fetching rate from ${apiConfig.name}...`);
            
            const response = await axios.get(apiConfig.url, {
                timeout: 10000, // 10 second timeout
                headers: {
                    'User-Agent': 'eSIM-Platform/1.0'
                }
            });

            const rate = apiConfig.extractRate(response.data);
            
            if (typeof rate === 'number' && rate > 0) {
                console.log(`[ExchangeRate] ✅ Got rate from ${apiConfig.name}: 1 CNY = ${rate} USD`);
                return rate;
            } else {
                console.log(`[ExchangeRate] ❌ Invalid rate from ${apiConfig.name}:`, rate);
                return null;
            }
        } catch (error) {
            console.log(`[ExchangeRate] ❌ Error fetching from ${apiConfig.name}:`, error.message);
            return null;
        }
    }

    /**
     * Check if API key is configured for the given API
     * @param {Object} apiConfig - API configuration object
     * @returns {boolean} True if API key is configured
     */
    isAPIKeyConfigured(apiConfig) {
        if (apiConfig.name === 'Fixer.io') {
            return !!process.env.FIXER_API_KEY;
        }
        if (apiConfig.name === 'CurrencyAPI') {
            return !!process.env.CURRENCY_API_KEY;
        }
        if (apiConfig.name === 'ExchangeRatesAPI') {
            return !!process.env.EXCHANGE_RATES_API_KEY;
        }
        return true; // Free APIs don't need keys
    }

    /**
     * Get current CNY to USD exchange rate
     * @param {boolean} forceRefresh - Force refresh from API even if cached
     * @returns {Promise<number>} Exchange rate (CNY to USD)
     */
    async getCNYToUSDRate(forceRefresh = false) {
        const cacheKey = 'CNY_USD';
        
        // Check cache first unless force refresh is requested
        if (!forceRefresh) {
            const cachedRate = this.getCachedRate(cacheKey);
            if (cachedRate) {
                console.log(`[ExchangeRate] Using cached rate: 1 CNY = ${cachedRate} USD`);
                return cachedRate;
            }
        }

        // Try each API in order of preference
        for (const apiConfig of this.apiConfigs) {
            const rate = await this.fetchFromAPI(apiConfig);
            if (rate) {
                // Cache the successful rate
                this.setCachedRate(cacheKey, rate);
                return rate;
            }
        }

        // If all APIs fail, use fallback rate
        console.warn(`[ExchangeRate] ⚠️  All APIs failed, using fallback rate: 1 CNY = ${this.fallbackRate} USD`);
        return this.fallbackRate;
    }

    /**
     * Convert CNY amount to USD using real-time exchange rate
     * @param {number} cnyAmount - Amount in Chinese Yuan
     * @param {boolean} forceRefresh - Force refresh exchange rate
     * @returns {Promise<number>} Amount in USD
     */
    async convertCNYToUSD(cnyAmount, forceRefresh = false) {
        try {
            const rate = await this.getCNYToUSDRate(forceRefresh);
            const usdAmount = Number((cnyAmount * rate).toFixed(2));
            
            console.log(`[ExchangeRate] Converted ${cnyAmount} CNY → $${usdAmount} USD (rate: ${rate})`);
            return usdAmount;
        } catch (error) {
            console.error('[ExchangeRate] Error converting CNY to USD:', error);
            // Use fallback rate in case of error
            const usdAmount = Number((cnyAmount * this.fallbackRate).toFixed(2));
            console.warn(`[ExchangeRate] Using fallback conversion: ${cnyAmount} CNY → $${usdAmount} USD`);
            return usdAmount;
        }
    }

    /**
     * Get exchange rate information for debugging/monitoring
     * @returns {Object} Exchange rate status information
     */
    async getExchangeRateInfo() {
        const cacheKey = 'CNY_USD';
        const cachedRate = this.getCachedRate(cacheKey);
        const cacheExpiry = this.cacheExpiry.get(cacheKey);
        
        return {
            currentRate: cachedRate || await this.getCNYToUSDRate(),
            isCached: !!cachedRate,
            cacheExpiry: cacheExpiry ? new Date(cacheExpiry).toISOString() : null,
            fallbackRate: this.fallbackRate,
            availableAPIs: this.apiConfigs.map(api => ({
                name: api.name,
                requiresKey: api.requiresKey || false,
                keyConfigured: this.isAPIKeyConfigured(api)
            }))
        };
    }

    /**
     * Clear exchange rate cache (useful for testing or manual refresh)
     */
    clearCache() {
        this.rateCache.clear();
        this.cacheExpiry.clear();
        console.log('[ExchangeRate] Cache cleared');
    }
}

module.exports = new ExchangeRateService();
