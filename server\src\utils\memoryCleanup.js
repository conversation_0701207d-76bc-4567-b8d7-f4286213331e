/**
 * Enhanced Memory cleanup utilities for preventing memory leaks and managing memory pools
 * Includes advanced features for search operations, streaming cleanup, and predictive management
 */

const v8 = require('v8');
const { EventEmitter } = require('events');

class MemoryCleanup extends EventEmitter {
    constructor() {
        super();
        this.objectPools = new Map();
        this.trackedReferences = new WeakSet();
        this.cleanupCallbacks = [];
        this.searchCleanupStrategies = new Map();
        this.memorySnapshots = [];
        this.cleanupHistory = [];
        this.isStreamingCleanup = false;
        this.streamingCleanupQueue = [];
        this.weakReferences = new Map();
        this.memoryPressureThreshold = 0.8; // 80% memory usage triggers aggressive cleanup
        this.lastCleanupTime = 0;
        this.lastLightCleanupTime = 0;
        this.cleanupCooldown = 10000; // 10 seconds between major cleanups
        this.lightCleanupCooldown = 5000; // 5 seconds between light cleanups

        // Initialize search-specific cleanup strategies
        this.initializeSearchCleanupStrategies();

        // Start background monitoring
        this.startBackgroundMonitoring();
    }

    /**
     * Initialize search-specific cleanup strategies
     */
    initializeSearchCleanupStrategies() {
        // Strategy for broad searches (global, premium, etc.)
        this.searchCleanupStrategies.set('broad', {
            priority: 'high',
            aggressiveCleanup: true,
            streamingEnabled: true,
            gcFrequency: 'immediate',
            poolClearThreshold: 0.5,
            callbacks: ['clearQueryResults', 'clearRelevanceScores', 'clearTempCollections']
        });

        // Strategy for specific searches
        this.searchCleanupStrategies.set('specific', {
            priority: 'medium',
            aggressiveCleanup: false,
            streamingEnabled: false,
            gcFrequency: 'delayed',
            poolClearThreshold: 0.8,
            callbacks: ['clearTempCollections']
        });

        // Strategy for no-search operations
        this.searchCleanupStrategies.set('none', {
            priority: 'low',
            aggressiveCleanup: false,
            streamingEnabled: false,
            gcFrequency: 'scheduled',
            poolClearThreshold: 0.9,
            callbacks: []
        });
    }

    /**
     * Start background memory monitoring
     */
    startBackgroundMonitoring() {
        setInterval(() => {
            this.checkMemoryPressure();
            this.processStreamingCleanupQueue();
        }, 30000); // Check every 30 seconds 
    }

    /**
     * Check for memory pressure and trigger proactive cleanup
     */
    checkMemoryPressure() {
        const memUsage = process.memoryUsage();
        const heapUsedMB = memUsage.heapUsed / 1024 / 1024;
        const heapTotalMB = memUsage.heapTotal / 1024 / 1024;

        // Use absolute memory thresholds appropriate for 3.7GB system
        const absoluteThreshold = 800; // 800MB absolute threshold 
        const highThreshold = 1200; // 1.2GB high threshold 

        const memoryPressure = heapUsedMB / heapTotalMB;

        // Trigger cleanup based on absolute memory usage, not percentage
        if (heapUsedMB > highThreshold) {
            console.log(`🔥 High memory usage detected: ${heapUsedMB.toFixed(1)}MB (${(memoryPressure * 100).toFixed(1)}%) - triggering aggressive cleanup`);
            this.proactiveCleanup();
        } else if (heapUsedMB > absoluteThreshold) {
            console.log(`⚠️ Elevated memory usage: ${heapUsedMB.toFixed(1)}MB (${(memoryPressure * 100).toFixed(1)}%) - triggering light cleanup`);
            this.lightCleanup();
        }
    }

    /**
     * Light cleanup for elevated memory usage
     */
    lightCleanup() {
        const now = Date.now();
        if (now - this.lastLightCleanupTime < this.lightCleanupCooldown) {
            return; 
        }

        console.log('🧹 [LIGHT CLEANUP] Starting light memory cleanup');

        this.forceGarbageCollection({ cycles: 1, logDetails: false });

        this.lastLightCleanupTime = now;
        this.emit('lightCleanup', { timestamp: now });
    }

    /**
     * Proactive cleanup when memory pressure is high
     */
    proactiveCleanup() {
        const now = Date.now();
        if (now - this.lastCleanupTime < this.cleanupCooldown) {
            return; 
        }

        console.log('🧹 [PROACTIVE] Starting memory pressure cleanup');

        this.clearLRUPools();

        this.executeCleanupCallbacks();

        this.forceGarbageCollection();

        this.lastCleanupTime = now;
        this.emit('proactiveCleanup', { timestamp: now });
    }

    /**
     * Clear least recently used object pools
     */
    clearLRUPools() {
        const poolsToPartialClear = [];

        for (const [name, pool] of this.objectPools) {
            if (pool.objects.length > pool.maxSize * 0.5) {
                poolsToPartialClear.push({ name, pool });
            }
        }

        poolsToPartialClear.sort((a, b) => (b.pool.objects.length - a.pool.objects.length));

        for (const { name, pool } of poolsToPartialClear.slice(0, 3)) {
            const clearCount = Math.floor(pool.objects.length * 0.5);
            pool.objects.splice(0, clearCount);
            console.log(`   Cleared ${clearCount} objects from pool "${name}"`);
        }
    }

    /**
     * Create an object pool for reusing objects
     */
    createObjectPool(name, factory, resetFn, maxSize = 100) {
        const pool = {
            name,
            factory,
            resetFn,
            maxSize,
            objects: [],
            created: 0,
            reused: 0
        };

        this.objectPools.set(name, pool);
        console.log(`🏊 Created object pool "${name}" with max size ${maxSize}`);
        
        return pool;
    }

    /**
     * Get an object from the pool or create a new one
     */
    getFromPool(poolName) {
        const pool = this.objectPools.get(poolName);
        if (!pool) {
            throw new Error(`Object pool "${poolName}" not found`);
        }

        let obj;
        if (pool.objects.length > 0) {
            obj = pool.objects.pop();
            pool.reused++;
        } else {
            obj = pool.factory();
            pool.created++;
        }

        return obj;
    }

    /**
     * Return an object to the pool
     */
    returnToPool(poolName, obj) {
        const pool = this.objectPools.get(poolName);
        if (!pool) {
            console.warn(`Object pool "${poolName}" not found for return`);
            return;
        }

        if (pool.objects.length < pool.maxSize) {
            if (pool.resetFn) {
                pool.resetFn(obj);
            }
            pool.objects.push(obj);
        }
    }

    /**
     * Get pool statistics
     */
    getPoolStats(poolName) {
        const pool = this.objectPools.get(poolName);
        if (!pool) {
            return null;
        }

        return {
            name: pool.name,
            available: pool.objects.length,
            created: pool.created,
            reused: pool.reused,
            maxSize: pool.maxSize,
            efficiency: pool.reused / (pool.created + pool.reused) * 100
        };
    }

    /**
     * Clear all object pools
     */
    clearAllPools() {
        console.log('🧹 Clearing all object pools...');
        
        for (const [name, pool] of this.objectPools) {
            const cleared = pool.objects.length;
            pool.objects.length = 0; // Clear the array
            console.log(`   Cleared pool "${name}": ${cleared} objects`);
        }
    }

    /**
     * Manually clear object references
     */
    clearObjectReferences(obj, depth = 0, maxDepth = 3) {
        if (!obj || typeof obj !== 'object' || depth > maxDepth) {
            return;
        }

        if (this.trackedReferences.has(obj)) {
            return;
        }
        this.trackedReferences.add(obj);

        try {
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    const value = obj[key];
                    
                    if (Array.isArray(value)) {
                        value.length = 0;
                    } else if (value && typeof value === 'object') {
                        this.clearObjectReferences(value, depth + 1, maxDepth);
                    }
                    
                    obj[key] = null;
                }
            }
        } catch (error) {
            console.warn('Warning during object cleanup:', error.message);
        }
    }

    /**
     * Enhanced large collection cleanup with streaming support
     */
    clearLargeCollections(collections, options = {}) {
        const { streaming = false, batchSize = 1000, strategy = 'immediate' } = options;
        let totalCleared = 0;

        if (streaming) {
            return this.streamingClearCollections(collections, batchSize);
        }

        for (const collection of collections) {
            if (Array.isArray(collection)) {
                totalCleared += collection.length;

                if (strategy === 'safe' && collection.length > 10000) {
                    this.clearArrayInBatches(collection, batchSize);
                } else {
                    collection.length = 0;
                }
            } else if (collection && typeof collection === 'object') {
                const keys = Object.keys(collection);
                totalCleared += keys.length;

                if (strategy === 'safe' && keys.length > 1000) {
                    this.clearObjectInBatches(collection, keys, batchSize);
                } else {
                    for (const key of keys) {
                        delete collection[key];
                    }
                }
            }
        }

        console.log(`🧹 Cleared ${totalCleared} items from large collections`);
        return totalCleared;
    }

    /**
     * Clear array in batches to prevent blocking
     */
    clearArrayInBatches(array, batchSize = 1000) {
        const totalLength = array.length;
        let cleared = 0;

        const clearBatch = () => {
            const endIndex = Math.min(cleared + batchSize, totalLength);
            array.splice(cleared, endIndex - cleared);
            cleared = endIndex;

            if (cleared < totalLength) {
                setImmediate(clearBatch); 
            }
        };

        clearBatch();
    }

    /**
     * Clear object properties in batches
     */
    clearObjectInBatches(obj, keys, batchSize = 1000) {
        let processed = 0;

        const clearBatch = () => {
            const endIndex = Math.min(processed + batchSize, keys.length);

            for (let i = processed; i < endIndex; i++) {
                delete obj[keys[i]];
            }

            processed = endIndex;

            if (processed < keys.length) {
                setImmediate(clearBatch); 
            }
        };

        clearBatch();
    }

    /**
     * Streaming cleanup for very large collections
     */
    streamingClearCollections(collections, batchSize = 1000) {
        this.isStreamingCleanup = true;
        let totalCleared = 0;

        for (const collection of collections) {
            if (Array.isArray(collection)) {
                totalCleared += collection.length;
                this.streamingCleanupQueue.push({
                    type: 'array',
                    collection,
                    batchSize,
                    processed: 0,
                    total: collection.length
                });
            } else if (collection && typeof collection === 'object') {
                const keys = Object.keys(collection);
                totalCleared += keys.length;
                this.streamingCleanupQueue.push({
                    type: 'object',
                    collection,
                    keys,
                    batchSize,
                    processed: 0,
                    total: keys.length
                });
            }
        }

        console.log(`🧹 [STREAMING] Queued ${totalCleared} items for streaming cleanup`);
        return totalCleared;
    }

    /**
     * Process streaming cleanup queue
     */
    processStreamingCleanupQueue() {
        if (!this.isStreamingCleanup || this.streamingCleanupQueue.length === 0) {
            return;
        }

        const item = this.streamingCleanupQueue[0];
        const endIndex = Math.min(item.processed + item.batchSize, item.total);

        if (item.type === 'array') {
            item.collection.splice(item.processed, endIndex - item.processed);
        } else if (item.type === 'object') {
            for (let i = item.processed; i < endIndex; i++) {
                delete item.collection[item.keys[i]];
            }
        }

        item.processed = endIndex;

        if (item.processed >= item.total) {
            this.streamingCleanupQueue.shift();
            console.log(`🧹 [STREAMING] Completed cleanup of ${item.type} with ${item.total} items`);
        }

        if (this.streamingCleanupQueue.length === 0) {
            this.isStreamingCleanup = false;
            console.log('🧹 [STREAMING] All streaming cleanup completed');
            this.emit('streamingCleanupComplete');
        }
    }

    /**
     * Register a cleanup callback
     */
    registerCleanupCallback(name, callback) {
        this.cleanupCallbacks.push({ name, callback });
        console.log(`📝 Registered cleanup callback: ${name}`);
    }

    /**
     * Execute all cleanup callbacks
     */
    executeCleanupCallbacks() {
        console.log('🧹 Executing cleanup callbacks...');
        
        for (const { name, callback } of this.cleanupCallbacks) {
            try {
                console.log(`   Executing: ${name}`);
                callback();
            } catch (error) {
                console.error(`   Error in cleanup callback "${name}":`, error);
            }
        }
    }

    /**
     * Enhanced garbage collection with measurement and optimization
     */
    forceGarbageCollection(options = {}) {
        const { cycles = 1, measureHeapSpaces = false, logDetails = true } = options;

        if (!global.gc) {
            console.warn('⚠️ Garbage collection not available (start with --expose-gc)');
            return null;
        }

        const before = process.memoryUsage();
        let heapSpacesBefore = null;

        if (measureHeapSpaces) {
            heapSpacesBefore = v8.getHeapSpaceStatistics();
        }

        if (logDetails) {
            console.log(`🗑️ Memory before GC: ${(before.heapUsed / 1024 / 1024).toFixed(1)}MB`);
        }

        let totalFreed = 0;

        // Multiple GC cycles for better cleanup
        for (let i = 0; i < cycles; i++) {
            const cycleStart = process.memoryUsage().heapUsed;
            global.gc();
            const cycleEnd = process.memoryUsage().heapUsed;
            const cycleFreed = cycleStart - cycleEnd;
            totalFreed += cycleFreed;

            if (logDetails && cycles > 1) {
                console.log(`🗑️ GC Cycle ${i + 1}/${cycles}: Freed ${(cycleFreed / 1024 / 1024).toFixed(1)}MB`);
            }
        }

        const after = process.memoryUsage();
        const totalFreedMB = totalFreed / 1024 / 1024;

        if (logDetails) {
            console.log(`🗑️ Memory after GC: ${(after.heapUsed / 1024 / 1024).toFixed(1)}MB`);
            console.log(`🗑️ Total memory freed: ${totalFreedMB.toFixed(1)}MB`);
        }

        const result = {
            before: before.heapUsed,
            after: after.heapUsed,
            freed: totalFreed,
            freedMB: totalFreedMB,
            cycles: cycles,
            efficiency: totalFreedMB > 0 ? 'good' : 'poor'
        };

        if (measureHeapSpaces) {
            result.heapSpaces = {
                before: heapSpacesBefore,
                after: v8.getHeapSpaceStatistics()
            };
        }

        // Record cleanup history
        this.cleanupHistory.push({
            timestamp: Date.now(),
            type: 'gc',
            freed: totalFreedMB,
            cycles: cycles
        });

        // Keep only last 50 cleanup records
        if (this.cleanupHistory.length > 50) {
            this.cleanupHistory = this.cleanupHistory.slice(-50);
        }

        this.emit('garbageCollection', result);
        return result;
    }

    /**
     * Emergency memory cleanup
     */
    emergencyCleanup() {
        console.log('🚨 EMERGENCY MEMORY CLEANUP');
        
        this.executeCleanupCallbacks();
        
        this.clearAllPools();
        
        for (let i = 0; i < 3; i++) {
            console.log(`🗑️ Emergency GC cycle ${i + 1}/3`);
            this.forceGarbageCollection();
        }
        
        console.log('🚨 Emergency cleanup completed');
    }

    /**
     * Get memory cleanup recommendations
     */
    getCleanupRecommendations() {
        const memUsage = process.memoryUsage();
        const heapUsedMB = memUsage.heapUsed / 1024 / 1024;
        
        const recommendations = [];
        
        if (heapUsedMB > 500) {
            recommendations.push('Consider clearing large temporary arrays');
            recommendations.push('Execute cleanup callbacks for cached data');
            recommendations.push('Force garbage collection');
        }
        
        if (heapUsedMB > 1000) {
            recommendations.push('URGENT: Clear all object pools');
            recommendations.push('URGENT: Execute emergency cleanup');
            recommendations.push('URGENT: Consider restarting the application');
        }
        
        for (const [name, pool] of this.objectPools) {
            const efficiency = pool.reused / (pool.created + pool.reused) * 100;
            if (efficiency < 50) {
                recommendations.push(`Object pool "${name}" has low efficiency (${efficiency.toFixed(1)}%)`);
            }
        }
        
        return recommendations;
    }

    /**
     * Monitor memory and auto-cleanup
     */
    startAutoCleanup(options = {}) {
        const interval = options.interval || 30000; // 30 seconds
        const threshold = options.threshold || 800; // 800MB
        
        console.log(`🤖 Starting auto-cleanup monitor (threshold: ${threshold}MB, interval: ${interval}ms)`);
        
        const monitor = setInterval(() => {
            const memUsage = process.memoryUsage();
            const heapUsedMB = memUsage.heapUsed / 1024 / 1024;
            
            if (heapUsedMB > threshold) {
                console.log(`🤖 Auto-cleanup triggered at ${heapUsedMB.toFixed(1)}MB`);
                this.executeCleanupCallbacks();
                this.forceGarbageCollection();
            }
        }, interval);
        
        return monitor;
    }

    /**
     * Search-specific cleanup for database query results
     */
    cleanupSearchResults(searchData, searchType = 'broad') {
        const strategy = this.searchCleanupStrategies.get(searchType) || this.searchCleanupStrategies.get('broad');

        console.log(`🔍 [SEARCH CLEANUP] Starting ${searchType} search cleanup`);

        const startTime = Date.now();
        let totalCleared = 0;

        if (searchData.allPlans && Array.isArray(searchData.allPlans)) {
            totalCleared += searchData.allPlans.length;
            if (strategy.streamingEnabled && searchData.allPlans.length > 1000) {
                this.clearLargeCollections([searchData.allPlans], { streaming: true, batchSize: 500 });
            } else {
                searchData.allPlans.length = 0;
            }
        }

        if (searchData.processedPlans && Array.isArray(searchData.processedPlans)) {
            totalCleared += searchData.processedPlans.length;
            searchData.processedPlans.length = 0;
        }

        if (searchData.relevanceScores) {
            const keys = Object.keys(searchData.relevanceScores);
            totalCleared += keys.length;
            for (const key of keys) {
                delete searchData.relevanceScores[key];
            }
        }

        if (searchData.countries) {
            totalCleared += searchData.countries.length;
            searchData.countries.length = 0;
        }

        if (searchData.processedRegions) {
            totalCleared += searchData.processedRegions.length;
            searchData.processedRegions.length = 0;
        }

        if (strategy.gcFrequency === 'immediate' || (strategy.gcFrequency === 'delayed' && totalCleared > 1000)) {
            this.forceGarbageCollection({ cycles: strategy.aggressiveCleanup ? 2 : 1, logDetails: false });
        }

        const duration = Date.now() - startTime;
        console.log(`🔍 [SEARCH CLEANUP] Completed in ${duration}ms, cleared ${totalCleared} items`);

        this.emit('searchCleanup', {
            searchType,
            totalCleared,
            duration,
            strategy: strategy.priority
        });

        return { totalCleared, duration };
    }

    /**
     * Weak reference management for large objects
     */
    trackWeakReference(key, object, cleanupCallback) {
        if (this.weakReferences.has(key)) {
            const existing = this.weakReferences.get(key);
            if (existing.cleanupCallback) {
                existing.cleanupCallback();
            }
        }

        this.weakReferences.set(key, {
            weakRef: new WeakRef(object),
            cleanupCallback,
            timestamp: Date.now()
        });

        console.log(`📎 Tracking weak reference: ${key}`);
    }

    /**
     * Clean up expired weak references
     */
    cleanupWeakReferences() {
        const now = Date.now();
        const expiredKeys = [];

        for (const [key, ref] of this.weakReferences) {
            const obj = ref.weakRef.deref();

            if (!obj || (now - ref.timestamp) > 3600000) {
                if (ref.cleanupCallback) {
                    try {
                        ref.cleanupCallback();
                    } catch (error) {
                        console.warn(`Warning during weak reference cleanup for ${key}:`, error.message);
                    }
                }
                expiredKeys.push(key);
            }
        }

        for (const key of expiredKeys) {
            this.weakReferences.delete(key);
        }

        if (expiredKeys.length > 0) {
            console.log(`🧹 Cleaned up ${expiredKeys.length} expired weak references`);
        }

        return expiredKeys.length;
    }

    /**
     * Memory snapshot for leak detection
     */
    takeMemorySnapshot(label = 'snapshot') {
        const memUsage = process.memoryUsage();
        const heapStats = v8.getHeapStatistics();

        const snapshot = {
            label,
            timestamp: Date.now(),
            memory: {
                rss: memUsage.rss,
                heapTotal: memUsage.heapTotal,
                heapUsed: memUsage.heapUsed,
                external: memUsage.external
            },
            heap: {
                totalHeapSize: heapStats.total_heap_size,
                totalHeapSizeExecutable: heapStats.total_heap_size_executable,
                totalPhysicalSize: heapStats.total_physical_size,
                totalAvailableSize: heapStats.total_available_size,
                usedHeapSize: heapStats.used_heap_size,
                heapSizeLimit: heapStats.heap_size_limit
            },
            objectPools: this.objectPools.size,
            weakReferences: this.weakReferences.size,
            streamingActive: this.isStreamingCleanup
        };

        this.memorySnapshots.push(snapshot);

        if (this.memorySnapshots.length > 20) {
            this.memorySnapshots = this.memorySnapshots.slice(-20);
        }

        return snapshot;
    }

    /**
     * Get detailed memory report with enhanced analytics
     */
    getMemoryReport() {
        const memUsage = process.memoryUsage();
        const poolStats = Array.from(this.objectPools.keys()).map(name => this.getPoolStats(name));
        const heapStats = v8.getHeapStatistics();

        return {
            memoryUsage: {
                rss: (memUsage.rss / 1024 / 1024).toFixed(1) + 'MB',
                heapTotal: (memUsage.heapTotal / 1024 / 1024).toFixed(1) + 'MB',
                heapUsed: (memUsage.heapUsed / 1024 / 1024).toFixed(1) + 'MB',
                external: (memUsage.external / 1024 / 1024).toFixed(1) + 'MB',
                heapUtilization: ((memUsage.heapUsed / memUsage.heapTotal) * 100).toFixed(1) + '%'
            },
            heapStatistics: {
                totalHeapSize: (heapStats.total_heap_size / 1024 / 1024).toFixed(1) + 'MB',
                usedHeapSize: (heapStats.used_heap_size / 1024 / 1024).toFixed(1) + 'MB',
                heapSizeLimit: (heapStats.heap_size_limit / 1024 / 1024).toFixed(1) + 'MB',
                totalAvailableSize: (heapStats.total_available_size / 1024 / 1024).toFixed(1) + 'MB'
            },
            objectPools: poolStats,
            cleanupCallbacks: this.cleanupCallbacks.length,
            weakReferences: this.weakReferences.size,
            streamingCleanup: {
                active: this.isStreamingCleanup,
                queueLength: this.streamingCleanupQueue.length
            },
            cleanupHistory: this.cleanupHistory.slice(-10), // Last 10 cleanups
            memorySnapshots: this.memorySnapshots.length,
            recommendations: this.getCleanupRecommendations()
        };
    }
}

module.exports = new MemoryCleanup();
