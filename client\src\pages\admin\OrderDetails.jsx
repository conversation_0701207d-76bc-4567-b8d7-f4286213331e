import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import api from '@/lib/axios';
import { useToast } from '@/components/ui/use-toast';
import {
    Card,
    CardHeader,
    CardTitle,
    CardContent,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Loader2, ArrowLeft, Copy, CheckCircle, Clock, CreditCard, CirclePlus , Smartphone, Wifi, QrCode, Phone, Shield, Globe, Calendar, Hash, Package, User, Mail, Download, RefreshCw, Bell, AlertTriangle } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";

const OrderDetails = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const { toast } = useToast();
    const [order, setOrder] = useState(null);
    const [loading, setLoading] = useState(true);
    const [pollingInterval, setPollingInterval] = useState(null);
    const [pollingAttempts, setPollingAttempts] = useState(0);
    const [isPollingTimedOut, setIsPollingTimedOut] = useState(false);
    const [notifications, setNotifications] = useState([]);
    const [isComponentMounted, setIsComponentMounted] = useState(true);
    const [usageData, setUsageData] = useState(null);
    const [loadingUsage, setLoadingUsage] = useState(false);

    // Helper functions to manage polling timeout state in localStorage
    const getPollingTimeoutKey = (orderId) => `polling_timeout_${orderId}`;

    const isOrderPollingTimedOut = useCallback((orderId) => {
        const timeoutData = localStorage.getItem(getPollingTimeoutKey(orderId));
        if (!timeoutData) return false;

        const { timestamp } = JSON.parse(timeoutData);
        const now = Date.now();
        const timeoutDuration = 30 * 60 * 1000; // 30 minutes

        // If more than 30 minutes have passed since timeout, allow polling again
        if (now - timestamp > timeoutDuration) {
            localStorage.removeItem(getPollingTimeoutKey(orderId));
            return false;
        }

        return true;
    }, []);

    const markOrderPollingTimedOut = useCallback((orderId) => {
        const timeoutData = {
            timestamp: Date.now(),
            orderId: orderId
        };
        localStorage.setItem(getPollingTimeoutKey(orderId), JSON.stringify(timeoutData));
    }, []);

    const clearOrderPollingTimeout = useCallback((orderId) => {
        localStorage.removeItem(getPollingTimeoutKey(orderId));
    }, []);

    const formatBytes = (bytes) => {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
    };

    const fetchUsageData = useCallback(async () => {
        try {
            setLoadingUsage(true);
            const response = await api.get(`/api/orders/${id}/usage`);

            // Validate the response data structure
            if (response.data && typeof response.data === 'object') {
                setUsageData(response.data);
            } else {
                console.error('Invalid usage data format:', response.data);
                toast({
                    variant: "destructive",
                    title: "Error",
                    description: "Invalid usage data format received"
                });
            }
        } catch (error) {
            console.error('Error fetching usage data:', error);
            toast({
                variant: "destructive",
                title: "Error",
                description: error.response?.data?.message || "Failed to fetch usage data"
            });
        } finally {
            setLoadingUsage(false);
        }
    }, [id, toast]);

    const copyToClipboard = async (text, label) => {
        try {
            await navigator.clipboard.writeText(text);
            toast({
                title: "Copied!",
                description: `${label} copied to clipboard`,
            });
        } catch (err) {
            toast({
                variant: "destructive",
                title: "Failed to copy",
                description: "Could not copy to clipboard",
            });
        }
    };

    const getStatusColor = (status) => {
        switch (status?.toLowerCase()) {
            case 'completed':
            case 'activated':
                return 'bg-green-100 text-green-800 border-green-200';
            case 'pending':
                return 'bg-yellow-100 text-yellow-800 border-yellow-200';
            case 'failed':
            case 'expired':
                return 'bg-red-100 text-red-800 border-red-200';
            default:
                return 'bg-gray-100 text-gray-800 border-gray-200';
        }
    };

    const downloadQRCode = async (qrCodeData, orderId) => {
        try {
            // Create a link element and trigger download
            const link = document.createElement('a');
            link.href = qrCodeData.startsWith('data:') ? qrCodeData : `data:image/png;base64,${qrCodeData}`;
            link.download = `eSIM-QR-Code-Order-${orderId}.png`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            toast({
                title: "Downloaded!",
                description: "QR Code has been downloaded successfully",
            });
        } catch (err) {
            toast({
                variant: "destructive",
                title: "Download failed",
                description: "Could not download QR code",
            });
        }
    };

    // Function to start polling for pending BillionConnect orders
    const startPolling = useCallback(() => {
        if (pollingInterval) return; // Already polling

        // Check if this order has already timed out
        if (isOrderPollingTimedOut(id)) {
            setIsPollingTimedOut(true);
            return; // Don't start polling for timed out orders
        }

        const interval = setInterval(async () => {
            try {
                // Check if component is still mounted before proceeding
                if (!isComponentMounted) {
                    clearInterval(interval);
                    setPollingInterval(null);
                    setPollingAttempts(0);
                    return;
                }

                // Increment attempt counter first
                setPollingAttempts(prev => {
                    const newAttempts = prev + 1;

                    // Stop polling after 30 attempts (5 minutes with 10-second intervals)
                    if (newAttempts > 30) {
                        clearInterval(interval);
                        setPollingInterval(null);
                        setPollingAttempts(0);
                        setIsPollingTimedOut(true);
                        markOrderPollingTimedOut(id);
                        toast({
                            variant: "destructive",
                            title: "Polling Timeout",
                            description: "Order is still pending. Automatic checking has stopped. Please refresh manually or wait for email notification when order completes.",
                        });
                        return 0; // Reset attempts
                    }

                    return newAttempts;
                });

                const response = await api.get(`/api/orders/admin/${id}`);
                const updatedOrder = response.data;

                // Check again if component is still mounted before updating state
                if (!isComponentMounted) {
                    clearInterval(interval);
                    return;
                }

                // Stop polling if order is not pending (covers all non-pending states)
                if (updatedOrder.status !== 'pending') {
                    // console.log('Order status changed to:', updatedOrder.status);
                    clearInterval(interval);
                    setPollingInterval(null);
                    setPollingAttempts(0);
                    setIsPollingTimedOut(false);
                    clearOrderPollingTimeout(id);
                    setOrder(updatedOrder);

                    // Only show success notification if it changed from pending to completed
                    if (updatedOrder.status === 'completed') {
                        toast({
                            title: "Order Completed!",
                            description: "The eSIM order has been processed successfully. The page will refresh to show the latest data.",
                        });

                        // Refresh the page after a short delay to show the completed order
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000);
                    }

                    return;
                }

                // Update order data
                setOrder(updatedOrder);

            } catch (error) {
                console.error('Error polling order status:', error);
                // Continue polling even if there's an error, but still respect the attempt limit
            }
        }, 10000); // Poll every 10 seconds

        setPollingInterval(interval);
    }, [pollingInterval, isComponentMounted, id, toast, isOrderPollingTimedOut, markOrderPollingTimedOut, clearOrderPollingTimeout]);

    // Function to stop polling
    const stopPolling = useCallback(() => {
        if (pollingInterval) {
            clearInterval(pollingInterval);
            setPollingInterval(null);
            setPollingAttempts(0);
        }
    }, [pollingInterval]);

    useEffect(() => {
        setIsComponentMounted(true);

        const fetchOrderDetails = async () => {
            try {
                setLoading(true);
                const response = await api.get(`/api/orders/admin/${id}`);
                const orderData = response.data;
                setOrder(orderData);

                // If it's a provider that supports usage data, fetch it automatically
                const providerName = orderData.plan?.provider?.name;
                if ((providerName === 'Mobimatter' || providerName === 'Billionconnect') && orderData.status === 'completed') {
                    fetchUsageData();
                }

                // Check timeout state and start polling for pending BillionConnect orders
                if (orderData.status === 'pending' && providerName === 'Billionconnect') {
                    console.log('Starting polling for pending BillionConnect order:', id);
                    if (isOrderPollingTimedOut(id)) {
                        setIsPollingTimedOut(true);
                    } else {
                        startPolling();
                    }
                } else if (orderData.status !== 'pending') {
                    console.log('Order is not pending, skipping polling. Status:', orderData.status);
                }
            } catch (error) {
                // console.error('Error fetching order details:', error);
                toast({
                    variant: "destructive",
                    title: "Error",
                    description: error.response?.data?.message || "Failed to fetch order details"
                });
                navigate('/admin/orders');
            } finally {
                setLoading(false);
            }
        };

        if (id) {
            fetchOrderDetails();
        }

        // Cleanup polling on unmount
        return () => {
            setIsComponentMounted(false);
            stopPolling();
        };
    }, [id, isOrderPollingTimedOut, startPolling, stopPolling, toast, navigate, fetchUsageData]);

    // Cleanup polling when order status changes
    useEffect(() => {
        if (order?.status !== 'pending') {
            stopPolling();
        }
    }, [order?.status, stopPolling]);

    const fetchNotifications = useCallback(async () => {
        if (!order?.externalOrderId) return;
        try {
            const response = await api.get(`/api/admin/notifications?orderId=${order.externalOrderId}&limit=5`);
            setNotifications(response.data.data.notifications || []);
        } catch (error) {
            console.error('Failed to fetch notifications:', error);
        }
    }, [order?.externalOrderId, id]);

    // Fetch notifications when order is loaded
    useEffect(() => {
        if (order?.externalOrderId) {
            fetchNotifications();
        }
    }, [order?.externalOrderId, fetchNotifications]);

    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-100">
                <div className="container mx-auto p-6">
                    <div className="flex items-center justify-center min-h-[60vh]">
                        <div className="text-center">
                            <div className="relative">
                                <div className="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
                                    <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                                </div>
                                <div className="absolute inset-0 w-16 h-16 mx-auto bg-blue-200 rounded-full animate-ping opacity-20"></div>
                            </div>
                            <h3 className="text-lg font-semibold text-gray-700 mb-2">Loading Order Details</h3>
                            <p className="text-gray-500">Please wait while we fetch the order information...</p>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    if (!order) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-100">
                <div className="container mx-auto p-6">
                    <div className="flex items-center justify-center min-h-[60vh]">
                        <div className="text-center">
                            <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
                                <Package className="h-8 w-8 text-red-600" />
                            </div>
                            <h2 className="text-2xl font-bold text-gray-800 mb-2">Order Not Found</h2>
                            <p className="text-gray-600 mb-6">The order you're looking for doesn't exist or has been removed.</p>
                            <Button
                                onClick={() => navigate('/admin/orders')}
                                className="bg-blue-600 hover:bg-blue-700"
                            >
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Orders
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-100">
            <div className="container mx-auto p-6">
                {/* Header Section */}
                <div className="flex items-center justify-between mb-8">
                    <div className="flex items-center gap-4">
                        <Button
                            variant="outline"
                            onClick={() => navigate('/admin/orders')}
                            className="flex items-center gap-2 bg-black text-white border-gray-200 shadow-sm"
                        >
                            <ArrowLeft className="h-4 w-4" />
                            Back to Orders
                        </Button>
                        <div className="hidden md:block">
                            <h1 className="text-2xl font-bold text-gray-900">Order Details</h1>
                            <p className="text-gray-600">Admin view of order information</p>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <Badge className={`px-3 py-1 text-sm font-medium border ${getStatusColor(order.status)}`}>
                            {order.status === 'completed' && <CheckCircle className="h-3 w-3 mr-1" />}
                            {order.status === 'pending' && <Clock className="h-3 w-3 mr-1" />}
                            <span className="capitalize">{order.status}</span>
                        </Badge>
                    </div>
                </div>

                {/* Pending Order Notification for BillionConnect */}
                {order.status === 'pending' && order.plan?.provider?.name === 'Billionconnect' && (
                    <div className="mb-6">
                        <Card className="border-l-4 border-l-yellow-500 bg-yellow-50 border-yellow-200">
                            <CardContent className="p-4">
                                <div className="flex items-start gap-3">
                                    <div className="flex-shrink-0">
                                        <Clock className="h-5 w-5 text-yellow-600 mt-0.5" />
                                    </div>
                                    <div className="flex-1">
                                        <h3 className="text-sm font-medium text-yellow-800 mb-1">
                                            {isPollingTimedOut ? "Order Still Processing" : "Order Processing"}
                                        </h3>
                                        <p className="text-sm text-yellow-700 mb-2">
                                            {isPollingTimedOut ? (
                                                "This BillionConnect order is still being processed. Automatic checking has stopped after 5 minutes. The system will send email notifications when the order completes, or you can refresh manually to check for updates."
                                            ) : (
                                                "This BillionConnect order is being processed and will take 1-5 minutes to complete. The eSIM data will be available automatically once processing is finished."
                                            )}
                                        </p>
                                        <div className="flex items-center gap-2 text-xs text-yellow-600">
                                            <RefreshCw className={`h-3 w-3 ${pollingInterval ? 'animate-spin' : ''}`} />
                                            {isPollingTimedOut ? (
                                                <span>Automatic checking stopped - refresh manually to check</span>
                                            ) : pollingInterval ? (
                                                <span>Checking for updates... (Attempt {pollingAttempts}/30)</span>
                                            ) : (
                                                <span>Monitoring stopped</span>
                                            )}
                                        </div>
                                    </div>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => window.location.reload()}
                                        className="text-yellow-700 border-yellow-300 hover:bg-yellow-100"
                                    >
                                        <RefreshCw className="h-3 w-3 mr-1" />
                                        Refresh
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Main Content */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Order Details Card */}
                    <div className="lg:col-span-2">
                        <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                            <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-t-lg">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-white/20 rounded-lg">
                                        <Hash className="h-5 w-5" />
                                    </div>
                                    <div>
                                        <CardTitle className="text-xl">Order #{order.id}</CardTitle>
                                        <p className="text-blue-100 text-sm">
                                            Placed on {format(new Date(order.createdAt), 'MMMM d, yyyy h:mm a')}
                                        </p>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent className="p-6 space-y-6">
                                {/* Order Information Grid */}
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div className="space-y-4">
                                        <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                            <User className="h-5 w-5 text-blue-600 mt-0.5" />
                                            <div className="flex-1">
                                                <h3 className="text-sm font-medium text-gray-700">Customer</h3>
                                                <div className="flex items-center gap-2 mt-1">
                                                    <p className="font-semibold text-gray-900">{order.customer}</p>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => copyToClipboard(order.customer, 'Customer name')}
                                                        className="h-6 w-6 p-0 bg-blue-600 border border-blue-600"
                                                    >
                                                        <Copy className="h-3 w-3" />
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                            <Mail className="h-5 w-5 text-blue-600 mt-0.5" />
                                            <div className="flex-1">
                                                <h3 className="text-sm font-medium text-gray-700">Customer Email</h3>
                                                <div className="flex items-center gap-2 mt-1">
                                                    <p className="font-semibold text-gray-900">{order.customerEmail}</p>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => copyToClipboard(order.customerEmail, 'Customer email')}
                                                        className="h-6 w-6 p-0 bg-blue-600 border border-blue-600"
                                                    >
                                                        <Copy className="h-3 w-3" />
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                            <Package className="h-5 w-5 text-blue-600 mt-0.5" />
                                            <div className="flex-1">
                                                <h3 className="text-sm font-medium text-gray-700">Plan Name</h3>
                                                <p className="mt-1 font-semibold text-gray-900">{order.plan.name}</p>
                                            </div>
                                        </div>

                                        <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                            <CreditCard className="h-5 w-5 text-blue-600 mt-0.5" />
                                            <div className="flex-1">
                                                <h3 className="text-sm font-medium text-gray-700">Order Total</h3>
                                                <p className="mt-1 font-semibold text-gray-900 text-lg">${order.orderTotal}</p>
                                            </div>
                                        </div>

                                        <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                            <CirclePlus  className="h-5 w-5 text-blue-600 mt-0.5" />
                                            <div className="flex-1">
                                                <h3 className="text-sm font-medium text-gray-700">Quantity</h3>
                                                <p className="mt-1 font-semibold text-gray-900">{order.quantity}</p>
                                            </div>
                                        </div>

                                        {order.startDate && (
                                            <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                                <Calendar className="h-5 w-5 text-blue-600 mt-0.5" />
                                                <div className="flex-1">
                                                    <h3 className="text-sm font-medium text-gray-700">Start Date</h3>
                                                    <p className="mt-1 font-semibold text-gray-900">{format(new Date(order.startDate), 'MMMM d, yyyy')}</p>
                                                </div>
                                            </div>
                                        )}

                                        {order.validTime && order.plan?.provider?.name === 'Billionconnect' && (
                                            <div className="flex items-start gap-3 p-4 bg-orange-50 border border-orange-200 rounded-lg">
                                                <Clock className="h-5 w-5 text-orange-600 mt-0.5" />
                                                <div className="flex-1">
                                                    <h3 className="text-sm font-medium text-orange-700">Activate Before</h3>
                                                    <p className="mt-1 font-semibold text-orange-900">{format(new Date(order.validTime), 'MMMM d, yyyy h:mm a')}</p>
                                                </div>
                                            </div>
                                        )}

                                        <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                            <Shield className="h-5 w-5 text-blue-600 mt-0.5" />
                                            <div className="flex-1">
                                                <h3 className="text-sm font-medium text-gray-700">Activation Policy</h3>
                                                <p className="mt-1 font-semibold text-gray-900">{order.plan.activationPolicy}</p>
                                            </div>
                                        </div>

                                        {order.stock?.walletAuthTransactionId && (
                                            <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                                <Hash className="h-5 w-5 text-blue-600 mt-0.5" />
                                                <div className="flex-1">
                                                    <h3 className="text-sm font-medium text-gray-700">Wallet Auth Transaction ID</h3>
                                                    <div className="flex items-center gap-2 mt-1">
                                                        <p className="font-mono text-sm text-gray-900 truncate">{order.stock.walletAuthTransactionId}</p>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => copyToClipboard(order.stock.walletAuthTransactionId, 'Transaction ID')}
                                                            className="h-6 w-6 p-0 bg-blue-600 border border-blue-600"
                                                        >
                                                            <Copy className="h-3 w-3" />
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                    <div className="space-y-4">
                                        <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                            <Smartphone className="h-5 w-5 text-blue-600 mt-0.5" />
                                            <div className="flex-1">
                                                <h3 className="text-sm font-medium text-gray-700">ICC ID</h3>
                                                <div className="flex items-center gap-2 mt-1">
                                                    <p className="font-mono text-sm text-gray-900">{order.iccid || 'N/A'}</p>
                                                    {order.iccid && (
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => copyToClipboard(order.iccid, 'ICC ID')}
                                                            className="h-6 w-6 p-0 bg-blue-600 border border-blue-600"
                                                        >
                                                            <Copy className="h-3 w-3" />
                                                        </Button>
                                                    )}
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                            <Globe className="h-5 w-5 text-blue-600 mt-0.5" />
                                            <div className="flex-1">
                                                <h3 className="text-sm font-medium text-gray-700">SMDP Address</h3>
                                                <div className="flex items-center gap-2 mt-1">
                                                    <p className="font-mono text-sm text-gray-900">{order.smdpAddress || 'N/A'}</p>
                                                    {order.smdpAddress && (
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => copyToClipboard(order.smdpAddress, 'SMDP Address')}
                                                            className="h-6 w-6 p-0 bg-blue-600 border border-blue-600"
                                                        >
                                                            <Copy className="h-3 w-3" />
                                                        </Button>
                                                    )}
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                            <Wifi className="h-5 w-5 text-blue-600 mt-0.5" />
                                            <div className="flex-1">
                                                <h3 className="text-sm font-medium text-gray-700">Local Profile Assistant (LPA)</h3>
                                                <div className="flex items-start gap-2 mt-1">
                                                    <p className="font-mono text-xs text-gray-900 break-all flex-1">{order.lpaString || 'N/A'}</p>
                                                    {order.lpaString && (
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => copyToClipboard(order.lpaString, 'LPA String')}
                                                            className="h-6 w-6 p-0 bg-blue-600 border border-blue-600"
                                                        >
                                                            <Copy className="h-3 w-3" />
                                                        </Button>
                                                    )}
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                            <Globe className="h-5 w-5 text-blue-600 mt-0.5" />
                                            <div className="flex-1">
                                                <h3 className="text-sm font-medium text-gray-700">Access Point Name</h3>
                                                <div className="flex items-center gap-2 mt-1">
                                                    <p className="font-mono text-sm text-gray-900">{order.accessPointName || 'N/A'}</p>
                                                    {order.accessPointName && (
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => copyToClipboard(order.accessPointName, 'APN')}
                                                            className="h-6 w-6 p-0 bg-blue-600 border border-blue-600"
                                                        >
                                                            <Copy className="h-3 w-3" />
                                                        </Button>
                                                    )}
                                                </div>
                                            </div>
                                        </div>

                                        {order.phoneNumber && order.phoneNumber !== 'N/A' && (
                                            <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                                <Phone className="h-5 w-5 text-blue-600 mt-0.5" />
                                                <div className="flex-1">
                                                    <h3 className="text-sm font-medium text-gray-700">Phone Number</h3>
                                                    <div className="flex items-center gap-2 mt-1">
                                                        <p className="font-mono text-sm text-gray-900">{order.phoneNumber}</p>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => copyToClipboard(order.phoneNumber, 'Phone Number')}
                                                            className="h-6 w-6 p-0 bg-blue-600 border border-blue-600"
                                                        >
                                                            <Copy className="h-3 w-3" />
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        {order.activationCode && (
                                            <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                                <Shield className="h-5 w-5 text-blue-600 mt-0.5" />
                                                <div className="flex-1">
                                                    <h3 className="text-sm font-medium text-gray-700">Activation Code</h3>
                                                    <div className="flex items-center gap-2 mt-1">
                                                        <p className="font-mono text-sm text-gray-900">{order.activationCode}</p>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => copyToClipboard(order.activationCode, 'Activation Code')}
                                                            className="h-6 w-6 p-0 bg-blue-600 border border-blue-600"
                                                        >
                                                            <Copy className="h-3 w-3" />
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        {order.confCode && (
                                            <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                                <Hash className="h-5 w-5 text-blue-600 mt-0.5" />
                                                <div className="flex-1">
                                                    <h3 className="text-sm font-medium text-gray-700">Confirmation Code</h3>
                                                    <div className="flex items-center gap-2 mt-1">
                                                        <p className="font-mono text-sm text-gray-900">{order.confCode}</p>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => copyToClipboard(order.confCode, 'Confirmation Code')}
                                                            className="h-6 w-6 p-0 bg-blue-600 border border-blue-600"
                                                        >
                                                            <Copy className="h-3 w-3" />
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Notification Status Card */}
                        {notifications.length > 0 && (
                            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                                <CardHeader className="bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-t-lg">
                                    <div className="flex items-center gap-3">
                                        <div className="p-2 bg-white/20 rounded-lg">
                                            <Bell className="h-5 w-5" />
                                        </div>
                                        <div>
                                            <CardTitle className="text-lg">Webhook Notifications</CardTitle>
                                            <p className="text-white/80 text-sm">
                                                BC notification processing status
                                            </p>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent className="p-6">
                                    <div className="space-y-3">
                                        {notifications.map((notification) => (
                                            <div key={notification.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                                <div className="flex items-center gap-3">
                                                    <div className="flex-shrink-0">
                                                        {notification.status === 'completed' && <CheckCircle className="h-4 w-4 text-green-600" />}
                                                        {notification.status === 'pending' && <Clock className="h-4 w-4 text-yellow-600" />}
                                                        {notification.status === 'failed' && <AlertTriangle className="h-4 w-4 text-red-600" />}
                                                        {notification.status === 'processing' && <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />}
                                                    </div>
                                                    <div>
                                                        <p className="text-sm font-medium text-gray-900">
                                                            {notification.notificationType}
                                                        </p>
                                                        <p className="text-xs text-gray-500">
                                                            {format(new Date(notification.receivedAt), 'MMM dd, HH:mm')}
                                                        </p>
                                                    </div>
                                                </div>
                                                <Badge className={`text-xs ${
                                                    notification.status === 'completed' ? 'bg-green-100 text-green-800' :
                                                    notification.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                                    notification.status === 'failed' ? 'bg-red-100 text-red-800' :
                                                    'bg-blue-100 text-blue-800'
                                                }`}>
                                                    {notification.status}
                                                </Badge>
                                            </div>
                                        ))}
                                    </div>
                                    {notifications.some(n => n.status === 'failed') && (
                                        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                                            <div className="flex items-start gap-2">
                                                <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5" />
                                                <div>
                                                    <p className="text-sm font-medium text-red-800">Processing Issues Detected</p>
                                                    <p className="text-xs text-red-600 mt-1">
                                                        Some notifications failed to process. Check the notifications page for details.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        )}
                    </div>

                    {/* Sidebar */}
                    <div className="lg:col-span-1 space-y-6">
                        {/* QR Code Card */}
                        {(order.lpaString || order.qrCode) && (
                            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                                <CardHeader className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-t-lg">
                                    <div className="flex items-center gap-3">
                                        <div className="p-2 bg-white/20 rounded-lg">
                                            <QrCode className="h-5 w-5" />
                                        </div>
                                        <div>
                                            <CardTitle className="text-lg">eSIM QR Code</CardTitle>
                                            <p className="text-indigo-100 text-sm">Customer activation code</p>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent className="p-6">
                                    <div className="text-center space-y-4">
                                        <div className="bg-white p-4 rounded-xl shadow-inner border-2 border-gray-100">
                                            <img
                                                src={order.qrCode?.startsWith('data:') ? order.qrCode : `data:image/png;base64,${order.qrCode}`}
                                                alt="eSIM QR Code"
                                                className="w-full max-w-[250px] mx-auto"
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Button
                                                onClick={() => downloadQRCode(order.qrCode, order.id)}
                                                className="w-full bg-indigo-600 hover:bg-indigo-700 text-white"
                                            >
                                                <Download className="h-4 w-4 mr-2" />
                                                Download QR Code
                                            </Button>

                                            <p className="text-xs text-gray-500">
                                                Download for customer or support purposes
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </div>
                </div>

                {/* Usage Data Section */}
                <div className="mt-8">
                    <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                        <CardHeader className="bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-t-lg">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-white/20 rounded-lg">
                                        <Wifi className="h-5 w-5" />
                                    </div>
                                    <div>
                                        <CardTitle className="text-lg">Usage Data</CardTitle>
                                        <p className="text-purple-100 text-sm">Real-time data consumption tracking</p>
                                    </div>
                                </div>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={fetchUsageData}
                                    disabled={loadingUsage}
                                    className="bg-white/20 border-white/30 text-white hover:bg-white/30"
                                >
                                    {loadingUsage ? (
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                    ) : (
                                        <RefreshCw className="h-4 w-4" />
                                    )}
                                    <span className="ml-2">Refresh</span>
                                </Button>
                            </div>
                        </CardHeader>
                        <CardContent className="p-6">
                            {loadingUsage ? (
                                <div className="flex items-center justify-center py-12">
                                    <div className="text-center">
                                        <div className="w-12 h-12 mx-auto mb-4 bg-purple-100 rounded-full flex items-center justify-center">
                                            <Loader2 className="h-6 w-6 animate-spin text-purple-600" />
                                        </div>
                                        <p className="text-gray-600">Loading usage data...</p>
                                    </div>
                                </div>
                            ) : usageData ? (
                                <div className="space-y-6">
                                    <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6">
                                        <Table>
                                            <TableHeader>
                                                <TableRow className="border-gray-200">
                                                    <TableHead className="font-semibold text-gray-700">Type</TableHead>
                                                    <TableHead className="font-semibold text-gray-700">Used</TableHead>
                                                    <TableHead className="font-semibold text-gray-700">Data Left</TableHead>
                                                    <TableHead className="font-semibold text-gray-700">Total</TableHead>
                                                    <TableHead className="font-semibold text-gray-700">Status</TableHead>
                                                </TableRow>
                                            </TableHeader>
                                            <TableBody>
                                                <TableRow className="border-gray-200">
                                                    <TableCell className="font-medium">
                                                        <div className="flex items-center gap-2">
                                                            <Wifi className="h-4 w-4 text-blue-600" />
                                                            Data
                                                        </div>
                                                    </TableCell>
                                                    <TableCell className="font-mono text-sm">
                                                        {usageData.dataUsage ? formatBytes(usageData.dataUsage) : 'N/A'}
                                                    </TableCell>
                                                    <TableCell className="font-mono text-sm">
                                                        {usageData.dataUsage && usageData.dataAllowance ?
                                                            formatBytes(Math.max(0, usageData.dataAllowance - usageData.dataUsage))
                                                            : 'N/A'
                                                        }
                                                    </TableCell>
                                                    <TableCell className="font-mono text-sm">
                                                        {usageData.dataAllowance ? formatBytes(usageData.dataAllowance) : 'N/A'}
                                                    </TableCell>
                                                    <TableCell>
                                                        <Badge className={`border`}>
                                                            {usageData.status || 'N/A'}
                                                        </Badge>
                                                    </TableCell>
                                                </TableRow>
                                            </TableBody>
                                        </Table>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        {usageData.activationDate && (
                                            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                                <div className="flex items-center gap-2 text-green-800 font-medium mb-1">
                                                    <CheckCircle className="h-4 w-4" />
                                                    Activation Date
                                                </div>
                                                <p className="text-green-700 text-sm">{format(new Date(usageData.activationDate), 'MMM d, yyyy h:mm a')}</p>
                                            </div>
                                        )}
                                        {usageData.expiryDate && (
                                            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                                                <div className="flex items-center gap-2 text-orange-800 font-medium mb-1">
                                                    <Clock className="h-4 w-4" />
                                                    Expiry Date
                                                </div>
                                                <p className="text-orange-700 text-sm">{format(new Date(usageData.expiryDate), 'MMM d, yyyy h:mm a')}</p>
                                            </div>
                                        )}
                                        {usageData.lastUpdateTime && (
                                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                                <div className="flex items-center gap-2 text-blue-800 font-medium mb-1">
                                                    <RefreshCw className="h-4 w-4" />
                                                    Last Updated
                                                </div>
                                                <p className="text-blue-700 text-sm">{format(new Date(usageData.lastUpdateTime), 'MMM d, yyyy h:mm a')}</p>
                                            </div>
                                        )}
                                    </div>

                                    {usageData.message && (
                                        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                                            <p className="text-gray-700 italic text-sm">{usageData.message}</p>
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <div className="text-center py-12">
                                    <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                                        <Wifi className="h-8 w-8 text-gray-400" />
                                    </div>
                                    <h3 className="text-lg font-medium text-gray-700 mb-2">No Usage Data Available</h3>
                                    <p className="text-gray-500 text-sm">
                                        Data usage tracking is not available for this plan at the moment.
                                    </p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    );
};

export default OrderDetails; 