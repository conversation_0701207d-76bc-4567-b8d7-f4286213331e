const cron = require('node-cron');
const { Provider, Country } = require('../models');
const providerFactory = require('./provider.factory');
const { EsimPlan } = require('../models');
const { Op } = require('sequelize');
const { v4: uuidv4 } = require('uuid');
const memoryMonitor = require('../utils/memoryMonitor');

class CronService {
    constructor() {
        // Schedule plan sync to run every 4 hours
        // Cron format: Minute Hour Day Month Day_of_week
        // '0 */4 * * *' means: At minute 0 past every 4th hour
        this.planSyncJob = cron.schedule('0 */4 * * *', async () => {
            console.log('Starting automatic plan sync...');
            await this.syncExternalPlans();
        }, {
            scheduled: true,
            timezone: "UTC"
        });
    }
    
    async syncExternalPlans() {
        try {
            // Check memory before starting sync
            console.log('[Cron] Starting plan sync - checking memory status...');
            memoryMonitor.logMemoryStatus();

            if (!memoryMonitor.isSafeToProcess()) {
                console.log('[Cron] ⚠️ Memory usage too high, waiting for memory to be available...');
                try {
                    await memoryMonitor.waitForMemory(30000); // Wait up to 30 seconds
                } catch (error) {
                    console.error('[Cron] ❌ Plan sync aborted due to memory constraints:', error.message);
                    return {
                        total: 0,
                        updated: 0,
                        created: 0,
                        failed: 0,
                        errors: [{ error: 'Sync aborted due to memory constraints' }]
                    };
                }
            }

            // Get all active external providers
            const providers = await Provider.findAll({
                where: {
                    type: 'API',
                    status: 'active'
                }
            });

            const results = {
                total: 0,
                updated: 0,
                created: 0,
                failed: 0,
                errors: []
            };

            // Get all countries for mapping (needed for country associations)
            const allCountries = await Country.findAll({
                where: { isActive: true },
                attributes: ['id', 'name', 'iso3']
            });

            // Create a map of country names and ISO3 codes to IDs for quick lookup
            const countryMap = new Map();
            allCountries.forEach(country => {
                countryMap.set(country.name.toLowerCase(), country);
                countryMap.set(country.iso3.toLowerCase(), country);
                countryMap.set(country.id.toLowerCase(), country);
            });

            console.log(`[Cron] Loaded ${allCountries.length} countries for mapping`);

            // Categories to sync
            const categories = ['esim_realtime', 'esim_addon', 'esim_replacement'];

            for (const provider of providers) {
                try {
                    console.log(`[Cron] Processing provider: ${provider.name}`);

                    // Check memory before processing each provider
                    if (memoryMonitor.isMemoryWarning()) {
                        console.log(`[Cron] ⚠️ Memory warning detected before processing ${provider.name}`);
                        memoryMonitor.forceGarbageCollection();

                        // Wait a bit for GC to complete
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }

                    const providerService = providerFactory.getProvider(provider.name);

                    // Fetch plans for each category
                    for (const category of categories) {
                        try {
                            console.log(`[Cron] Fetching ${category} plans from ${provider.name}...`);

                            // Use getProductsWithPrices for BillionConnect to ensure prices are fetched
                            const externalPlans = provider.name.toLowerCase() === 'billionconnect'
                                ? await providerService.getProductsWithPrices()
                                : await providerService.getProducts(category);

                            if (!Array.isArray(externalPlans)) {
                                throw new Error(`Invalid response from provider ${provider.name} for category ${category}`);
                            }

                            console.log(`[Cron] Found ${externalPlans.length} ${category} plans from ${provider.name}`);

                            // Process plans in smaller batches to prevent memory overload
                            const batchSize = memoryMonitor.getRecommendedBatchSize();

                            for (let i = 0; i < externalPlans.length; i += batchSize) {
                                const batch = externalPlans.slice(i, i + batchSize);
                                console.log(`[Cron] Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(externalPlans.length / batchSize)} (${batch.length} plans)`);

                                for (const externalPlan of batch) {
                                    try {
                                        results.total++;
                                        const standardizedPlan = await providerFactory.standardizeProduct(provider.name, externalPlan);

                                        // Check if plan already exists - prioritize externalProductId for exact matching
                                        let plan = await EsimPlan.findOne({
                                            where: {
                                                externalProductId: standardizedPlan.externalProductId
                                            }
                                        });

                                        // If not found by externalProductId, try externalSkuId as fallback
                                        // but only if externalProductId is not available
                                        if (!plan && !standardizedPlan.externalProductId) {
                                            plan = await EsimPlan.findOne({
                                                where: {
                                                    externalSkuId: standardizedPlan.externalSkuId
                                                }
                                            });
                                        }

                                        if (plan) {
                                            // Update existing plan while preserving manually set fields
                                            const fieldsToPreserve = {
                                                sellingPrice: plan.sellingPrice,
                                                status: plan.status,
                                                stockThreshold: plan.stockThreshold,
                                                startDateEnabled: plan.startDateEnabled,
                                                features: plan.features,
                                                instructions: plan.instructions,
                                                description: plan.description,
                                                activationPolicy: plan.activationPolicy,
                                                hotspot: plan.hotspot,
                                                speed: plan.speed,
                                                name: plan.name
                                            };

                                            // Update plan with new data while preserving manual fields
                                            await plan.update({
                                                ...standardizedPlan,
                                                ...fieldsToPreserve,
                                                providerId: provider.id,
                                                updatedAt: new Date()
                                            });
                                            results.updated++;
                                        } else {
                                            // Create new plan
                                            plan = await EsimPlan.create({
                                                ...standardizedPlan,
                                                id: uuidv4(),
                                                providerId: provider.id,
                                                status: 'visible'
                                            });
                                            results.created++;
                                        }

                                        // Handle country associations for all plans (both updated and created)
                                        const supportedCountries = standardizedPlan.supportedCountries || [];

                                        // Clear existing country associations
                                        await plan.setCountries([]);

                                        // Add new country associations
                                        if (Array.isArray(supportedCountries) && supportedCountries.length > 0) {
                                            const countryAssociations = [];
                                            supportedCountries.forEach(countryCode => {
                                                const country = countryMap.get(countryCode.toLowerCase());
                                                if (country) {
                                                    countryAssociations.push({
                                                        countryId: country.id,
                                                        isDefault: countryAssociations.length === 0
                                                    });
                                                }
                                            });

                                            // Add all countries at once
                                            if (countryAssociations.length > 0) {
                                                await plan.addCountries(
                                                    countryAssociations.map(assoc => assoc.countryId),
                                                    {
                                                        through: countryAssociations.map(assoc => ({
                                                            isDefault: assoc.isDefault,
                                                            id: uuidv4()
                                                        }))
                                                    }
                                                );
                                            }
                                        }
                                    } catch (error) {
                                        console.error(`[Cron] Error processing plan from ${provider.name}:`, error);
                                        results.failed++;
                                        results.errors.push({
                                            provider: provider.name,
                                            planId: externalPlan.id,
                                            category: category,
                                            error: error.message
                                        });
                                    }
                                }

                                // Force GC between batches if memory is getting high
                                if (memoryMonitor.isMemoryWarning()) {
                                    console.log(`[Cron] 🗑️ Forcing GC after batch ${Math.floor(i / batchSize) + 1}`);
                                    memoryMonitor.forceGarbageCollection();
                                }

                                // Small delay between batches
                                if (i + batchSize < externalPlans.length) {
                                    await new Promise(resolve => setTimeout(resolve, 200));
                                }
                            }
                        } catch (error) {
                            console.error(`[Cron] Error fetching ${category} plans from ${provider.name}:`, error);
                            results.errors.push({
                                provider: provider.name,
                                category,
                                error: error.message
                            });
                        }
                    }
                } catch (error) {
                    console.error(`[Cron] Error processing provider ${provider.name}:`, error);
                    results.errors.push({
                        provider: provider.name,
                        error: error.message
                    });
                }
            }

            console.log('[Cron] Plan sync completed:', results);
            memoryMonitor.logMemoryStatus();

            // Final GC after sync completion
            if (memoryMonitor.isMemoryWarning()) {
                console.log('[Cron] 🗑️ Final garbage collection after sync completion');
                memoryMonitor.forceGarbageCollection();
            }

            return results;
        } catch (error) {
            console.error('[Cron] Error in automatic plan sync:', error);
            throw error;
        }
    }

    startJobs() {
        this.planSyncJob.start();
        console.log('Cron jobs started');
    }

    stopJobs() {
        this.planSyncJob.stop();
        console.log('Cron jobs stopped');
    }
}

module.exports = new CronService(); 