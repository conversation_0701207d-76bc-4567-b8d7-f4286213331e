#!/usr/bin/env node

/**
 * <PERSON>ript to optimize database indexes for partner plans performance
 * Run this script to apply performance optimizations for the /api/esim-plans/partner endpoint
 */

const fs = require('fs');
const path = require('path');
const sequelize = require('../src/config/database');

async function runOptimization() {
    try {
        console.log('🚀 Starting partner plans performance optimization...');
        
        // Read the SQL migration file
        const migrationPath = path.join(__dirname, '../src/migrations/20250715_optimize_partner_plans_performance.sql');
        const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
        
        // Split SQL statements (handle multiple statements)
        const statements = migrationSQL
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
        
        console.log(`📝 Found ${statements.length} SQL statements to execute`);
        
        // Execute each statement
        for (let i = 0; i < statements.length; i++) {
            const statement = statements[i];
            if (statement.trim()) {
                try {
                    console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`);
                    await sequelize.query(statement);
                    console.log(`✅ Statement ${i + 1} completed successfully`);
                } catch (error) {
                    // Some statements might fail if indexes already exist, that's okay
                    if (error.message.includes('Duplicate key name') || 
                        error.message.includes('already exists') ||
                        error.message.includes("Can't DROP")) {
                        console.log(`⚠️  Statement ${i + 1} skipped (already exists): ${error.message.split('\n')[0]}`);
                    } else {
                        console.error(`❌ Error in statement ${i + 1}:`, error.message);
                        // Continue with other statements
                    }
                }
            }
        }
        
        console.log('🎉 Partner plans optimization completed!');
        console.log('📊 Performance improvements applied:');
        console.log('   • Optimized indexes for status + isActive + category queries');
        console.log('   • Enhanced search performance for name and networkName');
        console.log('   • Improved country filtering through junction table');
        console.log('   • Better region filtering performance');
        console.log('   • Optimized pagination with composite indexes');
        console.log('   • Enhanced provider status filtering');
        console.log('   • Updated table statistics for query optimizer');
        
        // Test the optimization by running a sample query
        console.log('\n🔍 Testing optimization with sample query...');
        const startTime = Date.now();
        
        const testResult = await sequelize.query(`
            SELECT COUNT(*) as count 
            FROM esimplans e 
            INNER JOIN providers p ON e.providerId = p.id 
            WHERE e.status = 'visible' 
            AND e.isActive = true 
            AND e.category = 'esim_realtime' 
            AND p.status = 'active'
        `, { type: sequelize.QueryTypes.SELECT });
        
        const duration = Date.now() - startTime;
        console.log(`✅ Test query completed in ${duration}ms`);
        console.log(`📈 Found ${testResult[0].count} active plans`);
        
        if (duration < 100) {
            console.log('🚀 Excellent performance! Query completed in under 100ms');
        } else if (duration < 500) {
            console.log('✅ Good performance! Query completed in under 500ms');
        } else {
            console.log('⚠️  Performance could be better. Consider checking your database configuration.');
        }
        
    } catch (error) {
        console.error('❌ Optimization failed:', error);
        process.exit(1);
    } finally {
        await sequelize.close();
    }
}

// Run the optimization
if (require.main === module) {
    runOptimization()
        .then(() => {
            console.log('\n✨ All done! Your partner plans endpoint should now be much faster.');
            process.exit(0);
        })
        .catch((error) => {
            console.error('💥 Fatal error:', error);
            process.exit(1);
        });
}

module.exports = { runOptimization };
