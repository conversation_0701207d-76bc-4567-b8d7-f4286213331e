#!/usr/bin/env node

/**
 * Memory Monitoring Script
 * Standalone script to monitor memory usage of the eSIM application
 * Usage: node scripts/memory-monitor.js [options]
 */

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

class MemoryMonitorScript {
    constructor() {
        this.logFile = path.join(__dirname, '../logs/memory-monitor.log');
        this.alertThreshold = 80; // Alert when memory usage exceeds 80%
        this.checkInterval = 5000; // Check every 5 seconds
        this.isRunning = false;
        this.processName = 'node'; // Process name to monitor
        this.maxLogSize = 10 * 1024 * 1024; // 10MB max log file size
    }

    /**
     * Ensure log directory exists
     */
    ensureLogDirectory() {
        const logDir = path.dirname(this.logFile);
        if (!fs.existsSync(logDir)) {
            fs.mkdirSync(logDir, { recursive: true });
        }
    }

    /**
     * Rotate log file if it gets too large
     */
    rotateLogIfNeeded() {
        try {
            if (fs.existsSync(this.logFile)) {
                const stats = fs.statSync(this.logFile);
                if (stats.size > this.maxLogSize) {
                    const backupFile = this.logFile + '.old';
                    fs.renameSync(this.logFile, backupFile);
                    console.log(`Log file rotated: ${backupFile}`);
                }
            }
        } catch (error) {
            console.error('Error rotating log file:', error);
        }
    }

    /**
     * Log message to both console and file
     */
    log(message) {
        const timestamp = new Date().toISOString();
        const logMessage = `${timestamp} - ${message}\n`;
        
        console.log(message);
        
        try {
            fs.appendFileSync(this.logFile, logMessage);
        } catch (error) {
            console.error('Error writing to log file:', error);
        }
    }

    /**
     * Get memory usage of Node.js processes
     */
    async getNodeProcessMemory() {
        return new Promise((resolve, reject) => {
            // Get all node processes with memory info
            exec('ps aux | grep node | grep -v grep', (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                    return;
                }

                const processes = [];
                const lines = stdout.trim().split('\n');

                for (const line of lines) {
                    const parts = line.trim().split(/\s+/);
                    if (parts.length >= 11) {
                        const pid = parts[1];
                        const cpu = parseFloat(parts[2]);
                        const memory = parseFloat(parts[3]);
                        const vsz = parseInt(parts[4]); // Virtual memory size in KB
                        const rss = parseInt(parts[5]); // Resident set size in KB
                        const command = parts.slice(10).join(' ');

                        // Filter for our application (contains app.js)
                        if (command.includes('app.js')) {
                            processes.push({
                                pid,
                                cpu,
                                memory,
                                vsz: Math.round(vsz / 1024), // Convert to MB
                                rss: Math.round(rss / 1024), // Convert to MB
                                command
                            });
                        }
                    }
                }

                resolve(processes);
            });
        });
    }

    /**
     * Get system memory information
     */
    async getSystemMemory() {
        return new Promise((resolve, reject) => {
            exec('free -m', (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                    return;
                }

                const lines = stdout.trim().split('\n');
                const memLine = lines[1].split(/\s+/);
                
                resolve({
                    total: parseInt(memLine[1]),
                    used: parseInt(memLine[2]),
                    free: parseInt(memLine[3]),
                    available: parseInt(memLine[6]) || parseInt(memLine[3]),
                    usagePercent: Math.round((parseInt(memLine[2]) / parseInt(memLine[1])) * 100)
                });
            });
        });
    }

    /**
     * Check memory usage and alert if necessary
     */
    async checkMemory() {
        try {
            const [nodeProcesses, systemMemory] = await Promise.all([
                this.getNodeProcessMemory(),
                this.getSystemMemory()
            ]);

            // Log system memory
            this.log(`System Memory: ${systemMemory.used}MB/${systemMemory.total}MB (${systemMemory.usagePercent}%)`);

            // Log Node.js processes
            for (const process of nodeProcesses) {
                const message = `Node Process PID ${process.pid}: CPU ${process.cpu}%, Memory ${process.memory}%, RSS ${process.rss}MB, VSZ ${process.vsz}MB`;
                this.log(message);

                // Alert if memory usage is high
                if (process.memory > this.alertThreshold) {
                    this.log(`🚨 ALERT: High memory usage detected! Process ${process.pid} using ${process.memory}% of system memory`);
                    this.log(`   RSS: ${process.rss}MB, VSZ: ${process.vsz}MB`);
                    this.log(`   Command: ${process.command}`);
                }
            }

            // Alert if system memory is high
            if (systemMemory.usagePercent > this.alertThreshold) {
                this.log(`🚨 SYSTEM ALERT: High system memory usage ${systemMemory.usagePercent}%`);
            }

            this.log('---');

        } catch (error) {
            this.log(`Error checking memory: ${error.message}`);
        }
    }

    /**
     * Start monitoring
     */
    start() {
        if (this.isRunning) {
            console.log('Memory monitor is already running');
            return;
        }

        this.ensureLogDirectory();
        this.rotateLogIfNeeded();
        
        this.isRunning = true;
        this.log('Memory monitoring started');
        this.log(`Alert threshold: ${this.alertThreshold}%`);
        this.log(`Check interval: ${this.checkInterval}ms`);
        this.log(`Log file: ${this.logFile}`);

        // Initial check
        this.checkMemory();

        // Set up interval
        this.interval = setInterval(() => {
            this.checkMemory();
        }, this.checkInterval);

        // Handle graceful shutdown
        process.on('SIGINT', () => {
            this.stop();
            process.exit(0);
        });

        process.on('SIGTERM', () => {
            this.stop();
            process.exit(0);
        });
    }

    /**
     * Stop monitoring
     */
    stop() {
        if (!this.isRunning) {
            return;
        }

        this.isRunning = false;
        if (this.interval) {
            clearInterval(this.interval);
        }
        this.log('Memory monitoring stopped');
    }

    /**
     * Show current status
     */
    async status() {
        try {
            const [nodeProcesses, systemMemory] = await Promise.all([
                this.getNodeProcessMemory(),
                this.getSystemMemory()
            ]);

            console.log('\n=== Memory Status ===');
            console.log(`System Memory: ${systemMemory.used}MB/${systemMemory.total}MB (${systemMemory.usagePercent}%)`);
            console.log(`Available: ${systemMemory.available}MB`);
            
            console.log('\n=== Node.js Processes ===');
            if (nodeProcesses.length === 0) {
                console.log('No Node.js processes found running app.js');
            } else {
                for (const process of nodeProcesses) {
                    console.log(`PID ${process.pid}: CPU ${process.cpu}%, Memory ${process.memory}%, RSS ${process.rss}MB`);
                    console.log(`  Command: ${process.command}`);
                }
            }
            console.log('');

        } catch (error) {
            console.error('Error getting status:', error.message);
        }
    }
}

// Main execution
if (require.main === module) {
    const monitor = new MemoryMonitorScript();
    const args = process.argv.slice(2);
    const command = args[0] || 'start';

    switch (command) {
        case 'start':
            monitor.start();
            break;
        case 'status':
            monitor.status();
            break;
        case 'help':
            console.log('Usage: node scripts/memory-monitor.js [command]');
            console.log('Commands:');
            console.log('  start   - Start memory monitoring (default)');
            console.log('  status  - Show current memory status');
            console.log('  help    - Show this help message');
            break;
        default:
            console.log(`Unknown command: ${command}`);
            console.log('Use "help" for available commands');
            break;
    }
}

module.exports = MemoryMonitorScript;
